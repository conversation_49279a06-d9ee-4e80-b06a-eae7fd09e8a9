# Multi-stage Dockerfile for Azure variant
# Optimized for Azure Container Instances

# Build stage
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    unixodbc-dev \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements/azure.txt /tmp/requirements.txt
RUN pip install --upgrade pip && \
    pip install -r /tmp/requirements.txt

# Production stage
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    CLOUD_VARIANT=azure \
    AI_ENGINE=azure_openai

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    unixodbc \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Create app user
RUN groupadd -r omnify && useradd -r -g omnify omnify

# Create app directory
WORKDIR /app

# Copy application code
COPY --chown=omnify:omnify . .

# Copy Azure-specific configuration
COPY --chown=omnify:omnify config/azure/ config/
COPY --chown=omnify:omnify docker/azure/entrypoint.sh /entrypoint.sh

# Make entrypoint executable
RUN chmod +x /entrypoint.sh

# Create directories for models and logs
RUN mkdir -p /opt/models /app/logs && \
    chown -R omnify:omnify /opt/models /app/logs

# Switch to app user
USER omnify

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Set entrypoint
ENTRYPOINT ["/entrypoint.sh"]

# Default command
CMD ["uvicorn", "apps.core.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
