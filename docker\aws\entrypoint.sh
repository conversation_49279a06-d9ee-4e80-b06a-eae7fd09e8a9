#!/bin/bash
set -e

# AWS-specific entrypoint script for Omnify Marketing Cloud
echo "🚀 Starting Omnify Marketing Cloud - AWS Variant"

# Set AWS-specific environment variables
export CLOUD_PROVIDER="aws"
export AI_ENGINE_PRIMARY="manus_rl"
export AI_ENGINE_FALLBACK="aws_bedrock"
export DATABASE_TYPE="aurora_postgresql"
export CACHE_TYPE="elasticache_redis"

# AWS region detection
if [ -z "$AWS_REGION" ]; then
    export AWS_REGION=$(curl -s http://***************/latest/meta-data/placement/region 2>/dev/null || echo "us-east-1")
fi

echo "📍 AWS Region: $AWS_REGION"

# Wait for database to be ready
echo "⏳ Waiting for Aurora PostgreSQL to be ready..."
python -c "
import time
import psycopg2
import os
from urllib.parse import urlparse

db_url = os.environ.get('DATABASE_URL')
if db_url:
    parsed = urlparse(db_url)
    max_retries = 30
    for i in range(max_retries):
        try:
            conn = psycopg2.connect(
                host=parsed.hostname,
                port=parsed.port or 5432,
                user=parsed.username,
                password=parsed.password,
                database=parsed.path[1:] if parsed.path else 'omnify'
            )
            conn.close()
            print('✅ Database connection successful')
            break
        except Exception as e:
            if i == max_retries - 1:
                print(f'❌ Database connection failed after {max_retries} retries: {e}')
                exit(1)
            print(f'⏳ Database not ready, retrying in 5 seconds... ({i+1}/{max_retries})')
            time.sleep(5)
"

# Wait for Redis to be ready
echo "⏳ Waiting for ElastiCache Redis to be ready..."
python -c "
import time
import redis
import os

redis_url = os.environ.get('REDIS_URL')
if redis_url:
    max_retries = 30
    for i in range(max_retries):
        try:
            r = redis.from_url(redis_url)
            r.ping()
            print('✅ Redis connection successful')
            break
        except Exception as e:
            if i == max_retries - 1:
                print(f'❌ Redis connection failed after {max_retries} retries: {e}')
                exit(1)
            print(f'⏳ Redis not ready, retrying in 5 seconds... ({i+1}/{max_retries})')
            time.sleep(5)
"

# Run database migrations
echo "🔄 Running database migrations..."
alembic upgrade head

# Initialize Manus RL model if not exists
echo "🤖 Initializing Manus RL model..."
if [ ! -f "/opt/models/manus_rl_v2/model.pkl" ]; then
    echo "📥 Downloading Manus RL model..."
    python scripts/download_manus_model.py --variant aws --output /opt/models/manus_rl_v2/
fi

# Test AWS Bedrock connectivity
echo "🧠 Testing AWS Bedrock connectivity..."
python -c "
import boto3
import json
try:
    bedrock = boto3.client('bedrock-runtime', region_name='$AWS_REGION')
    # Test with a simple prompt
    response = bedrock.invoke_model(
        modelId='anthropic.claude-3-haiku-20240307-v1:0',
        contentType='application/json',
        accept='application/json',
        body=json.dumps({
            'prompt': '\n\nHuman: Hello\n\nAssistant:',
            'max_tokens_to_sample': 10
        })
    )
    print('✅ AWS Bedrock connection successful')
except Exception as e:
    print(f'⚠️ AWS Bedrock connection failed: {e}')
    print('🔄 Will use Manus RL only mode')
"

# Start background workers
echo "🔧 Starting background workers..."
celery -A apps.core.celery worker --loglevel=info --detach

# Start the application
echo "🎯 Starting Omnify Marketing Cloud API server..."
echo "🌐 Cloud Variant: AWS-Manus Hybrid"
echo "🤖 AI Engine: Manus RL + AWS Bedrock"
echo "💾 Database: Aurora PostgreSQL Serverless v2"
echo "⚡ Cache: ElastiCache Redis"
echo "🏗️ Compute: ECS Fargate with Graviton3"

exec "$@"
