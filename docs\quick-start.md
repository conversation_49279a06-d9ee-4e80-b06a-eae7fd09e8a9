# 🏁 Quick Start Guide - Omnify Marketing Cloud

Get your Omnify Marketing Cloud MVP up and running in 15 minutes! Choose from 5 cloud deployment variants or use our enhanced setup wizard for AI-powered recommendations.

## 📋 Prerequisites

### **System Requirements**
- **Python**: 3.11 or higher
- **Node.js**: 18 or higher
- **Docker**: Latest version
- **PostgreSQL**: 15 or higher
- **Redis**: 7 or higher

### **API Keys Required**
- **OpenAI API Key**: For AI decision making
- **Google Ads API**: For campaign integration (optional for demo)
- **Meta Ads API**: For Facebook/Instagram ads (optional for demo)
- **SendGrid API Key**: For email automation (optional for demo)

## 🧙‍♂️ **Enhanced Setup Wizard (Recommended)**

For the best experience, use our AI-powered setup wizard that analyzes your business needs and recommends the optimal cloud deployment:

```bash
# Clone the repository
git clone <your-repository-url>
cd omnify-marketing-cloud

# Run the enhanced setup wizard
python scripts/enhanced_setup_wizard.py
```

The wizard will:
1. **Analyze your business profile** (company size, industry, budget)
2. **Generate AI-powered cloud recommendations** (AWS, Azure, GCP, Multi-Cloud, Open Source)
3. **Deploy your chosen variant** with one command
4. **Configure integrations** and validate deployment
5. **Provide next steps** for immediate use

**Estimated Time**: 15-30 minutes (including deployment)

---

## 🚀 **Manual 15-Minute Setup (Local Development)**

For local development and testing, follow these steps:

### **Step 1: Clone and Setup Environment (2 minutes)**

```bash
# Clone the repository
git clone <your-repository-url>
cd omnify-marketing-cloud

# Create environment file
cp .env.example .env

# Edit .env with your configuration
nano .env
```

**Required Environment Variables:**
```bash
# Database
DATABASE_URL=postgresql://omnify:password@localhost:5432/omnify_dev
REDIS_URL=redis://localhost:6379/0

# AI Services
OPENAI_API_KEY=your_openai_api_key_here
MANUS_RL_API_KEY=your_manus_rl_key_here

# Security
SECRET_KEY=your_super_secret_key_here
ENVIRONMENT=development

# Frontend
FRONTEND_URL=http://localhost:3000
```

### **Step 2: Start Infrastructure Services (3 minutes)**

```bash
# Start PostgreSQL and Redis with Docker
docker-compose up -d postgres redis

# Wait for services to be ready
sleep 30

# Verify services are running
docker-compose ps
```

### **Step 3: Setup Backend (5 minutes)**

```bash
# Install Python dependencies
pip install -r requirements.txt

# Run database migrations
alembic upgrade head

# Create demo data
python scripts/setup_demo_data.py

# Start the API server
uvicorn apps.core.main:app --reload --host 0.0.0.0 --port 8000
```

**Expected Output:**
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process
INFO:     Started server process
```

### **Step 4: Setup Frontend (3 minutes)**

```bash
# Open new terminal
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

**Expected Output:**
```
▲ Next.js 14.0.0
- Local:        http://localhost:3000
- Ready in 2.1s
```

### **Step 5: Verify Installation (2 minutes)**

```bash
# Test API health
curl http://localhost:8000/health

# Test frontend
open http://localhost:3000

# Run quick validation
python scripts/validate_mvp.py
```

## 🎯 **Demo Login Credentials**

After running `setup_demo_data.py`, use these credentials:

### **Demo Company 1: TechStart Solutions**
- **Email**: `<EMAIL>`
- **Password**: `Demo123!`
- **Features**: 5 campaigns, 500 customers, 90 days of data

### **Demo Company 2: EcoFriendly Goods**
- **Email**: `<EMAIL>`
- **Password**: `Demo123!`
- **Features**: 3 campaigns, 300 customers, e-commerce focus

### **Demo Company 3: HealthPlus Clinic**
- **Email**: `<EMAIL>`
- **Password**: `Demo123!`
- **Features**: 4 campaigns, 800 customers, healthcare vertical

## 🎮 **First Steps in the Dashboard**

### **1. Login and Explore**
1. Go to http://localhost:3000
2. Login with demo credentials
3. Explore the dashboard overview

### **2. Check AI Agents Status**
- **ROI Engine X™**: Should show "Active" status
- **Retention Reactor Pro™**: Should show recent customer analysis
- **EngageSense Ultra™**: Should show segment updates

### **3. Try the Smart Integration Wizard**
- Navigate to Integrations section
- See AI-powered recommendations
- Start a sample integration wizard
- Experience step-by-step guidance

### **4. View Campaign Performance**
- Navigate to Campaigns section
- See realistic performance data
- Check AI optimization recommendations

### **5. Test Real-time Features**
- Open browser developer tools
- Go to Network tab and filter for WebSocket
- See real-time updates every 30 seconds

## 🧪 **Validate Your Setup**

### **Run Complete Validation**
```bash
# Comprehensive MVP validation
python scripts/complete_mvp_validation.py
```

**Expected Results:**
```
✅ Enhanced Authentication: Registration, login, and token refresh working
✅ Smart Integration Wizard: AI recommendations and step guidance working
✅ WebSocket Connectivity: Connection and ping/pong working
✅ Advanced Campaign Management: All advanced features accessible
✅ Billing System: All billing endpoints working
✅ Complete Dashboard APIs: All dashboard endpoints working

📊 Complete MVP Validation Summary
Total Tests: 6
Passed: 6
Failed: 0
Success Rate: 100.0%

🎉 MVP is PRODUCTION READY!
```

## 🔧 **Common Issues & Solutions**

### **Database Connection Issues**
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Reset database if needed
docker-compose down postgres
docker-compose up -d postgres
sleep 30
alembic upgrade head
```

### **Redis Connection Issues**
```bash
# Check Redis status
docker-compose ps redis

# Test Redis connection
redis-cli ping
```

### **Frontend Build Issues**
```bash
# Clear cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run dev
```

### **API Key Issues**
```bash
# Verify environment variables
python -c "from apps.core.config import settings; print(f'OpenAI: {bool(settings.OPENAI_API_KEY)}')"

# Test OpenAI connection
python -c "import openai; openai.api_key='your_key'; print('OpenAI connected')"
```

## 🎯 **Next Steps**

### **Development**
1. **Explore the Code**: Check out the [Architecture Guide](architecture.md)
2. **Run Tests**: Follow the [Testing Guide](#validate-your-setup)
3. **Customize Features**: See [Configuration Guide](#step-1-clone-and-setup-environment-2-minutes)

### **Multi-Cloud Production Deployment**
1. **Choose Cloud Variant**: Use enhanced setup wizard or manual deployment
2. **AWS Deployment**: `python scripts/universal_deploy.py deploy --variant aws --environment production`
3. **Azure Deployment**: `python scripts/universal_deploy.py deploy --variant azure --environment production`
4. **GCP Deployment**: `python scripts/universal_deploy.py deploy --variant gcp --environment production`
5. **Multi-Cloud**: `python scripts/universal_deploy.py deploy --variant multi --environment production`
6. **Open Source**: `python scripts/universal_deploy.py deploy --variant oss --environment production`

### **Traditional Production Deployment**
1. **Setup AWS**: Follow [Deployment Guide](DEPLOYMENT_GUIDE.md)
2. **Configure Monitoring**: Setup [Monitoring & Alerts](architecture.md#monitoring--observability)
3. **Security Setup**: Review [Security Guide](architecture.md#security-architecture)

### **Business Launch**
1. **Setup Billing**: Configure [Stripe Integration](api-reference.md#billing--subscription-apis)
2. **Client Onboarding**: Review [Customer Success](#demo-login-credentials)
3. **Go-to-Market**: Check [Launch Strategy](mvp-status.md#next-steps-for-market-launch)

## 📞 **Need Help?**

### **Technical Issues**
- Check the [Troubleshooting Guide](#common-issues--solutions)
- Review [Common Issues](#common-issues--solutions)
- Email: <EMAIL>

### **Business Questions**
- Review [MVP Status](mvp-status.md)
- Schedule demo: https://omnify.com/demo
- Email: <EMAIL>

---

**🎉 Congratulations! Your Omnify Marketing Cloud MVP is now running!**

**Ready to see AI-driven marketing automation in action? Start exploring the dashboard and watch the AI agents optimize your campaigns in real-time!**

[📊 **Next: Explore the API Reference →**](api-reference.md)
