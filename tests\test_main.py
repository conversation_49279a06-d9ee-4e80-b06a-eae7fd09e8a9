"""
Basic tests for Omnify Marketing Cloud API
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
import asyncio

from apps.core.main import app
from apps.core.database import get_db, Base

# Test database URL (SQLite in memory for testing)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

async def override_get_db():
    """Override database dependency for testing"""
    async with TestingSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def setup_database():
    """Set up test database"""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest.fixture
def client():
    """Create test client"""
    with TestClient(app) as c:
        yield c

def test_root_endpoint(client):
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Omnify Marketing Cloud API"
    assert data["version"] == "1.0.0"
    assert "status" in data

def test_health_endpoint(client):
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_docs_endpoint(client):
    """Test API documentation endpoint"""
    response = client.get("/docs")
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_database_connection(setup_database):
    """Test database connection"""
    async with TestingSessionLocal() as session:
        # Simple query to test connection
        result = await session.execute("SELECT 1")
        assert result.scalar() == 1

def test_cors_headers(client):
    """Test CORS headers are present"""
    response = client.options("/")
    assert "access-control-allow-origin" in response.headers

def test_rate_limiting_headers(client):
    """Test rate limiting headers"""
    response = client.get("/")
    assert "x-ratelimit-limit" in response.headers
    assert "x-ratelimit-remaining" in response.headers

def test_security_headers(client):
    """Test security headers"""
    response = client.get("/")
    assert response.headers.get("x-content-type-options") == "nosniff"
    assert response.headers.get("x-frame-options") == "DENY"
    assert "x-request-id" in response.headers
