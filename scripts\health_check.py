#!/usr/bin/env python3
"""
Comprehensive Health Check Script for Multi-Cloud Omnify Deployment
Tests all critical components across cloud variants
"""
import asyncio
import argparse
import json
import sys
import time
from typing import Dict, Any, List
import httpx
import structlog

logger = structlog.get_logger()

class MultiCloudHealthChecker:
    """Comprehensive health checker for all cloud variants"""
    
    def __init__(self, variant: str, environment: str):
        self.variant = variant
        self.environment = environment
        self.endpoints = self._get_endpoints()
        self.health_results = {}
    
    def _get_endpoints(self) -> Dict[str, str]:
        """Get endpoints for the specific variant and environment"""
        # These would typically be loaded from deployment outputs
        base_endpoints = {
            "aws": {
                "api": f"https://api-{self.environment}.omnify.aws.com",
                "dashboard": f"https://app-{self.environment}.omnify.aws.com",
                "health": f"https://api-{self.environment}.omnify.aws.com/health"
            },
            "azure": {
                "api": f"https://omnify-{self.environment}.azurecontainer.io:8000",
                "dashboard": f"https://omnify-{self.environment}.azurecontainer.io:8000",
                "health": f"https://omnify-{self.environment}.azurecontainer.io:8000/health"
            },
            "gcp": {
                "api": f"https://omnify-api-{self.environment}-hash.a.run.app",
                "dashboard": f"https://omnify-api-{self.environment}-hash.a.run.app",
                "health": f"https://omnify-api-{self.environment}-hash.a.run.app/health"
            },
            "multi": {
                "api": "https://api.omnify.com",
                "dashboard": "https://app.omnify.com",
                "health": "https://api.omnify.com/health"
            },
            "oss": {
                "api": f"https://omnify-{self.environment}.local:8000",
                "dashboard": f"https://omnify-{self.environment}.local:3000",
                "health": f"https://omnify-{self.environment}.local:8000/health"
            }
        }
        
        return base_endpoints.get(self.variant, {})
    
    async def run_comprehensive_health_check(self) -> Dict[str, Any]:
        """Run all health checks"""
        logger.info(
            "Starting comprehensive health check",
            variant=self.variant,
            environment=self.environment
        )
        
        start_time = time.time()
        
        # Run all health check categories
        checks = [
            ("API Health", self._check_api_health),
            ("Database Connectivity", self._check_database_health),
            ("Cache Connectivity", self._check_cache_health),
            ("AI Engine Status", self._check_ai_engine_health),
            ("Authentication System", self._check_auth_health),
            ("Integration Endpoints", self._check_integration_health),
            ("Performance Metrics", self._check_performance_metrics),
            ("Security Configuration", self._check_security_config)
        ]
        
        results = {}
        overall_status = "healthy"
        
        for check_name, check_function in checks:
            try:
                logger.info(f"Running {check_name} check")
                result = await check_function()
                results[check_name] = result
                
                if result.get("status") != "healthy":
                    overall_status = "degraded"
                    
            except Exception as e:
                logger.error(f"{check_name} check failed", error=str(e))
                results[check_name] = {
                    "status": "error",
                    "error": str(e),
                    "timestamp": time.time()
                }
                overall_status = "unhealthy"
        
        end_time = time.time()
        
        # Generate comprehensive report
        report = {
            "overall_status": overall_status,
            "variant": self.variant,
            "environment": self.environment,
            "check_duration": round(end_time - start_time, 2),
            "timestamp": time.time(),
            "checks": results,
            "summary": self._generate_summary(results)
        }
        
        logger.info(
            "Health check completed",
            overall_status=overall_status,
            duration=report["check_duration"]
        )
        
        return report
    
    async def _check_api_health(self) -> Dict[str, Any]:
        """Check API health and responsiveness"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Basic health endpoint
                health_url = self.endpoints.get("health")
                if not health_url:
                    return {
                        "status": "error",
                        "error": "Health endpoint not configured"
                    }
                
                start_time = time.time()
                response = await client.get(health_url)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    health_data = response.json()
                    
                    # Check API version and status
                    api_status = health_data.get("status", "unknown")
                    api_version = health_data.get("version", "unknown")
                    
                    return {
                        "status": "healthy" if api_status == "ok" else "degraded",
                        "response_time": round(response_time, 3),
                        "api_version": api_version,
                        "api_status": api_status,
                        "details": health_data
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "status_code": response.status_code,
                        "response_time": round(response_time, 3),
                        "error": response.text
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Database health endpoint
                db_health_url = f"{self.endpoints.get('api', '')}/health/database"
                
                start_time = time.time()
                response = await client.get(db_health_url)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    db_data = response.json()
                    
                    return {
                        "status": "healthy" if db_data.get("connected") else "unhealthy",
                        "response_time": round(response_time, 3),
                        "connection_pool": db_data.get("connection_pool", {}),
                        "query_performance": db_data.get("query_performance", {}),
                        "variant_specific": self._get_db_variant_info()
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "status_code": response.status_code,
                        "error": "Database health endpoint failed"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _check_cache_health(self) -> Dict[str, Any]:
        """Check cache connectivity and performance"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Cache health endpoint
                cache_health_url = f"{self.endpoints.get('api', '')}/health/cache"
                
                response = await client.get(cache_health_url)
                
                if response.status_code == 200:
                    cache_data = response.json()
                    
                    return {
                        "status": "healthy" if cache_data.get("connected") else "unhealthy",
                        "memory_usage": cache_data.get("memory_usage", {}),
                        "hit_rate": cache_data.get("hit_rate", 0),
                        "variant_specific": self._get_cache_variant_info()
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": "Cache health endpoint failed"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _check_ai_engine_health(self) -> Dict[str, Any]:
        """Check AI engine status and performance"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # AI engine health endpoint
                ai_health_url = f"{self.endpoints.get('api', '')}/health/ai-engine"
                
                response = await client.get(ai_health_url)
                
                if response.status_code == 200:
                    ai_data = response.json()
                    
                    return {
                        "status": "healthy" if ai_data.get("status") == "active" else "degraded",
                        "engine_type": ai_data.get("engine_type"),
                        "model_status": ai_data.get("model_status", {}),
                        "performance_metrics": ai_data.get("performance_metrics", {}),
                        "variant_specific": self._get_ai_variant_info()
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": "AI engine health endpoint failed"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _check_auth_health(self) -> Dict[str, Any]:
        """Check authentication system health"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Auth health endpoint
                auth_health_url = f"{self.endpoints.get('api', '')}/health/auth"
                
                response = await client.get(auth_health_url)
                
                if response.status_code == 200:
                    auth_data = response.json()
                    
                    return {
                        "status": "healthy" if auth_data.get("jwt_valid") else "degraded",
                        "token_validation": auth_data.get("jwt_valid", False),
                        "refresh_mechanism": auth_data.get("refresh_available", False),
                        "session_management": auth_data.get("session_active", False)
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": "Auth health endpoint failed"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _check_integration_health(self) -> Dict[str, Any]:
        """Check integration endpoints health"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Integration health endpoint
                integration_health_url = f"{self.endpoints.get('api', '')}/health/integrations"
                
                response = await client.get(integration_health_url)
                
                if response.status_code == 200:
                    integration_data = response.json()
                    
                    return {
                        "status": "healthy" if integration_data.get("all_healthy") else "degraded",
                        "active_integrations": integration_data.get("active_count", 0),
                        "failed_integrations": integration_data.get("failed_count", 0),
                        "integration_details": integration_data.get("details", {})
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": "Integration health endpoint failed"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _check_performance_metrics(self) -> Dict[str, Any]:
        """Check system performance metrics"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Performance metrics endpoint
                metrics_url = f"{self.endpoints.get('api', '')}/health/metrics"
                
                response = await client.get(metrics_url)
                
                if response.status_code == 200:
                    metrics_data = response.json()
                    
                    cpu_usage = metrics_data.get("cpu_usage", 0)
                    memory_usage = metrics_data.get("memory_usage", 0)
                    
                    status = "healthy"
                    if cpu_usage > 80 or memory_usage > 85:
                        status = "degraded"
                    if cpu_usage > 95 or memory_usage > 95:
                        status = "unhealthy"
                    
                    return {
                        "status": status,
                        "cpu_usage": cpu_usage,
                        "memory_usage": memory_usage,
                        "disk_usage": metrics_data.get("disk_usage", 0),
                        "network_io": metrics_data.get("network_io", {}),
                        "request_rate": metrics_data.get("request_rate", 0)
                    }
                else:
                    return {
                        "status": "degraded",
                        "error": "Performance metrics endpoint failed"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _check_security_config(self) -> Dict[str, Any]:
        """Check security configuration"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Security config endpoint
                security_url = f"{self.endpoints.get('api', '')}/health/security"
                
                response = await client.get(security_url)
                
                if response.status_code == 200:
                    security_data = response.json()
                    
                    return {
                        "status": "healthy" if security_data.get("secure") else "degraded",
                        "ssl_enabled": security_data.get("ssl_enabled", False),
                        "cors_configured": security_data.get("cors_configured", False),
                        "rate_limiting": security_data.get("rate_limiting", False),
                        "security_headers": security_data.get("security_headers", {})
                    }
                else:
                    return {
                        "status": "degraded",
                        "error": "Security config endpoint failed"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _get_db_variant_info(self) -> Dict[str, Any]:
        """Get database variant-specific information"""
        variant_info = {
            "aws": {"type": "Aurora PostgreSQL Serverless v2", "scaling": "auto"},
            "azure": {"type": "Azure SQL Database", "tier": "Serverless"},
            "gcp": {"type": "Cloud SQL PostgreSQL", "tier": "Regional"},
            "multi": {"type": "Multi-cloud distributed", "replication": "cross-cloud"},
            "oss": {"type": "PostgreSQL", "deployment": "containerized"}
        }
        return variant_info.get(self.variant, {})
    
    def _get_cache_variant_info(self) -> Dict[str, Any]:
        """Get cache variant-specific information"""
        variant_info = {
            "aws": {"type": "ElastiCache Redis", "mode": "cluster"},
            "azure": {"type": "Azure Cache for Redis", "tier": "Standard"},
            "gcp": {"type": "Memorystore Redis", "tier": "Standard HA"},
            "multi": {"type": "Multi-cloud Redis", "sync": "cross-cloud"},
            "oss": {"type": "Redis", "deployment": "containerized"}
        }
        return variant_info.get(self.variant, {})
    
    def _get_ai_variant_info(self) -> Dict[str, Any]:
        """Get AI engine variant-specific information"""
        variant_info = {
            "aws": {"primary": "Manus RL", "fallback": "AWS Bedrock"},
            "azure": {"primary": "Azure OpenAI", "fallback": "Cognitive Services"},
            "gcp": {"primary": "Vertex AI", "fallback": "BigQuery ML"},
            "multi": {"primary": "Intelligent Router", "fallback": "Best Available"},
            "oss": {"primary": "Manus OSS", "fallback": "Ollama"}
        }
        return variant_info.get(self.variant, {})
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate health check summary"""
        total_checks = len(results)
        healthy_checks = sum(1 for r in results.values() if r.get("status") == "healthy")
        degraded_checks = sum(1 for r in results.values() if r.get("status") == "degraded")
        unhealthy_checks = sum(1 for r in results.values() if r.get("status") in ["unhealthy", "error"])
        
        health_score = (healthy_checks / total_checks) * 100 if total_checks > 0 else 0
        
        return {
            "total_checks": total_checks,
            "healthy_checks": healthy_checks,
            "degraded_checks": degraded_checks,
            "unhealthy_checks": unhealthy_checks,
            "health_score": round(health_score, 1),
            "recommendations": self._get_recommendations(results)
        }
    
    def _get_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Get recommendations based on health check results"""
        recommendations = []
        
        for check_name, result in results.items():
            status = result.get("status")
            
            if status == "unhealthy":
                recommendations.append(f"URGENT: Fix {check_name} - system is unhealthy")
            elif status == "degraded":
                recommendations.append(f"WARNING: Monitor {check_name} - performance degraded")
            elif status == "error":
                recommendations.append(f"ERROR: Investigate {check_name} - check failed")
        
        # Performance-specific recommendations
        if "Performance Metrics" in results:
            perf = results["Performance Metrics"]
            if perf.get("cpu_usage", 0) > 80:
                recommendations.append("Consider scaling up CPU resources")
            if perf.get("memory_usage", 0) > 85:
                recommendations.append("Consider increasing memory allocation")
        
        return recommendations

async def main():
    """Main health check function"""
    parser = argparse.ArgumentParser(description="Multi-Cloud Health Checker")
    parser.add_argument("--variant", required=True, 
                       choices=["aws", "azure", "gcp", "multi", "oss"],
                       help="Cloud variant to check")
    parser.add_argument("--environment", required=True,
                       choices=["dev", "staging", "prod"],
                       help="Environment to check")
    parser.add_argument("--output", choices=["json", "text"], default="text",
                       help="Output format")
    
    args = parser.parse_args()
    
    # Initialize health checker
    checker = MultiCloudHealthChecker(args.variant, args.environment)
    
    # Run comprehensive health check
    results = await checker.run_comprehensive_health_check()
    
    # Output results
    if args.output == "json":
        print(json.dumps(results, indent=2))
    else:
        # Text output
        print(f"\n🏥 OMNIFY HEALTH CHECK REPORT")
        print(f"{'='*50}")
        print(f"Variant: {results['variant']}")
        print(f"Environment: {results['environment']}")
        print(f"Overall Status: {results['overall_status'].upper()}")
        print(f"Health Score: {results['summary']['health_score']}%")
        print(f"Duration: {results['check_duration']}s")
        
        print(f"\n📊 CHECK RESULTS:")
        for check_name, result in results['checks'].items():
            status = result.get('status', 'unknown')
            emoji = "✅" if status == "healthy" else "⚠️" if status == "degraded" else "❌"
            print(f"  {emoji} {check_name}: {status.upper()}")
        
        if results['summary']['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in results['summary']['recommendations']:
                print(f"  • {rec}")
    
    # Exit with appropriate code
    if results['overall_status'] == "healthy":
        sys.exit(0)
    elif results['overall_status'] == "degraded":
        sys.exit(1)
    else:
        sys.exit(2)

if __name__ == "__main__":
    asyncio.run(main())
