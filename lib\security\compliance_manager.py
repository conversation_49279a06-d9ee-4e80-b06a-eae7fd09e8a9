"""
Security and Compliance Manager for Omnify Marketing Cloud
Handles GDPR, data encryption, audit logging, and security policies
"""
import asyncio
import hashlib
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from enum import Enum
import structlog
from pydantic import BaseModel, Field
from cryptography.fernet import Fernet
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from apps.core.database import get_db
from apps.core.config import settings

logger = structlog.get_logger()

class DataCategory(str, Enum):
    """Data categories for GDPR compliance"""
    PERSONAL_DATA = "personal_data"
    MARKETING_DATA = "marketing_data"
    ANALYTICS_DATA = "analytics_data"
    FINANCIAL_DATA = "financial_data"
    TECHNICAL_DATA = "technical_data"

class ProcessingPurpose(str, Enum):
    """Data processing purposes"""
    MARKETING_AUTOMATION = "marketing_automation"
    ANALYTICS = "analytics"
    BILLING = "billing"
    SUPPORT = "support"
    LEGAL_COMPLIANCE = "legal_compliance"

class ConsentStatus(str, Enum):
    """Consent status for data processing"""
    GIVEN = "given"
    WITHDRAWN = "withdrawn"
    PENDING = "pending"
    NOT_REQUIRED = "not_required"

class AuditAction(str, Enum):
    """Audit log action types"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXPORT = "export"
    LOGIN = "login"
    LOGOUT = "logout"
    API_ACCESS = "api_access"

class DataConsent(BaseModel):
    """Data processing consent record"""
    customer_id: str
    client_id: str
    data_category: DataCategory
    processing_purpose: ProcessingPurpose
    consent_status: ConsentStatus
    consent_date: datetime
    withdrawal_date: Optional[datetime] = None
    legal_basis: str
    retention_period_days: int

class AuditLogEntry(BaseModel):
    """Audit log entry"""
    id: str
    timestamp: datetime
    user_id: Optional[str]
    client_id: Optional[str]
    action: AuditAction
    resource_type: str
    resource_id: str
    details: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    success: bool
    error_message: Optional[str] = None

class DataRetentionPolicy(BaseModel):
    """Data retention policy"""
    data_category: DataCategory
    retention_period_days: int
    auto_delete: bool
    archive_before_delete: bool
    legal_hold_override: bool

class SecurityPolicy(BaseModel):
    """Security policy configuration"""
    password_min_length: int = 8
    password_require_uppercase: bool = True
    password_require_lowercase: bool = True
    password_require_numbers: bool = True
    password_require_symbols: bool = False
    session_timeout_minutes: int = 480  # 8 hours
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30
    require_2fa: bool = False
    api_rate_limit_per_minute: int = 60

class ComplianceManager:
    """Manages security and compliance features"""
    
    def __init__(self):
        self.encryption_key = self._get_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        self.audit_logs: List[AuditLogEntry] = []
        self.data_consents: Dict[str, List[DataConsent]] = {}
        self.retention_policies = self._initialize_retention_policies()
        self.security_policy = SecurityPolicy()
    
    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key"""
        key = getattr(settings, 'ENCRYPTION_KEY', None)
        if key:
            return key.encode()
        else:
            # Generate new key (in production, store securely)
            return Fernet.generate_key()
    
    def _initialize_retention_policies(self) -> List[DataRetentionPolicy]:
        """Initialize default data retention policies"""
        return [
            DataRetentionPolicy(
                data_category=DataCategory.PERSONAL_DATA,
                retention_period_days=2555,  # 7 years
                auto_delete=False,  # Require manual review
                archive_before_delete=True,
                legal_hold_override=True
            ),
            DataRetentionPolicy(
                data_category=DataCategory.MARKETING_DATA,
                retention_period_days=1095,  # 3 years
                auto_delete=True,
                archive_before_delete=True,
                legal_hold_override=False
            ),
            DataRetentionPolicy(
                data_category=DataCategory.ANALYTICS_DATA,
                retention_period_days=730,  # 2 years
                auto_delete=True,
                archive_before_delete=False,
                legal_hold_override=False
            ),
            DataRetentionPolicy(
                data_category=DataCategory.FINANCIAL_DATA,
                retention_period_days=2555,  # 7 years
                auto_delete=False,
                archive_before_delete=True,
                legal_hold_override=True
            ),
            DataRetentionPolicy(
                data_category=DataCategory.TECHNICAL_DATA,
                retention_period_days=365,  # 1 year
                auto_delete=True,
                archive_before_delete=False,
                legal_hold_override=False
            )
        ]
    
    def encrypt_sensitive_data(self, data: Union[str, Dict[str, Any]]) -> str:
        """Encrypt sensitive data"""
        if isinstance(data, dict):
            data = json.dumps(data)
        
        encrypted_data = self.cipher_suite.encrypt(data.encode())
        return encrypted_data.decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            decrypted_data = self.cipher_suite.decrypt(encrypted_data.encode())
            return decrypted_data.decode()
        except Exception as e:
            logger.error("Failed to decrypt data", error=str(e))
            raise
    
    async def log_audit_event(
        self,
        action: AuditAction,
        resource_type: str,
        resource_id: str,
        user_id: Optional[str] = None,
        client_id: Optional[str] = None,
        details: Dict[str, Any] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """Log audit event"""
        
        audit_entry = AuditLogEntry(
            id=f"audit_{int(datetime.utcnow().timestamp())}_{len(self.audit_logs)}",
            timestamp=datetime.utcnow(),
            user_id=user_id,
            client_id=client_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details or {},
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message
        )
        
        self.audit_logs.append(audit_entry)
        
        # In production, store in database or external audit system
        logger.info(
            "Audit event logged",
            action=action.value,
            resource_type=resource_type,
            resource_id=resource_id,
            user_id=user_id,
            success=success
        )
    
    async def record_data_consent(
        self,
        customer_id: str,
        client_id: str,
        data_category: DataCategory,
        processing_purpose: ProcessingPurpose,
        consent_status: ConsentStatus,
        legal_basis: str = "consent",
        retention_period_days: int = 2555
    ):
        """Record data processing consent"""
        
        consent = DataConsent(
            customer_id=customer_id,
            client_id=client_id,
            data_category=data_category,
            processing_purpose=processing_purpose,
            consent_status=consent_status,
            consent_date=datetime.utcnow(),
            legal_basis=legal_basis,
            retention_period_days=retention_period_days
        )
        
        if customer_id not in self.data_consents:
            self.data_consents[customer_id] = []
        
        self.data_consents[customer_id].append(consent)
        
        await self.log_audit_event(
            action=AuditAction.CREATE,
            resource_type="data_consent",
            resource_id=f"{customer_id}_{data_category.value}",
            client_id=client_id,
            details={
                "data_category": data_category.value,
                "processing_purpose": processing_purpose.value,
                "consent_status": consent_status.value
            }
        )
    
    async def withdraw_consent(
        self,
        customer_id: str,
        data_category: DataCategory,
        processing_purpose: ProcessingPurpose
    ):
        """Withdraw data processing consent"""
        
        if customer_id in self.data_consents:
            for consent in self.data_consents[customer_id]:
                if (consent.data_category == data_category and 
                    consent.processing_purpose == processing_purpose and
                    consent.consent_status == ConsentStatus.GIVEN):
                    
                    consent.consent_status = ConsentStatus.WITHDRAWN
                    consent.withdrawal_date = datetime.utcnow()
                    
                    await self.log_audit_event(
                        action=AuditAction.UPDATE,
                        resource_type="data_consent",
                        resource_id=f"{customer_id}_{data_category.value}",
                        details={
                            "action": "consent_withdrawn",
                            "data_category": data_category.value,
                            "processing_purpose": processing_purpose.value
                        }
                    )
                    
                    return True
        
        return False
    
    def check_consent(
        self,
        customer_id: str,
        data_category: DataCategory,
        processing_purpose: ProcessingPurpose
    ) -> bool:
        """Check if consent exists for data processing"""
        
        if customer_id not in self.data_consents:
            return False
        
        for consent in self.data_consents[customer_id]:
            if (consent.data_category == data_category and 
                consent.processing_purpose == processing_purpose and
                consent.consent_status == ConsentStatus.GIVEN):
                return True
        
        return False
    
    async def export_customer_data(
        self,
        customer_id: str,
        client_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Export all customer data (GDPR Article 20)"""
        
        await self.log_audit_event(
            action=AuditAction.EXPORT,
            resource_type="customer_data",
            resource_id=customer_id,
            client_id=client_id,
            details={"export_type": "gdpr_data_portability"}
        )
        
        # Collect all customer data from various tables
        customer_data = {
            "customer_id": customer_id,
            "export_date": datetime.utcnow().isoformat(),
            "data_categories": {}
        }
        
        # Get customer profile
        from apps.core.database import CustomerProfile
        profile_result = await db.execute(
            select(CustomerProfile).where(
                and_(
                    CustomerProfile.customer_id == customer_id,
                    CustomerProfile.client_id == client_id
                )
            )
        )
        profile = profile_result.scalar_one_or_none()
        
        if profile:
            customer_data["data_categories"]["personal_data"] = {
                "email": profile.email,
                "first_name": profile.first_name,
                "last_name": profile.last_name,
                "total_purchases": profile.total_purchases,
                "total_spent": profile.total_spent,
                "last_purchase_date": profile.last_purchase_date.isoformat() if profile.last_purchase_date else None
            }
        
        # Get retention actions
        from apps.core.database import RetentionAction
        actions_result = await db.execute(
            select(RetentionAction).where(
                and_(
                    RetentionAction.customer_id == customer_id,
                    RetentionAction.client_id == client_id
                )
            )
        )
        actions = actions_result.scalars().all()
        
        customer_data["data_categories"]["marketing_data"] = {
            "retention_actions": [
                {
                    "action_type": action.action_type,
                    "created_at": action.created_at.isoformat(),
                    "status": action.status
                }
                for action in actions
            ]
        }
        
        # Get consent records
        customer_data["data_categories"]["consent_records"] = [
            {
                "data_category": consent.data_category.value,
                "processing_purpose": consent.processing_purpose.value,
                "consent_status": consent.consent_status.value,
                "consent_date": consent.consent_date.isoformat(),
                "withdrawal_date": consent.withdrawal_date.isoformat() if consent.withdrawal_date else None
            }
            for consent in self.data_consents.get(customer_id, [])
        ]
        
        return customer_data
    
    async def delete_customer_data(
        self,
        customer_id: str,
        client_id: str,
        db: AsyncSession,
        reason: str = "gdpr_right_to_erasure"
    ) -> bool:
        """Delete all customer data (GDPR Article 17)"""
        
        await self.log_audit_event(
            action=AuditAction.DELETE,
            resource_type="customer_data",
            resource_id=customer_id,
            client_id=client_id,
            details={
                "deletion_reason": reason,
                "deletion_type": "complete_erasure"
            }
        )
        
        try:
            # Delete from all tables
            from apps.core.database import CustomerProfile, RetentionAction
            
            # Delete customer profile
            await db.execute(
                select(CustomerProfile).where(
                    and_(
                        CustomerProfile.customer_id == customer_id,
                        CustomerProfile.client_id == client_id
                    )
                ).delete()
            )
            
            # Delete retention actions
            await db.execute(
                select(RetentionAction).where(
                    and_(
                        RetentionAction.customer_id == customer_id,
                        RetentionAction.client_id == client_id
                    )
                ).delete()
            )
            
            # Remove consent records
            if customer_id in self.data_consents:
                del self.data_consents[customer_id]
            
            await db.commit()
            
            logger.info(
                "Customer data deleted",
                customer_id=customer_id,
                client_id=client_id,
                reason=reason
            )
            
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(
                "Failed to delete customer data",
                error=str(e),
                customer_id=customer_id
            )
            return False
    
    async def anonymize_customer_data(
        self,
        customer_id: str,
        client_id: str,
        db: AsyncSession
    ) -> bool:
        """Anonymize customer data while preserving analytics"""
        
        await self.log_audit_event(
            action=AuditAction.UPDATE,
            resource_type="customer_data",
            resource_id=customer_id,
            client_id=client_id,
            details={"action": "anonymization"}
        )
        
        try:
            # Generate anonymous ID
            anonymous_id = hashlib.sha256(f"{customer_id}_{datetime.utcnow()}".encode()).hexdigest()[:16]
            
            # Anonymize customer profile
            from apps.core.database import CustomerProfile
            profile_result = await db.execute(
                select(CustomerProfile).where(
                    and_(
                        CustomerProfile.customer_id == customer_id,
                        CustomerProfile.client_id == client_id
                    )
                )
            )
            profile = profile_result.scalar_one_or_none()
            
            if profile:
                profile.customer_id = f"anon_{anonymous_id}"
                profile.email = None
                profile.first_name = None
                profile.last_name = None
            
            await db.commit()
            
            logger.info(
                "Customer data anonymized",
                original_customer_id=customer_id,
                anonymous_id=anonymous_id
            )
            
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error("Failed to anonymize customer data", error=str(e))
            return False
    
    def get_audit_logs(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[str] = None,
        client_id: Optional[str] = None,
        action: Optional[AuditAction] = None
    ) -> List[AuditLogEntry]:
        """Get filtered audit logs"""
        
        logs = self.audit_logs
        
        if start_date:
            logs = [log for log in logs if log.timestamp >= start_date]
        
        if end_date:
            logs = [log for log in logs if log.timestamp <= end_date]
        
        if user_id:
            logs = [log for log in logs if log.user_id == user_id]
        
        if client_id:
            logs = [log for log in logs if log.client_id == client_id]
        
        if action:
            logs = [log for log in logs if log.action == action]
        
        return logs
    
    async def cleanup_expired_data(self, db: AsyncSession):
        """Clean up expired data based on retention policies"""
        
        for policy in self.retention_policies:
            cutoff_date = datetime.utcnow() - timedelta(days=policy.retention_period_days)
            
            if policy.auto_delete:
                # Would implement automatic deletion based on data category
                logger.info(
                    "Data cleanup executed",
                    data_category=policy.data_category.value,
                    cutoff_date=cutoff_date.isoformat()
                )

# Global compliance manager instance
compliance_manager = ComplianceManager()
