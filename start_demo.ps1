# Omnify Marketing Cloud Demo Launcher for Windows PowerShell
# Universal cross-platform demo with automatic dependency installation

Write-Host "OMNIFY MARKETING CLOUD - UNIVERSAL DEMO" -ForegroundColor Cyan
Write-Host "============================================================" -ForegroundColor Gray
Write-Host "Platform: Windows (PowerShell)" -ForegroundColor Green
Write-Host ""

# Check if Python is available
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python detected: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ from https://python.org" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Install dependencies
Write-Host "Installing/updating dependencies..." -ForegroundColor Yellow
try {
    python -m pip install --upgrade pip --quiet
    python -m pip install fastapi "uvicorn[standard]" --quiet
    Write-Host "Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to install dependencies" -ForegroundColor Red
    Write-Host "Please check your internet connection and try again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting Omnify Demo..." -ForegroundColor Cyan
Write-Host ""
Write-Host "Demo will auto-detect available port and open in browser" -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the demo" -ForegroundColor Yellow
Write-Host ""

# Start the demo
try {
    python omnify_demo.py
} catch {
    Write-Host ""
    Write-Host "Demo encountered an error" -ForegroundColor Red
    Write-Host "Please check the error message above" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Demo stopped. Thank you for trying Omnify Marketing Cloud!" -ForegroundColor Green
Read-Host "Press Enter to exit"
