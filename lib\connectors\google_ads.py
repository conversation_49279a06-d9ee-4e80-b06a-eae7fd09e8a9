"""
Google Ads API Connector with OAuth2 authentication, rate limiting, and data validation
"""
import asyncio
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import structlog
from pydantic import BaseModel, Field, validator
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
import httpx

from apps.core.config import settings
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class GoogleAdsMetrics(BaseModel):
    """Unified output format for Google Ads data"""
    campaign_id: str
    campaign_name: str
    date: datetime
    impressions: int = 0
    clicks: int = 0
    conversions: float = 0.0
    cost_micros: int = 0  # Cost in micros (1 million micros = 1 currency unit)
    
    # Calculated fields
    cost: float = Field(default=0.0)
    ctr: float = Field(default=0.0)  # Click-through rate
    cpc: float = Field(default=0.0)  # Cost per click
    conversion_rate: float = Field(default=0.0)
    
    @validator('cost', always=True)
    def calculate_cost(cls, v, values):
        """Convert cost from micros to currency units"""
        return values.get('cost_micros', 0) / 1_000_000
    
    @validator('ctr', always=True)
    def calculate_ctr(cls, v, values):
        """Calculate click-through rate"""
        impressions = values.get('impressions', 0)
        clicks = values.get('clicks', 0)
        return (clicks / impressions * 100) if impressions > 0 else 0.0
    
    @validator('cpc', always=True)
    def calculate_cpc(cls, v, values):
        """Calculate cost per click"""
        clicks = values.get('clicks', 0)
        cost = values.get('cost', 0)
        return cost / clicks if clicks > 0 else 0.0
    
    @validator('conversion_rate', always=True)
    def calculate_conversion_rate(cls, v, values):
        """Calculate conversion rate"""
        clicks = values.get('clicks', 0)
        conversions = values.get('conversions', 0)
        return (conversions / clicks * 100) if clicks > 0 else 0.0

class GoogleAdsConnector:
    """Google Ads API connector with OAuth2 and rate limiting"""
    
    def __init__(self, customer_id: str):
        self.customer_id = customer_id.replace('-', '')  # Remove dashes
        self.client = None
        self.credentials = None
        self._rate_limit_delay = 1.0  # Start with 1 second delay
        self._last_request_time = 0
        
    async def authenticate(self) -> bool:
        """Authenticate with Google Ads API using OAuth2"""
        try:
            # Create credentials from settings
            self.credentials = Credentials(
                token=None,
                refresh_token=settings.GOOGLE_ADS_REFRESH_TOKEN,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=settings.GOOGLE_ADS_CLIENT_ID,
                client_secret=settings.GOOGLE_ADS_CLIENT_SECRET
            )
            
            # Refresh the token
            request = Request()
            self.credentials.refresh(request)
            
            # Initialize Google Ads client
            self.client = GoogleAdsClient(
                credentials=self.credentials,
                developer_token=settings.GOOGLE_ADS_DEVELOPER_TOKEN,
                use_proto_plus=True
            )
            
            logger.info("Google Ads authentication successful", customer_id=self.customer_id)
            return True
            
        except Exception as e:
            logger.error("Google Ads authentication failed", error=str(e))
            return False
    
    async def _rate_limit(self):
        """Implement rate limiting with exponential backoff"""
        current_time = time.time()
        time_since_last_request = current_time - self._last_request_time
        
        if time_since_last_request < self._rate_limit_delay:
            sleep_time = self._rate_limit_delay - time_since_last_request
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def get_campaign_metrics(
        self, 
        start_date: datetime, 
        end_date: datetime,
        campaign_ids: Optional[List[str]] = None
    ) -> List[GoogleAdsMetrics]:
        """
        Fetch campaign metrics with rate limiting and retry logic
        """
        if not self.client:
            if not await self.authenticate():
                raise Exception("Failed to authenticate with Google Ads API")
        
        await self._rate_limit()
        
        try:
            ga_service = self.client.get_service("GoogleAdsService")
            
            # Build query
            query = f"""
                SELECT 
                    campaign.id,
                    campaign.name,
                    segments.date,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.conversions,
                    metrics.cost_micros
                FROM campaign 
                WHERE segments.date BETWEEN '{start_date.strftime('%Y-%m-%d')}' 
                AND '{end_date.strftime('%Y-%m-%d')}'
            """
            
            # Add campaign filter if specified
            if campaign_ids:
                campaign_filter = "', '".join(campaign_ids)
                query += f" AND campaign.id IN ('{campaign_filter}')"
            
            query += " ORDER BY segments.date DESC"
            
            # Execute query
            search_request = self.client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = self.customer_id
            search_request.query = query
            
            response = ga_service.search(request=search_request)
            
            # Process results
            metrics_list = []
            for row in response:
                metrics = GoogleAdsMetrics(
                    campaign_id=str(row.campaign.id),
                    campaign_name=row.campaign.name,
                    date=datetime.strptime(row.segments.date, '%Y-%m-%d'),
                    impressions=row.metrics.impressions,
                    clicks=row.metrics.clicks,
                    conversions=row.metrics.conversions,
                    cost_micros=row.metrics.cost_micros
                )
                metrics_list.append(metrics)
            
            logger.info(
                "Successfully fetched Google Ads metrics",
                customer_id=self.customer_id,
                metrics_count=len(metrics_list)
            )
            
            # Reset rate limit delay on success
            self._rate_limit_delay = 1.0
            
            return metrics_list
            
        except GoogleAdsException as ex:
            logger.error(
                "Google Ads API error",
                customer_id=self.customer_id,
                error=ex.error.message if ex.error else str(ex)
            )
            
            # Increase rate limit delay on error
            self._rate_limit_delay = min(self._rate_limit_delay * 2, 60.0)
            raise
        
        except Exception as e:
            logger.error(
                "Unexpected error fetching Google Ads metrics",
                customer_id=self.customer_id,
                error=str(e)
            )
            raise
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def update_campaign_bid(
        self, 
        campaign_id: str, 
        bid_adjustment: float
    ) -> bool:
        """
        Update campaign bid with the specified adjustment
        """
        if not self.client:
            if not await self.authenticate():
                raise Exception("Failed to authenticate with Google Ads API")
        
        await self._rate_limit()
        
        try:
            campaign_service = self.client.get_service("CampaignService")
            campaign_operation = self.client.get_type("CampaignOperation")
            
            # Get current campaign
            campaign = campaign_operation.update
            campaign.resource_name = campaign_service.campaign_path(
                self.customer_id, campaign_id
            )
            
            # Apply bid adjustment (this is simplified - actual implementation
            # would depend on bidding strategy)
            # For manual CPC campaigns:
            if hasattr(campaign, 'manual_cpc'):
                current_bid = campaign.manual_cpc.enhanced_cpc_enabled
                new_bid = current_bid * (1 + bid_adjustment)
                campaign.manual_cpc.enhanced_cpc_enabled = new_bid
            
            # Update the campaign
            response = campaign_service.mutate_campaigns(
                customer_id=self.customer_id,
                operations=[campaign_operation]
            )
            
            logger.info(
                "Successfully updated campaign bid",
                customer_id=self.customer_id,
                campaign_id=campaign_id,
                bid_adjustment=bid_adjustment
            )
            
            return True
            
        except GoogleAdsException as ex:
            logger.error(
                "Failed to update campaign bid",
                customer_id=self.customer_id,
                campaign_id=campaign_id,
                error=ex.error.message if ex.error else str(ex)
            )
            raise
        
        except Exception as e:
            logger.error(
                "Unexpected error updating campaign bid",
                customer_id=self.customer_id,
                campaign_id=campaign_id,
                error=str(e)
            )
            raise
