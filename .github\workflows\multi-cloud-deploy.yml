name: Multi-Cloud Deployment Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      variant:
        description: 'Cloud variant to deploy'
        required: true
        default: 'aws'
        type: choice
        options:
          - aws
          - azure
          - gcp
          - multi
          - oss
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        variant: [aws, azure, gcp, multi, oss]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.variant }}-${{ hashFiles('requirements/*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.variant }}-
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements/${{ matrix.variant }}.txt
        pip install pytest pytest-asyncio pytest-cov
    
    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=lib --cov-report=xml
    
    - name: Run integration tests
      run: |
        pytest tests/integration/test_${{ matrix.variant }}_integration.py -v
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: ${{ matrix.variant }}

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Bandit security scan
      run: |
        pip install bandit
        bandit -r lib/ apps/ scripts/ -f json -o bandit-report.json
    
    - name: Run Safety check
      run: |
        pip install safety
        safety check --json --output safety-report.json
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    strategy:
      matrix:
        variant: [aws, azure, gcp, multi, oss]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/${{ matrix.variant }}/Dockerfile
        push: false
        tags: omnify-${{ matrix.variant }}:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Test Docker image
      run: |
        docker run --rm omnify-${{ matrix.variant }}:${{ github.sha }} python -c "import sys; print(f'Python {sys.version}')"

  deploy-aws:
    name: Deploy to AWS
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    environment: 
      name: ${{ github.event.inputs.environment || 'dev' }}
      url: ${{ steps.deploy.outputs.api_url }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install deployment dependencies
      run: |
        pip install -r requirements/aws.txt
        curl -fsSL https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip -o terraform.zip
        unzip terraform.zip
        sudo mv terraform /usr/local/bin/
    
    - name: Deploy AWS variant
      id: deploy
      run: |
        python scripts/universal_deploy.py deploy \
          --variant aws \
          --environment ${{ github.event.inputs.environment || 'dev' }} \
          --region us-east-1
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    
    - name: Run health checks
      run: |
        python scripts/health_check.py --variant aws --environment ${{ github.event.inputs.environment || 'dev' }}

  deploy-azure:
    name: Deploy to Azure
    runs-on: ubuntu-latest
    needs: [build-images]
    if: (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch') && (github.event.inputs.variant == 'azure' || github.event.inputs.variant == '')
    environment: 
      name: ${{ github.event.inputs.environment || 'dev' }}
      url: ${{ steps.deploy.outputs.api_url }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install deployment dependencies
      run: |
        pip install -r requirements/azure.txt
        curl -fsSL https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip -o terraform.zip
        unzip terraform.zip
        sudo mv terraform /usr/local/bin/
    
    - name: Deploy Azure variant
      id: deploy
      run: |
        python scripts/universal_deploy.py deploy \
          --variant azure \
          --environment ${{ github.event.inputs.environment || 'dev' }} \
          --region eastus
      env:
        AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
        AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
        AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}

  deploy-gcp:
    name: Deploy to GCP
    runs-on: ubuntu-latest
    needs: [build-images]
    if: (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch') && (github.event.inputs.variant == 'gcp' || github.event.inputs.variant == '')
    environment: 
      name: ${{ github.event.inputs.environment || 'dev' }}
      url: ${{ steps.deploy.outputs.api_url }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
    
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install deployment dependencies
      run: |
        pip install -r requirements/gcp.txt
        curl -fsSL https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip -o terraform.zip
        unzip terraform.zip
        sudo mv terraform /usr/local/bin/
    
    - name: Deploy GCP variant
      id: deploy
      run: |
        python scripts/universal_deploy.py deploy \
          --variant gcp \
          --environment ${{ github.event.inputs.environment || 'dev' }} \
          --region us-central1
      env:
        GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_SA_KEY }}
        GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

  deploy-multi-cloud:
    name: Deploy Multi-Cloud
    runs-on: ubuntu-latest
    needs: [deploy-aws, deploy-azure, deploy-gcp]
    if: github.event.inputs.variant == 'multi'
    environment: 
      name: ${{ github.event.inputs.environment || 'dev' }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        pip install -r requirements/multi.txt
    
    - name: Configure multi-cloud deployment
      run: |
        python scripts/universal_deploy.py deploy \
          --variant multi \
          --environment ${{ github.event.inputs.environment || 'dev' }}
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
        AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
        AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
        GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_SA_KEY }}
        GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-aws, deploy-azure, deploy-gcp, deploy-multi-cloud]
    if: always()
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          Multi-cloud deployment completed
          Variant: ${{ github.event.inputs.variant || 'all' }}
          Environment: ${{ github.event.inputs.environment || 'dev' }}
          Status: ${{ job.status }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: always()
