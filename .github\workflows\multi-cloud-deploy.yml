name: Multi-Cloud Deployment Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      variant:
        description: 'Cloud variant to deploy'
        required: true
        default: 'aws'
        type: choice
        options:
          - aws
          - azure
          - gcp
          - multi
          - oss
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        variant: [aws, azure, gcp, multi, oss]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.variant }}-${{ hashFiles('requirements/*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.variant }}-
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements/${{ matrix.variant }}.txt
        pip install pytest pytest-asyncio pytest-cov

    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=lib --cov-report=xml

    - name: Run integration tests
      run: |
        pytest tests/integration/test_${{ matrix.variant }}_integration.py -v

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: ${{ matrix.variant }}

  security-scan:
    name: Comprehensive Security Scan
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install security scanning tools
      run: |
        # Python security tools
        pip install bandit safety semgrep checkov

        # Node.js security tools
        npm install -g audit-ci @cyclonedx/cyclonedx-npm

        # Container security tools
        curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
        curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh -s -- -b /usr/local/bin

    - name: Python Code Security Scan (Bandit)
      run: |
        echo "🐍 Running Bandit security scan..."
        bandit -r lib/ apps/ scripts/ -f json -o bandit-report.json || true
        bandit -r lib/ apps/ scripts/ -f txt || true

    - name: Python Dependency Security Scan (Safety)
      run: |
        echo "🔒 Running Safety dependency scan..."
        safety check --json --output safety-report.json || true
        safety check || true

    - name: Secrets Detection (Semgrep)
      run: |
        echo "🔑 Scanning for secrets and sensitive data..."
        semgrep --config=p/secrets --config=p/security-audit --json --output=semgrep-secrets.json . || true
        semgrep --config=p/secrets --config=p/security-audit . || true

    - name: Infrastructure Security Scan (Checkov)
      run: |
        echo "🏗️ Scanning infrastructure code with Checkov..."
        checkov -d infrastructure/ --framework terraform --output json --output-file checkov-terraform.json || true
        checkov -d . --framework dockerfile --output json --output-file checkov-docker.json || true
        checkov -d infrastructure/ --framework terraform || true

    - name: Container Security Scan (Grype)
      run: |
        echo "🐳 Scanning container images for vulnerabilities..."
        # Scan Dockerfiles for vulnerabilities
        for dockerfile in docker/*/Dockerfile; do
          echo "Scanning $dockerfile..."
          syft "$dockerfile" -o json > "syft-$(basename $(dirname $dockerfile)).json" || true
          grype "$dockerfile" -o json > "grype-$(basename $(dirname $dockerfile)).json" || true
        done

    - name: SAST Analysis (Semgrep)
      run: |
        echo "🔍 Running Static Application Security Testing..."
        semgrep --config=auto --json --output=sast-full-report.json . || true
        semgrep --config=p/owasp-top-10 --config=p/cwe-top-25 . || true

    - name: License Compliance Scan
      run: |
        echo "📜 Scanning for license compliance..."
        pip-licenses --format=json --output-file=python-licenses.json || true
        if [ -f "package.json" ]; then
          npm install
          npx license-checker --json --out npm-licenses.json || true
        fi

    - name: Generate SBOM (Software Bill of Materials)
      run: |
        echo "📋 Generating Software Bill of Materials..."
        syft . -o json > sbom.json || true
        syft . -o spdx-json > sbom-spdx.json || true

    - name: Security Report Summary
      run: |
        echo "📊 Generating security report summary..."
        python scripts/security_report_generator.py

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
          semgrep-secrets.json
          checkov-terraform.json
          checkov-docker.json
          sast-full-report.json
          grype-*.json
          syft-*.json
          security-summary.json
          sbom.json
          sbom-spdx.json
          python-licenses.json
          npm-licenses.json

    - name: Comment PR with Security Summary
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          try {
            const summary = JSON.parse(fs.readFileSync('security-summary.json', 'utf8'));
            const status = summary.overall_status === 'PASS' ? '✅' :
                          summary.overall_status === 'WARNING' ? '⚠️' : '❌';

            const body = `## ${status} Security Scan Results

            | Check | Count |
            |-------|-------|
            | Bandit Issues | ${summary.bandit_issues} |
            | Safety Vulnerabilities | ${summary.safety_vulnerabilities} |
            | Secrets Found | ${summary.secrets_found} |
            | Terraform Issues | ${summary.terraform_issues} |
            | Docker Issues | ${summary.docker_issues} |

            **Overall Status:** ${summary.overall_status}

            📊 Detailed reports are available in the workflow artifacts.
            `;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });
          } catch (error) {
            console.log('Could not post security summary:', error);
          }

  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    strategy:
      matrix:
        variant: [aws, azure, gcp, multi, oss]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/${{ matrix.variant }}/Dockerfile
        push: false
        tags: omnify-${{ matrix.variant }}:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Test Docker image
      run: |
        docker run --rm omnify-${{ matrix.variant }}:${{ github.sha }} python -c "import sys; print(f'Python {sys.version}')"

  deploy-aws:
    name: Deploy to AWS
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    environment:
      name: ${{ github.event.inputs.environment || 'dev' }}
      url: ${{ steps.deploy.outputs.api_url }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install deployment dependencies
      run: |
        pip install -r requirements/aws.txt
        curl -fsSL https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip -o terraform.zip
        unzip terraform.zip
        sudo mv terraform /usr/local/bin/

    - name: Deploy AWS variant
      id: deploy
      run: |
        python scripts/universal_deploy.py deploy \
          --variant aws \
          --environment ${{ github.event.inputs.environment || 'dev' }} \
          --region us-east-1
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

    - name: Run health checks
      run: |
        python scripts/health_check.py --variant aws --environment ${{ github.event.inputs.environment || 'dev' }}

  deploy-azure:
    name: Deploy to Azure
    runs-on: ubuntu-latest
    needs: [build-images]
    if: (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch') && (github.event.inputs.variant == 'azure' || github.event.inputs.variant == '')
    environment:
      name: ${{ github.event.inputs.environment || 'dev' }}
      url: ${{ steps.deploy.outputs.api_url }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install deployment dependencies
      run: |
        pip install -r requirements/azure.txt
        curl -fsSL https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip -o terraform.zip
        unzip terraform.zip
        sudo mv terraform /usr/local/bin/

    - name: Deploy Azure variant
      id: deploy
      run: |
        python scripts/universal_deploy.py deploy \
          --variant azure \
          --environment ${{ github.event.inputs.environment || 'dev' }} \
          --region eastus
      env:
        AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
        AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
        AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}

  deploy-gcp:
    name: Deploy to GCP
    runs-on: ubuntu-latest
    needs: [build-images]
    if: (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch') && (github.event.inputs.variant == 'gcp' || github.event.inputs.variant == '')
    environment:
      name: ${{ github.event.inputs.environment || 'dev' }}
      url: ${{ steps.deploy.outputs.api_url }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install deployment dependencies
      run: |
        pip install -r requirements/gcp.txt
        curl -fsSL https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip -o terraform.zip
        unzip terraform.zip
        sudo mv terraform /usr/local/bin/

    - name: Deploy GCP variant
      id: deploy
      run: |
        python scripts/universal_deploy.py deploy \
          --variant gcp \
          --environment ${{ github.event.inputs.environment || 'dev' }} \
          --region us-central1
      env:
        GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_SA_KEY }}
        GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

  deploy-multi-cloud:
    name: Deploy Multi-Cloud
    runs-on: ubuntu-latest
    needs: [deploy-aws, deploy-azure, deploy-gcp]
    if: github.event.inputs.variant == 'multi'
    environment:
      name: ${{ github.event.inputs.environment || 'dev' }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        pip install -r requirements/multi.txt

    - name: Configure multi-cloud deployment
      run: |
        python scripts/universal_deploy.py deploy \
          --variant multi \
          --environment ${{ github.event.inputs.environment || 'dev' }}
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
        AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
        AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
        GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_SA_KEY }}
        GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-aws, deploy-azure, deploy-gcp, deploy-multi-cloud]
    if: always()

    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          Multi-cloud deployment completed
          Variant: ${{ github.event.inputs.variant || 'all' }}
          Environment: ${{ github.event.inputs.environment || 'dev' }}
          Status: ${{ job.status }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: always()
