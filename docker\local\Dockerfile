# Local development Dockerfile for Omnify Marketing Cloud Demo
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    CLOUD_VARIANT=local \
    AI_ENGINE=demo

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create app user
RUN groupadd -r omnify && useradd -r -g omnify omnify
RUN chown -R omnify:omnify /app
USER omnify

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["uvicorn", "apps.core.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
