@echo off
echo.
echo OMNIFY MARKETING CLOUD - UNIVERSAL DEMO
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python detected
echo.

REM Install requirements
echo Installing/updating dependencies...
python -m pip install --upgrade pip --quiet
python -m pip install fastapi uvicorn[standard] --quiet

echo.
echo Starting Omnify Demo...
echo.
echo Demo will auto-detect available port and open in browser
echo Press Ctrl+C to stop the demo
echo.

REM Start the universal demo
python omnify_demo.py

echo.
echo Demo stopped. Press any key to exit...
pause >nul
