@echo off
echo.
echo 🎯 OMNIFY MARKETING CLOUD - WINDOWS DEMO STARTER
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python detected
echo.

REM Install requirements
echo 📦 Installing requirements...
python -m pip install --upgrade pip
python -m pip install fastapi uvicorn redis python-multipart jinja2

REM Check if Docker is running (for Redis)
docker version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Docker not detected. Starting without Redis...
    set REDIS_URL=memory://
) else (
    echo 🐳 Docker detected, starting Redis...
    docker run -d --name omnify-redis-demo -p 6379:6379 redis:7-alpine 2>nul
    set REDIS_URL=redis://localhost:6379/0
)

REM Set environment variables
set DATABASE_URL=sqlite:///./omnify_demo.db
set ENVIRONMENT=demo
set DEBUG=true
set DEMO_MODE=true
set CLOUD_VARIANT=local

echo.
echo 🚀 Starting Omnify Demo Server...
echo.
echo 📍 Demo will be available at:
echo    • API Docs: http://localhost:8000/docs
echo    • Demo Dashboard: http://localhost:8000/demo
echo    • Health Check: http://localhost:8000/health
echo.
echo 💡 Press Ctrl+C to stop the demo
echo.

REM Start the server
python windows_demo.py

echo.
echo 👋 Demo stopped. Press any key to exit...
pause >nul
