"""
Enhanced Authentication Router with Complete User Management
"""
from datetime import datetime, timedelta
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel, EmailStr, Field
import structlog
import secrets

from apps.core.database import get_db, User, Client
from apps.core.config import settings
from apps.auth.user_management import user_manager, UserRegistration, UserResponse
from lib.integrations.email_service import email_manager, EmailRecipient

logger = structlog.get_logger()

router = APIRouter()

# Enhanced security configuration
SECRET_KEY = getattr(settings, 'SECRET_KEY', 'your-secret-key-change-in-production')
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 480  # 8 hours
REFRESH_TOKEN_EXPIRE_DAYS = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

class EnhancedToken(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    user: UserResponse

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class PasswordChangeRequest(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8)

class UserInvitation(BaseModel):
    email: EmailStr
    role: str = "user"
    first_name: str
    last_name: str

class TeamMember(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    role: str
    is_active: bool
    last_login: Optional[datetime]
    created_at: datetime

# Token storage (in production, use Redis)
refresh_tokens = {}

def create_refresh_token(user_id: int) -> str:
    """Create refresh token"""
    token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    
    refresh_tokens[token] = {
        "user_id": user_id,
        "expires_at": expires_at,
        "created_at": datetime.utcnow()
    }
    
    return token

def verify_refresh_token(token: str) -> Optional[int]:
    """Verify refresh token and return user_id"""
    token_data = refresh_tokens.get(token)
    if not token_data:
        return None
    
    if datetime.utcnow() > token_data["expires_at"]:
        del refresh_tokens[token]
        return None
    
    return token_data["user_id"]

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """Get user by email"""
    result = await db.execute(select(User).where(User.email == email))
    return result.scalar_one_or_none()

async def authenticate_user(db: AsyncSession, email: str, password: str) -> Optional[User]:
    """Authenticate user with email and password"""
    user = await get_user_by_email(db, email)
    if not user:
        return None
    if not pwd_context.verify(password, user.hashed_password):
        return None
    return user

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = await get_user_by_email(db, email=email)
    if user is None:
        raise credentials_exception
    
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

@router.post("/register", response_model=UserResponse)
async def register_user(
    registration: UserRegistration,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Register a new user with enhanced features"""
    try:
        user = await user_manager.register_user(registration, db)
        
        # Send welcome email in background
        background_tasks.add_task(
            send_welcome_email,
            user.email,
            f"{user.first_name} {user.last_name}"
        )
        
        logger.info(
            "User registered successfully",
            email=registration.email,
            company=registration.company_name,
            user_id=user.id
        )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Registration failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=EnhancedToken)
async def enhanced_login(
    login_request: dict,
    db: AsyncSession = Depends(get_db)
):
    """Enhanced login with refresh token"""
    email = login_request.get("email")
    password = login_request.get("password")
    
    user = await authenticate_user(db, email, password)
    if not user:
        logger.warning("Failed login attempt", email=email)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Account is inactive"
        )
    
    # Update last login
    user.last_login = datetime.utcnow()
    await db.commit()
    
    # Create tokens
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    refresh_token = create_refresh_token(user.id)
    
    # Get user profile
    user_profile = await user_manager.get_user_by_id(user.id, db)
    
    logger.info("User logged in successfully", user_id=user.id, email=user.email)
    
    return EnhancedToken(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=user_profile
    )

@router.post("/refresh", response_model=EnhancedToken)
async def refresh_access_token(
    refresh_request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token"""
    user_id = verify_refresh_token(refresh_request.refresh_token)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Get user
    user = await db.get(User, user_id)
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Create new tokens
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    new_refresh_token = create_refresh_token(user.id)
    
    # Invalidate old refresh token
    del refresh_tokens[refresh_request.refresh_token]
    
    # Get user profile
    user_profile = await user_manager.get_user_by_id(user.id, db)
    
    return EnhancedToken(
        access_token=access_token,
        refresh_token=new_refresh_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=user_profile
    )

@router.post("/logout")
async def logout(
    refresh_token: str,
    current_user: User = Depends(get_current_active_user)
):
    """Logout user and invalidate refresh token"""
    if refresh_token in refresh_tokens:
        del refresh_tokens[refresh_token]
    
    logger.info("User logged out", user_id=current_user.id)
    return {"message": "Logged out successfully"}

@router.post("/change-password")
async def change_password(
    password_change: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Change user password"""
    # Verify current password
    if not pwd_context.verify(password_change.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    current_user.hashed_password = pwd_context.hash(password_change.new_password)
    await db.commit()
    
    logger.info("Password changed", user_id=current_user.id)
    return {"message": "Password changed successfully"}

@router.post("/invite-user")
async def invite_team_member(
    invitation: UserInvitation,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Invite a team member (admin only)"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can invite team members"
        )
    
    # Check if user already exists
    existing_user = await get_user_by_email(db, invitation.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )
    
    # Generate temporary password
    temp_password = secrets.token_urlsafe(12)
    
    # Create user
    registration = UserRegistration(
        email=invitation.email,
        password=temp_password,
        first_name=invitation.first_name,
        last_name=invitation.last_name,
        company_name=""  # Will use current user's client
    )
    
    # Create user without creating new client
    hashed_password = pwd_context.hash(temp_password)
    user = User(
        email=invitation.email,
        hashed_password=hashed_password,
        first_name=invitation.first_name,
        last_name=invitation.last_name,
        role=invitation.role,
        client_id=current_user.client_id,
        is_active=True,
        is_verified=False
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    # Send invitation email
    background_tasks.add_task(
        send_invitation_email,
        invitation.email,
        f"{invitation.first_name} {invitation.last_name}",
        temp_password
    )
    
    logger.info(
        "Team member invited",
        inviter_id=current_user.id,
        invitee_email=invitation.email
    )
    
    return {"message": "Invitation sent successfully"}

@router.get("/team", response_model=List[TeamMember])
async def get_team_members(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get team members for current client"""
    result = await db.execute(
        select(User).where(User.client_id == current_user.client_id)
    )
    users = result.scalars().all()
    
    team_members = []
    for user in users:
        team_members.append(TeamMember(
            id=user.id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            role=user.role,
            is_active=user.is_active,
            last_login=user.last_login,
            created_at=user.created_at
        ))
    
    return team_members

async def send_welcome_email(email: str, name: str):
    """Send welcome email to new user"""
    try:
        recipient = EmailRecipient(
            email=email,
            name=name,
            variables={
                "name": name,
                "login_url": f"{settings.FRONTEND_URL}/login",
                "dashboard_url": f"{settings.FRONTEND_URL}/dashboard"
            }
        )
        
        await email_manager.send_email(recipient, "welcome")
        
    except Exception as e:
        logger.error("Failed to send welcome email", error=str(e), email=email)

async def send_invitation_email(email: str, name: str, temp_password: str):
    """Send invitation email to team member"""
    try:
        recipient = EmailRecipient(
            email=email,
            name=name,
            variables={
                "name": name,
                "temp_password": temp_password,
                "login_url": f"{settings.FRONTEND_URL}/login",
                "change_password_url": f"{settings.FRONTEND_URL}/change-password"
            }
        )
        
        await email_manager.send_email(recipient, "team_invitation")
        
    except Exception as e:
        logger.error("Failed to send invitation email", error=str(e), email=email)
