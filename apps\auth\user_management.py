"""
Complete User Management System for Omnify Marketing Cloud
"""
import asyncio
from typing import Optional, List
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from passlib.context import Crypt<PERSON>ontext
from passlib.hash import bcrypt
import secrets
import structlog
from pydantic import BaseModel, EmailStr, Field, validator

from apps.core.database import User, Client, get_db
from lib.integrations.email_service import email_manager, EmailRecipient
from apps.core.config import settings

logger = structlog.get_logger()

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserRegistration(BaseModel):
    """User registration request"""
    email: EmailStr
    password: str = Field(..., min_length=8)
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    company_name: str = Field(..., min_length=1, max_length=255)
    phone: Optional[str] = None
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class UserResponse(BaseModel):
    """User response model"""
    id: int
    email: str
    first_name: str
    last_name: str
    role: str
    is_active: bool
    is_verified: bool
    created_at: datetime
    client_id: Optional[int] = None
    client_name: Optional[str] = None

class PasswordReset(BaseModel):
    """Password reset request"""
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    """Password reset confirmation"""
    token: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        return v

class UserUpdate(BaseModel):
    """User update request"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None

class UserManager:
    """Manages user operations"""
    
    def __init__(self):
        self.reset_tokens = {}  # In production, use Redis
    
    async def register_user(
        self, 
        registration: UserRegistration, 
        db: AsyncSession
    ) -> UserResponse:
        """Register a new user and create associated client"""
        
        # Check if user already exists
        existing_user = await db.execute(
            select(User).where(User.email == registration.email)
        )
        if existing_user.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create client first
        client = Client(
            name=registration.company_name,
            email=registration.email,
            status="trial"  # Start with trial status
        )
        db.add(client)
        await db.flush()  # Get client ID
        
        # Hash password
        hashed_password = pwd_context.hash(registration.password)
        
        # Create user
        user = User(
            email=registration.email,
            hashed_password=hashed_password,
            first_name=registration.first_name,
            last_name=registration.last_name,
            role="admin",  # First user is admin of their client
            client_id=client.id,
            is_active=True,
            is_verified=False  # Require email verification
        )
        
        db.add(user)
        await db.commit()
        await db.refresh(user)
        
        # Send verification email
        await self._send_verification_email(user)
        
        logger.info(
            "User registered successfully",
            user_id=user.id,
            email=user.email,
            client_id=client.id
        )
        
        return UserResponse(
            id=user.id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            client_id=client.id,
            client_name=client.name
        )
    
    async def verify_email(self, token: str, db: AsyncSession) -> bool:
        """Verify user email with token"""
        # In production, implement proper token verification
        # For now, simplified implementation
        
        user = await db.execute(
            select(User).where(User.email == token)  # Simplified
        )
        user = user.scalar_one_or_none()
        
        if user:
            user.is_verified = True
            await db.commit()
            
            logger.info("Email verified", user_id=user.id)
            return True
        
        return False
    
    async def initiate_password_reset(
        self, 
        email: str, 
        db: AsyncSession
    ) -> bool:
        """Initiate password reset process"""
        
        user = await db.execute(
            select(User).where(User.email == email)
        )
        user = user.scalar_one_or_none()
        
        if not user:
            # Don't reveal if email exists
            return True
        
        # Generate reset token
        reset_token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(hours=1)
        
        # Store token (in production, use Redis with TTL)
        self.reset_tokens[reset_token] = {
            "user_id": user.id,
            "expires_at": expires_at
        }
        
        # Send reset email
        await self._send_password_reset_email(user, reset_token)
        
        logger.info("Password reset initiated", user_id=user.id)
        return True
    
    async def reset_password(
        self, 
        token: str, 
        new_password: str, 
        db: AsyncSession
    ) -> bool:
        """Reset password with token"""
        
        token_data = self.reset_tokens.get(token)
        if not token_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        if datetime.utcnow() > token_data["expires_at"]:
            del self.reset_tokens[token]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Reset token has expired"
            )
        
        # Get user
        user = await db.execute(
            select(User).where(User.id == token_data["user_id"])
        )
        user = user.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update password
        user.hashed_password = pwd_context.hash(new_password)
        await db.commit()
        
        # Remove used token
        del self.reset_tokens[token]
        
        logger.info("Password reset completed", user_id=user.id)
        return True
    
    async def update_user(
        self, 
        user_id: int, 
        update_data: UserUpdate, 
        db: AsyncSession
    ) -> UserResponse:
        """Update user information"""
        
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update fields
        if update_data.first_name is not None:
            user.first_name = update_data.first_name
        if update_data.last_name is not None:
            user.last_name = update_data.last_name
        
        await db.commit()
        await db.refresh(user)
        
        # Get client info
        client = None
        if user.client_id:
            client_result = await db.execute(
                select(Client).where(Client.id == user.client_id)
            )
            client = client_result.scalar_one_or_none()
        
        return UserResponse(
            id=user.id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            client_id=user.client_id,
            client_name=client.name if client else None
        )
    
    async def get_user_by_id(self, user_id: int, db: AsyncSession) -> Optional[UserResponse]:
        """Get user by ID"""
        
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar_one_or_none()
        
        if not user:
            return None
        
        # Get client info
        client = None
        if user.client_id:
            client_result = await db.execute(
                select(Client).where(Client.id == user.client_id)
            )
            client = client_result.scalar_one_or_none()
        
        return UserResponse(
            id=user.id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            client_id=user.client_id,
            client_name=client.name if client else None
        )
    
    async def list_users(
        self, 
        client_id: Optional[int] = None, 
        db: AsyncSession = None
    ) -> List[UserResponse]:
        """List users with optional client filter"""
        
        query = select(User)
        if client_id:
            query = query.where(User.client_id == client_id)
        
        result = await db.execute(query)
        users = result.scalars().all()
        
        user_responses = []
        for user in users:
            # Get client info
            client = None
            if user.client_id:
                client_result = await db.execute(
                    select(Client).where(Client.id == user.client_id)
                )
                client = client_result.scalar_one_or_none()
            
            user_responses.append(UserResponse(
                id=user.id,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                role=user.role,
                is_active=user.is_active,
                is_verified=user.is_verified,
                created_at=user.created_at,
                client_id=user.client_id,
                client_name=client.name if client else None
            ))
        
        return user_responses
    
    async def deactivate_user(self, user_id: int, db: AsyncSession) -> bool:
        """Deactivate a user"""
        
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar_one_or_none()
        
        if not user:
            return False
        
        user.is_active = False
        await db.commit()
        
        logger.info("User deactivated", user_id=user.id)
        return True
    
    async def _send_verification_email(self, user: User):
        """Send email verification"""
        try:
            recipient = EmailRecipient(
                email=user.email,
                name=f"{user.first_name} {user.last_name}",
                variables={
                    "first_name": user.first_name,
                    "verification_link": f"{settings.FRONTEND_URL}/verify-email?token={user.email}",
                    "company_name": "Omnify Marketing Cloud"
                }
            )
            
            await email_manager.send_email(recipient, "email_verification")
            
        except Exception as e:
            logger.error("Failed to send verification email", error=str(e), user_id=user.id)
    
    async def _send_password_reset_email(self, user: User, reset_token: str):
        """Send password reset email"""
        try:
            recipient = EmailRecipient(
                email=user.email,
                name=f"{user.first_name} {user.last_name}",
                variables={
                    "first_name": user.first_name,
                    "reset_link": f"{settings.FRONTEND_URL}/reset-password?token={reset_token}",
                    "company_name": "Omnify Marketing Cloud"
                }
            )
            
            await email_manager.send_email(recipient, "password_reset")
            
        except Exception as e:
            logger.error("Failed to send password reset email", error=str(e), user_id=user.id)

# Global user manager instance
user_manager = UserManager()
