@echo off
REM GitHub Repository Setup Script for Omnify Marketing Cloud (Windows)
REM Connects local repository to GitHub and pushes all code

echo 🚀 Setting up Omnify Marketing Cloud GitHub Repository...
echo ============================================================

REM Check if we're in the right directory
if not exist "README.md" (
    echo ❌ Error: Please run this script from the project root directory
    echo    Expected to find README.md
    pause
    exit /b 1
)

REM Check if git is installed
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Git is not installed
    echo    Please install Git from https://git-scm.com/
    pause
    exit /b 1
)

echo ✅ Project directory and Git installation verified

REM Initialize git repository if not already done
if not exist ".git" (
    echo 📦 Initializing Git repository...
    git init
    echo ✅ Git repository initialized
) else (
    echo ✅ Git repository already exists
)

REM Add GitHub remote if not already added
git remote get-url origin >nul 2>&1
if errorlevel 1 (
    echo 🔗 Adding GitHub remote...
    git remote add origin https://github.com/ssvgopal/omnify.git
    echo ✅ GitHub remote added
) else (
    echo ✅ GitHub remote already configured
    git remote -v
)

REM Check if there are any uncommitted changes
git status --porcelain > temp_status.txt
for /f %%i in ("temp_status.txt") do set size=%%~zi
del temp_status.txt

if %size% gtr 0 (
    echo 📝 Found uncommitted changes, staging all files...
    git add .
    
    echo 💾 Creating commit...
    git commit -m "🔄 Update: Latest changes to Omnify Marketing Cloud

✅ Updated Features:
- 📚 Enhanced documentation with Smart Integration Wizard
- 🧙‍♂️ Complete integration wizard implementation  
- 📖 Updated API reference with wizard endpoints
- 🎯 Comprehensive MVP status documentation
- 🚀 Production-ready GitHub repository setup

🎉 Repository Status: Ready for collaboration and deployment"
    
    echo ✅ Changes committed successfully
) else (
    echo ✅ No uncommitted changes found
)

REM Set main branch
echo 🌿 Setting up main branch...
git branch -M main

REM Push to GitHub
echo 🚀 Pushing to GitHub repository...
echo    Repository: https://github.com/ssvgopal/omnify
echo    Branch: main

git push -u origin main
if errorlevel 0 (
    echo ✅ Successfully pushed to GitHub!
    echo.
    echo 🎉 Repository Setup Complete!
    echo ============================================================
    echo 📍 Your repository is now available at:
    echo    https://github.com/ssvgopal/omnify
    echo.
    echo 📚 Documentation is available at:
    echo    https://github.com/ssvgopal/omnify/blob/main/docs/README.md
    echo.
    echo 🚀 Quick Start Guide:
    echo    https://github.com/ssvgopal/omnify/blob/main/docs/quick-start.md
    echo.
    echo 📖 API Documentation:
    echo    https://github.com/ssvgopal/omnify/blob/main/docs/api-reference.md
    echo.
    echo 🧙‍♂️ Integration Wizard:
    echo    https://github.com/ssvgopal/omnify/blob/main/docs/integration-wizard.md
    echo.
    echo 🎯 Next Steps:
    echo    1. Clone the repository on other machines
    echo    2. Follow the Quick Start Guide to set up development environment
    echo    3. Run the MVP validation script to verify everything works
    echo    4. Start onboarding your first pilot client!
    echo.
    echo 💰 Revenue Ready: Your MVP is 100%% complete and ready for immediate pilot client onboarding!
) else (
    echo ⚠️  Push failed. This might be due to:
    echo    1. Authentication required (you may need to enter GitHub credentials)
    echo    2. Repository permissions
    echo    3. Network connectivity
    echo.
    echo 🔧 Manual push command:
    echo    git push -u origin main
    echo.
    echo 📞 If you need help, the repository is configured and ready.
    echo    You can manually push when authentication is set up.
)

echo.
echo 🎉 Omnify Marketing Cloud is now on GitHub!
echo    Ready to revolutionize marketing automation! 🚀
pause
