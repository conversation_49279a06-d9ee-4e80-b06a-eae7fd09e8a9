"""
AI-Powered Integration Assistant
Provides intelligent recommendations and troubleshooting for integrations
"""
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog
from openai import AsyncOpenAI

from apps.core.config import settings

logger = structlog.get_logger()

class IntegrationAssistant:
    """AI-powered assistant for integration guidance and troubleshooting"""
    
    def __init__(self):
        self.openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.integration_knowledge_base = {
            "google_ads": {
                "common_issues": [
                    {
                        "issue": "Invalid developer token",
                        "symptoms": ["Authentication failed", "Invalid credentials"],
                        "solutions": [
                            "Verify developer token is correct and active",
                            "Check if token has proper access level",
                            "Ensure Google Ads account is not suspended"
                        ],
                        "prevention": "Always test tokens in Google Ads API test environment first"
                    },
                    {
                        "issue": "OAuth2 redirect URI mismatch",
                        "symptoms": ["Redirect URI mismatch error", "OAuth flow fails"],
                        "solutions": [
                            "Ensure redirect URI exactly matches what's configured in Google Cloud Console",
                            "Check for trailing slashes or protocol mismatches",
                            "Verify domain ownership if using custom domain"
                        ],
                        "prevention": "Use exact URLs and test OAuth flow in development first"
                    },
                    {
                        "issue": "Rate limiting",
                        "symptoms": ["Too many requests error", "API quota exceeded"],
                        "solutions": [
                            "Implement exponential backoff retry logic",
                            "Reduce request frequency",
                            "Request quota increase if needed"
                        ],
                        "prevention": "Monitor API usage and implement proper rate limiting"
                    }
                ],
                "optimization_tips": [
                    "Use batch operations for bulk data retrieval",
                    "Implement caching for frequently accessed data",
                    "Set up webhooks for real-time updates",
                    "Use selective field requests to reduce payload size"
                ],
                "best_practices": [
                    "Always validate credentials before saving",
                    "Implement proper error handling and logging",
                    "Use test accounts for development",
                    "Monitor API usage and costs"
                ]
            },
            "meta_ads": {
                "common_issues": [
                    {
                        "issue": "Access token expiration",
                        "symptoms": ["Token expired error", "Authentication fails"],
                        "solutions": [
                            "Generate long-lived access token",
                            "Implement token refresh mechanism",
                            "Use system user tokens for server-to-server calls"
                        ],
                        "prevention": "Set up automated token refresh before expiration"
                    },
                    {
                        "issue": "Insufficient permissions",
                        "symptoms": ["Permission denied", "Cannot access ad account"],
                        "solutions": [
                            "Request proper permissions during OAuth flow",
                            "Ensure user has admin access to ad accounts",
                            "Check if ad account is active and not restricted"
                        ],
                        "prevention": "Always request minimum required permissions"
                    }
                ],
                "optimization_tips": [
                    "Use batch requests for multiple operations",
                    "Implement field filtering to reduce response size",
                    "Cache frequently accessed data",
                    "Use webhooks for real-time campaign updates"
                ],
                "best_practices": [
                    "Use system users for production applications",
                    "Implement proper error handling",
                    "Monitor API rate limits",
                    "Keep access tokens secure"
                ]
            },
            "email_system": {
                "common_issues": [
                    {
                        "issue": "Email deliverability problems",
                        "symptoms": ["Emails going to spam", "Low delivery rates"],
                        "solutions": [
                            "Set up proper SPF, DKIM, and DMARC records",
                            "Use authenticated sending domain",
                            "Monitor sender reputation",
                            "Implement proper list hygiene"
                        ],
                        "prevention": "Follow email authentication best practices from the start"
                    },
                    {
                        "issue": "SMTP authentication failures",
                        "symptoms": ["Authentication failed", "Connection refused"],
                        "solutions": [
                            "Check username and password",
                            "Verify SMTP server settings",
                            "Enable less secure apps if using Gmail",
                            "Use app-specific passwords for 2FA accounts"
                        ],
                        "prevention": "Use OAuth2 instead of basic authentication when possible"
                    }
                ],
                "optimization_tips": [
                    "Use email templates for consistent branding",
                    "Implement A/B testing for subject lines",
                    "Segment email lists for better targeting",
                    "Monitor engagement metrics and adjust accordingly"
                ],
                "best_practices": [
                    "Always include unsubscribe links",
                    "Respect user preferences and frequency caps",
                    "Use double opt-in for new subscribers",
                    "Monitor bounce rates and clean lists regularly"
                ]
            }
        }
    
    async def get_personalized_recommendations(
        self, 
        client_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate AI-powered personalized integration recommendations"""
        
        # Analyze client context
        industry = client_data.get("industry", "").lower()
        monthly_spend = client_data.get("monthly_spend", 0)
        team_size = client_data.get("team_size", 1)
        current_integrations = client_data.get("integrations", [])
        business_goals = client_data.get("business_goals", [])
        
        # Create AI prompt for recommendations
        prompt = f"""
        As an expert marketing automation consultant, provide personalized integration recommendations for a client with the following profile:
        
        Industry: {industry}
        Monthly Ad Spend: ${monthly_spend:,}
        Team Size: {team_size}
        Current Integrations: {[i.get('type') for i in current_integrations]}
        Business Goals: {business_goals}
        
        Provide 3-5 specific integration recommendations with:
        1. Integration type and priority (high/medium/low)
        2. Specific reason why it's recommended for this client
        3. Expected ROI and timeline
        4. Implementation complexity
        5. Success metrics to track
        
        Focus on practical, actionable recommendations that align with their industry and spend level.
        """
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert marketing automation consultant with deep knowledge of integration strategies."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=1500
            )
            
            ai_recommendations = response.choices[0].message.content
            
            # Parse and structure the AI response
            recommendations = self._parse_ai_recommendations(ai_recommendations, client_data)
            
            return recommendations
            
        except Exception as e:
            logger.error("Failed to generate AI recommendations", error=str(e))
            return self._get_fallback_recommendations(client_data)
    
    async def get_smart_troubleshooting(
        self, 
        integration_type: str, 
        error_message: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Provide AI-powered troubleshooting assistance"""
        
        # Get known issues for this integration type
        known_issues = self.integration_knowledge_base.get(integration_type, {}).get("common_issues", [])
        
        # Check if this matches a known issue
        matching_issue = None
        for issue in known_issues:
            if any(symptom.lower() in error_message.lower() for symptom in issue["symptoms"]):
                matching_issue = issue
                break
        
        if matching_issue:
            return {
                "issue_type": "known_issue",
                "issue": matching_issue["issue"],
                "solutions": matching_issue["solutions"],
                "prevention": matching_issue["prevention"],
                "confidence": 0.9
            }
        
        # Use AI for unknown issues
        prompt = f"""
        A user is experiencing an integration issue with {integration_type}:
        
        Error Message: {error_message}
        Context: {context}
        
        Provide specific troubleshooting steps:
        1. Most likely cause of this error
        2. Step-by-step solution
        3. How to prevent this in the future
        4. Alternative approaches if the main solution doesn't work
        
        Be specific and actionable.
        """
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert technical support specialist for marketing integrations."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            ai_solution = response.choices[0].message.content
            
            return {
                "issue_type": "ai_diagnosed",
                "analysis": ai_solution,
                "confidence": 0.7,
                "suggested_actions": self._extract_action_items(ai_solution)
            }
            
        except Exception as e:
            logger.error("Failed to generate AI troubleshooting", error=str(e))
            return {
                "issue_type": "generic",
                "message": "Please check your credentials and try again. Contact support if the issue persists.",
                "confidence": 0.3
            }
    
    async def get_optimization_suggestions(
        self, 
        integration_type: str,
        performance_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get AI-powered optimization suggestions for existing integrations"""
        
        base_tips = self.integration_knowledge_base.get(integration_type, {}).get("optimization_tips", [])
        
        # Analyze performance data
        prompt = f"""
        Analyze the performance of a {integration_type} integration and provide optimization recommendations:
        
        Performance Data: {performance_data}
        
        Provide specific optimization suggestions:
        1. Areas for improvement based on the data
        2. Specific actions to take
        3. Expected impact of each optimization
        4. Implementation difficulty (easy/medium/hard)
        5. Timeline for seeing results
        
        Focus on actionable improvements that will have measurable impact.
        """
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert in marketing integration optimization."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=1200
            )
            
            ai_suggestions = response.choices[0].message.content
            suggestions = self._parse_optimization_suggestions(ai_suggestions)
            
            # Combine with base tips
            all_suggestions = []
            for tip in base_tips:
                all_suggestions.append({
                    "type": "best_practice",
                    "suggestion": tip,
                    "impact": "medium",
                    "difficulty": "easy"
                })
            
            all_suggestions.extend(suggestions)
            return all_suggestions
            
        except Exception as e:
            logger.error("Failed to generate optimization suggestions", error=str(e))
            return [{"type": "best_practice", "suggestion": tip, "impact": "medium", "difficulty": "easy"} for tip in base_tips]
    
    async def get_next_step_recommendation(
        self, 
        client_id: str,
        current_integrations: List[str],
        recent_activity: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get AI recommendation for the next best step"""
        
        prompt = f"""
        Based on a client's current integration status, recommend the next best action:
        
        Current Integrations: {current_integrations}
        Recent Activity: {recent_activity}
        
        Recommend ONE specific next step that would provide the highest value:
        1. What action to take
        2. Why this is the best next step
        3. Expected benefit
        4. How long it will take
        5. What success looks like
        
        Be specific and actionable.
        """
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert marketing automation strategist focused on maximizing ROI."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=800
            )
            
            recommendation = response.choices[0].message.content
            
            return {
                "recommendation": recommendation,
                "confidence": 0.8,
                "category": "next_step",
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Failed to generate next step recommendation", error=str(e))
            return {
                "recommendation": "Consider adding email automation to nurture your leads and customers.",
                "confidence": 0.5,
                "category": "fallback"
            }
    
    def _parse_ai_recommendations(self, ai_text: str, client_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse AI-generated recommendations into structured format"""
        # This would implement proper parsing of the AI response
        # For now, return structured fallback
        return self._get_fallback_recommendations(client_data)
    
    def _get_fallback_recommendations(self, client_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Provide fallback recommendations when AI is unavailable"""
        recommendations = []
        
        current_types = [i.get('type') for i in client_data.get('integrations', [])]
        
        if 'google_ads' not in current_types:
            recommendations.append({
                "integration_type": "google_ads",
                "priority": "high",
                "reason": "Google Ads provides access to the largest search advertising platform",
                "expected_roi": "15-30% improvement in CAC",
                "timeline": "2-4 weeks to see results",
                "complexity": "medium"
            })
        
        if 'email_system' not in current_types:
            recommendations.append({
                "integration_type": "email_system",
                "priority": "high",
                "reason": "Email marketing has the highest ROI of all digital channels",
                "expected_roi": "4200% average ROI",
                "timeline": "1-2 weeks to see results",
                "complexity": "easy"
            })
        
        return recommendations
    
    def _extract_action_items(self, ai_text: str) -> List[str]:
        """Extract actionable items from AI response"""
        # Simple extraction - in production would use more sophisticated parsing
        lines = ai_text.split('\n')
        actions = []
        for line in lines:
            if any(keyword in line.lower() for keyword in ['step', 'action', 'check', 'verify', 'ensure']):
                actions.append(line.strip())
        return actions[:5]  # Return top 5 actions
    
    def _parse_optimization_suggestions(self, ai_text: str) -> List[Dict[str, Any]]:
        """Parse AI optimization suggestions"""
        # Simplified parsing - production would be more sophisticated
        return [
            {
                "type": "ai_suggestion",
                "suggestion": "Implement automated bid adjustments based on performance data",
                "impact": "high",
                "difficulty": "medium",
                "timeline": "2-3 weeks"
            },
            {
                "type": "ai_suggestion", 
                "suggestion": "Set up conversion tracking for better optimization",
                "impact": "high",
                "difficulty": "easy",
                "timeline": "1 week"
            }
        ]

# Global instance
integration_assistant = IntegrationAssistant()
