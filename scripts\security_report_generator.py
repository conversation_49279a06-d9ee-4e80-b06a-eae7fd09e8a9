#!/usr/bin/env python3
"""
Security Report Generator for CI/CD Pipeline
Generates comprehensive security scan summaries
"""
import json
import os
import sys
from datetime import datetime

def load_json_safe(filename):
    """Safely load JSON file"""
    try:
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Warning: Could not load {filename}: {e}")
    return {}

def main():
    """Generate security report summary"""
    print("📊 Generating security report summary...")
    
    # Load all reports
    bandit = load_json_safe('bandit-report.json')
    safety = load_json_safe('safety-report.json')
    semgrep = load_json_safe('semgrep-secrets.json')
    checkov_tf = load_json_safe('checkov-terraform.json')
    checkov_docker = load_json_safe('checkov-docker.json')
    
    # Generate summary
    summary = {
        'scan_timestamp': datetime.utcnow().isoformat(),
        'bandit_issues': len(bandit.get('results', [])),
        'safety_vulnerabilities': len(safety.get('vulnerabilities', [])),
        'secrets_found': len(semgrep.get('results', [])),
        'terraform_issues': len(checkov_tf.get('results', {}).get('failed_checks', [])),
        'docker_issues': len(checkov_docker.get('results', {}).get('failed_checks', [])),
        'overall_status': 'PASS'
    }
    
    # Determine overall status
    total_critical = summary['safety_vulnerabilities'] + summary['secrets_found']
    if total_critical > 0:
        summary['overall_status'] = 'FAIL'
    elif summary['bandit_issues'] > 10 or summary['terraform_issues'] > 5:
        summary['overall_status'] = 'WARNING'
    
    # Save summary
    with open('security-summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Print summary
    print('🛡️ Security Scan Summary:')
    print(f"  Bandit Issues: {summary['bandit_issues']}")
    print(f"  Safety Vulnerabilities: {summary['safety_vulnerabilities']}")
    print(f"  Secrets Found: {summary['secrets_found']}")
    print(f"  Terraform Issues: {summary['terraform_issues']}")
    print(f"  Docker Issues: {summary['docker_issues']}")
    print(f"  Overall Status: {summary['overall_status']}")
    
    # Exit with appropriate code
    if summary['overall_status'] == 'FAIL':
        print('❌ Critical security issues found!')
        sys.exit(1)
    elif summary['overall_status'] == 'WARNING':
        print('⚠️ Security warnings found - review recommended')
        sys.exit(0)  # Don't fail build for warnings
    else:
        print('✅ No critical security issues found')
        sys.exit(0)

if __name__ == "__main__":
    main()
