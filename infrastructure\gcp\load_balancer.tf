# GCP Load Balancer and Networking Configuration
# Implements Cloud Load Balancing, Cloud CDN, and VPC security

# VPC Network
resource "google_compute_network" "omnify_vpc" {
  name                    = "omnify-vpc-${var.environment}"
  auto_create_subnetworks = false
  routing_mode           = "REGIONAL"

  description = "Omnify Marketing Cloud VPC for ${var.environment}"
}

# Subnets
resource "google_compute_subnetwork" "app_subnet" {
  name          = "omnify-app-subnet-${var.environment}"
  ip_cidr_range = "********/24"
  region        = var.region
  network       = google_compute_network.omnify_vpc.id

  secondary_ip_range {
    range_name    = "pods"
    ip_cidr_range = "********/16"
  }

  secondary_ip_range {
    range_name    = "services"
    ip_cidr_range = "********/16"
  }

  private_ip_google_access = true
}

resource "google_compute_subnetwork" "database_subnet" {
  name          = "omnify-db-subnet-${var.environment}"
  ip_cidr_range = "********/24"
  region        = var.region
  network       = google_compute_network.omnify_vpc.id

  private_ip_google_access = true
}

# Firewall Rules
resource "google_compute_firewall" "allow_internal" {
  name    = "omnify-allow-internal-${var.environment}"
  network = google_compute_network.omnify_vpc.name

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "icmp"
  }

  source_ranges = ["10.0.0.0/16"]
  target_tags   = ["omnify-internal"]
}

resource "google_compute_firewall" "allow_http_https" {
  name    = "omnify-allow-http-https-${var.environment}"
  network = google_compute_network.omnify_vpc.name

  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["omnify-web"]
}

resource "google_compute_firewall" "allow_health_check" {
  name    = "omnify-allow-health-check-${var.environment}"
  network = google_compute_network.omnify_vpc.name

  allow {
    protocol = "tcp"
    ports    = ["8000"]
  }

  source_ranges = ["130.211.0.0/22", "35.191.0.0/16"]  # Google health check ranges
  target_tags   = ["omnify-backend"]
}

resource "google_compute_firewall" "allow_ssh" {
  name    = "omnify-allow-ssh-${var.environment}"
  network = google_compute_network.omnify_vpc.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["35.235.240.0/20"]  # IAP range
  target_tags   = ["omnify-ssh"]
}

# Cloud NAT for outbound internet access
resource "google_compute_router" "omnify_router" {
  name    = "omnify-router-${var.environment}"
  region  = var.region
  network = google_compute_network.omnify_vpc.id
}

resource "google_compute_router_nat" "omnify_nat" {
  name                               = "omnify-nat-${var.environment}"
  router                             = google_compute_router.omnify_router.name
  region                             = var.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}

# Global IP Address for Load Balancer
resource "google_compute_global_address" "omnify_ip" {
  name         = "omnify-global-ip-${var.environment}"
  address_type = "EXTERNAL"
}

# SSL Certificate
resource "google_compute_managed_ssl_certificate" "omnify_ssl" {
  name = "omnify-ssl-cert-${var.environment}"

  managed {
    domains = var.environment == "prod" ? ["api.omnify.com", "app.omnify.com"] : ["api-${var.environment}.omnify.com", "app-${var.environment}.omnify.com"]
  }
}

# Backend Service
resource "google_compute_backend_service" "omnify_backend" {
  name                  = "omnify-backend-${var.environment}"
  protocol              = "HTTP"
  port_name             = "http"
  load_balancing_scheme = "EXTERNAL"
  timeout_sec           = 30

  health_checks = [google_compute_health_check.omnify_health_check.id]

  backend {
    group           = google_compute_region_network_endpoint_group.omnify_neg.id
    balancing_mode  = "UTILIZATION"
    capacity_scaler = 1.0
  }

  cdn_policy {
    cache_mode                   = "CACHE_ALL_STATIC"
    signed_url_cache_max_age_sec = 7200
    default_ttl                  = 3600
    max_ttl                      = 86400
    negative_caching             = true

    negative_caching_policy {
      code = 404
      ttl  = 120
    }

    cache_key_policy {
      include_host         = true
      include_protocol     = true
      include_query_string = false
    }
  }

  log_config {
    enable      = true
    sample_rate = 1.0
  }
}

# Health Check
resource "google_compute_health_check" "omnify_health_check" {
  name               = "omnify-health-check-${var.environment}"
  check_interval_sec = 30
  timeout_sec        = 10
  healthy_threshold  = 2
  unhealthy_threshold = 3

  http_health_check {
    port         = 8000
    request_path = "/health"
  }
}

# Network Endpoint Group for Cloud Run
resource "google_compute_region_network_endpoint_group" "omnify_neg" {
  name                  = "omnify-neg-${var.environment}"
  network_endpoint_type = "SERVERLESS"
  region                = var.region

  cloud_run {
    service = var.cloud_run_service_name
  }
}

# URL Map
resource "google_compute_url_map" "omnify_url_map" {
  name            = "omnify-url-map-${var.environment}"
  default_service = google_compute_backend_service.omnify_backend.id

  host_rule {
    hosts        = var.environment == "prod" ? ["api.omnify.com"] : ["api-${var.environment}.omnify.com"]
    path_matcher = "api-matcher"
  }

  path_matcher {
    name            = "api-matcher"
    default_service = google_compute_backend_service.omnify_backend.id

    path_rule {
      paths   = ["/api/*"]
      service = google_compute_backend_service.omnify_backend.id
    }

    path_rule {
      paths   = ["/health", "/docs", "/redoc"]
      service = google_compute_backend_service.omnify_backend.id
    }
  }
}

# HTTPS Proxy
resource "google_compute_target_https_proxy" "omnify_https_proxy" {
  name             = "omnify-https-proxy-${var.environment}"
  url_map          = google_compute_url_map.omnify_url_map.id
  ssl_certificates = [google_compute_managed_ssl_certificate.omnify_ssl.id]
}

# HTTP Proxy (for redirect)
resource "google_compute_target_http_proxy" "omnify_http_proxy" {
  name    = "omnify-http-proxy-${var.environment}"
  url_map = google_compute_url_map.omnify_redirect_map.id
}

# URL Map for HTTP to HTTPS redirect
resource "google_compute_url_map" "omnify_redirect_map" {
  name = "omnify-redirect-map-${var.environment}"

  default_url_redirect {
    https_redirect         = true
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
    strip_query            = false
  }
}

# Global Forwarding Rules
resource "google_compute_global_forwarding_rule" "omnify_https_forwarding_rule" {
  name                  = "omnify-https-forwarding-rule-${var.environment}"
  ip_protocol           = "TCP"
  load_balancing_scheme = "EXTERNAL"
  port_range            = "443"
  target                = google_compute_target_https_proxy.omnify_https_proxy.id
  ip_address            = google_compute_global_address.omnify_ip.id
}

resource "google_compute_global_forwarding_rule" "omnify_http_forwarding_rule" {
  name                  = "omnify-http-forwarding-rule-${var.environment}"
  ip_protocol           = "TCP"
  load_balancing_scheme = "EXTERNAL"
  port_range            = "80"
  target                = google_compute_target_http_proxy.omnify_http_proxy.id
  ip_address            = google_compute_global_address.omnify_ip.id
}

# Cloud Armor Security Policy
resource "google_compute_security_policy" "omnify_security_policy" {
  name = "omnify-security-policy-${var.environment}"

  # Default rule
  rule {
    action   = "allow"
    priority = "2147483647"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    description = "Default allow rule"
  }

  # Rate limiting rule
  rule {
    action   = "rate_based_ban"
    priority = "1000"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    rate_limit_options {
      conform_action = "allow"
      exceed_action  = "deny(429)"
      enforce_on_key = "IP"
      rate_limit_threshold {
        count        = 100
        interval_sec = 60
      }
      ban_duration_sec = 600
    }
    description = "Rate limiting rule"
  }

  # Block common attack patterns
  rule {
    action   = "deny(403)"
    priority = "900"
    match {
      expr {
        expression = "origin.region_code == 'CN' || origin.region_code == 'RU'"
      }
    }
    description = "Block traffic from certain regions"
  }

  adaptive_protection_config {
    layer_7_ddos_defense_config {
      enable = true
    }
  }
}

# Apply security policy to backend service
resource "google_compute_backend_service" "omnify_backend_with_security" {
  name                  = "omnify-backend-secure-${var.environment}"
  protocol              = "HTTP"
  port_name             = "http"
  load_balancing_scheme = "EXTERNAL"
  timeout_sec           = 30
  security_policy       = google_compute_security_policy.omnify_security_policy.id

  health_checks = [google_compute_health_check.omnify_health_check.id]

  backend {
    group           = google_compute_region_network_endpoint_group.omnify_neg.id
    balancing_mode  = "UTILIZATION"
    capacity_scaler = 1.0
  }

  cdn_policy {
    cache_mode                   = "CACHE_ALL_STATIC"
    signed_url_cache_max_age_sec = 7200
    default_ttl                  = 3600
    max_ttl                      = 86400
    negative_caching             = true
  }

  log_config {
    enable      = true
    sample_rate = 1.0
  }
}

# Variables
variable "region" {
  description = "GCP region"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "cloud_run_service_name" {
  description = "Name of the Cloud Run service"
  type        = string
  default     = "omnify-api"
}

# Outputs
output "vpc_network_id" {
  description = "ID of the VPC network"
  value       = google_compute_network.omnify_vpc.id
}

output "app_subnet_id" {
  description = "ID of the application subnet"
  value       = google_compute_subnetwork.app_subnet.id
}

output "database_subnet_id" {
  description = "ID of the database subnet"
  value       = google_compute_subnetwork.database_subnet.id
}

output "global_ip_address" {
  description = "Global IP address for the load balancer"
  value       = google_compute_global_address.omnify_ip.address
}

output "load_balancer_url" {
  description = "URL of the load balancer"
  value       = "https://${google_compute_global_address.omnify_ip.address}"
}

output "ssl_certificate_id" {
  description = "ID of the SSL certificate"
  value       = google_compute_managed_ssl_certificate.omnify_ssl.id
}
