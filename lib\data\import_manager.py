"""
Client Data Import and Migration System for Omnify Marketing Cloud
Handles bulk import of historical data from various sources
"""
import asyncio
import csv
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path
import structlog
from pydantic import BaseModel, Field, validator
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from apps.core.database import (
    Client, Campaign, CampaignMetric, CustomerProfile, 
    RetentionAction, get_db
)
from lib.connectors.google_ads import GoogleAdsConnector
from lib.connectors.meta_ads import MetaAdsConnector
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class ImportJob(BaseModel):
    """Import job configuration"""
    job_id: str
    client_id: str
    source_type: str  # "csv", "google_ads", "meta_ads", "api", "database"
    data_type: str    # "campaigns", "metrics", "customers", "all"
    
    # Source configuration
    source_config: Dict[str, Any] = Field(default_factory=dict)
    
    # Import settings
    batch_size: int = 1000
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    overwrite_existing: bool = False
    
    # Status
    status: str = "pending"  # pending, running, completed, failed
    progress: float = 0.0
    total_records: int = 0
    processed_records: int = 0
    error_count: int = 0
    
    # Timing
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Results
    imported_campaigns: int = 0
    imported_metrics: int = 0
    imported_customers: int = 0
    errors: List[str] = Field(default_factory=list)

class CampaignImportData(BaseModel):
    """Campaign import data structure"""
    campaign_id: str
    name: str
    platform: str
    budget: float
    target_cac: Optional[float] = None
    status: str = "active"

class MetricImportData(BaseModel):
    """Metric import data structure"""
    campaign_id: str
    date: datetime
    impressions: int = 0
    clicks: int = 0
    conversions: float = 0.0
    spend: float = 0.0
    revenue: Optional[float] = None
    
    @validator('date', pre=True)
    def parse_date(cls, v):
        if isinstance(v, str):
            try:
                return datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                return datetime.strptime(v, '%m/%d/%Y')
        return v

class CustomerImportData(BaseModel):
    """Customer import data structure"""
    customer_id: str
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    total_purchases: int = 0
    total_spent: float = 0.0
    last_purchase_date: Optional[datetime] = None
    email_engagement_rate: float = 0.0
    
    @validator('last_purchase_date', pre=True)
    def parse_purchase_date(cls, v):
        if isinstance(v, str) and v:
            try:
                return datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                return datetime.strptime(v, '%m/%d/%Y')
        return v

class DataImportManager:
    """Manages data import operations"""
    
    def __init__(self):
        self.active_jobs: Dict[str, ImportJob] = {}
        self.job_history: List[ImportJob] = []
    
    async def start_import_job(
        self, 
        client_id: str,
        source_type: str,
        data_type: str,
        source_config: Dict[str, Any],
        **kwargs
    ) -> ImportJob:
        """Start a new import job"""
        
        job_id = f"import_{client_id}_{int(datetime.utcnow().timestamp())}"
        
        job = ImportJob(
            job_id=job_id,
            client_id=client_id,
            source_type=source_type,
            data_type=data_type,
            source_config=source_config,
            **kwargs
        )
        
        self.active_jobs[job_id] = job
        
        # Start import in background
        asyncio.create_task(self._execute_import_job(job))
        
        logger.info(
            "Import job started",
            job_id=job_id,
            client_id=client_id,
            source_type=source_type,
            data_type=data_type
        )
        
        return job
    
    async def _execute_import_job(self, job: ImportJob):
        """Execute import job"""
        try:
            job.status = "running"
            job.started_at = datetime.utcnow()
            
            if job.source_type == "csv":
                await self._import_from_csv(job)
            elif job.source_type == "google_ads":
                await self._import_from_google_ads(job)
            elif job.source_type == "meta_ads":
                await self._import_from_meta_ads(job)
            elif job.source_type == "api":
                await self._import_from_api(job)
            else:
                raise ValueError(f"Unsupported source type: {job.source_type}")
            
            job.status = "completed"
            job.progress = 100.0
            job.completed_at = datetime.utcnow()
            
            logger.info(
                "Import job completed",
                job_id=job.job_id,
                imported_campaigns=job.imported_campaigns,
                imported_metrics=job.imported_metrics,
                imported_customers=job.imported_customers,
                error_count=job.error_count
            )
            
        except Exception as e:
            job.status = "failed"
            job.errors.append(str(e))
            job.completed_at = datetime.utcnow()
            
            logger.error(
                "Import job failed",
                job_id=job.job_id,
                error=str(e)
            )
        
        finally:
            # Move to history
            self.job_history.append(job)
            if job.job_id in self.active_jobs:
                del self.active_jobs[job.job_id]
    
    async def _import_from_csv(self, job: ImportJob):
        """Import data from CSV files"""
        file_path = job.source_config.get("file_path")
        if not file_path or not Path(file_path).exists():
            raise ValueError(f"CSV file not found: {file_path}")
        
        # Read CSV file
        df = pd.read_csv(file_path)
        job.total_records = len(df)
        
        async with get_db() as db:
            if job.data_type in ["campaigns", "all"]:
                await self._import_campaigns_from_df(job, df, db)
            
            if job.data_type in ["metrics", "all"]:
                await self._import_metrics_from_df(job, df, db)
            
            if job.data_type in ["customers", "all"]:
                await self._import_customers_from_df(job, df, db)
    
    async def _import_campaigns_from_df(self, job: ImportJob, df: pd.DataFrame, db: AsyncSession):
        """Import campaigns from DataFrame"""
        campaign_columns = ["campaign_id", "name", "platform", "budget"]
        
        if not all(col in df.columns for col in campaign_columns):
            missing = [col for col in campaign_columns if col not in df.columns]
            raise ValueError(f"Missing required columns for campaigns: {missing}")
        
        for _, row in df.iterrows():
            try:
                campaign_data = CampaignImportData(
                    campaign_id=str(row["campaign_id"]),
                    name=str(row["name"]),
                    platform=str(row["platform"]),
                    budget=float(row["budget"]),
                    target_cac=float(row.get("target_cac", 50.0)),
                    status=str(row.get("status", "active"))
                )
                
                # Check if campaign exists
                existing = await db.execute(
                    select(Campaign).where(
                        Campaign.client_id == job.client_id,
                        Campaign.campaign_id == campaign_data.campaign_id
                    )
                )
                
                if existing.scalar_one_or_none() and not job.overwrite_existing:
                    continue
                
                # Create or update campaign
                campaign = Campaign(
                    client_id=job.client_id,
                    name=campaign_data.name,
                    platform=campaign_data.platform,
                    campaign_id=campaign_data.campaign_id,
                    budget=campaign_data.budget,
                    target_cac=campaign_data.target_cac,
                    status=campaign_data.status
                )
                
                db.add(campaign)
                job.imported_campaigns += 1
                
            except Exception as e:
                job.error_count += 1
                job.errors.append(f"Campaign import error: {str(e)}")
            
            job.processed_records += 1
            job.progress = (job.processed_records / job.total_records) * 100
        
        await db.commit()
    
    async def _import_metrics_from_df(self, job: ImportJob, df: pd.DataFrame, db: AsyncSession):
        """Import metrics from DataFrame"""
        metric_columns = ["campaign_id", "date", "impressions", "clicks", "spend"]
        
        if not all(col in df.columns for col in metric_columns):
            missing = [col for col in metric_columns if col not in df.columns]
            raise ValueError(f"Missing required columns for metrics: {missing}")
        
        # Get campaign mapping
        campaigns_result = await db.execute(
            select(Campaign.id, Campaign.campaign_id).where(Campaign.client_id == job.client_id)
        )
        campaign_mapping = {cid: id for id, cid in campaigns_result.fetchall()}
        
        for _, row in df.iterrows():
            try:
                metric_data = MetricImportData(
                    campaign_id=str(row["campaign_id"]),
                    date=row["date"],
                    impressions=int(row.get("impressions", 0)),
                    clicks=int(row.get("clicks", 0)),
                    conversions=float(row.get("conversions", 0)),
                    spend=float(row.get("spend", 0)),
                    revenue=float(row.get("revenue", 0)) if pd.notna(row.get("revenue")) else None
                )
                
                # Get internal campaign ID
                internal_campaign_id = campaign_mapping.get(metric_data.campaign_id)
                if not internal_campaign_id:
                    job.errors.append(f"Campaign not found: {metric_data.campaign_id}")
                    job.error_count += 1
                    continue
                
                # Check if metric exists
                existing = await db.execute(
                    select(CampaignMetric).where(
                        CampaignMetric.campaign_id == internal_campaign_id,
                        CampaignMetric.date == metric_data.date.date()
                    )
                )
                
                if existing.scalar_one_or_none() and not job.overwrite_existing:
                    continue
                
                # Calculate derived metrics
                ctr = (metric_data.clicks / metric_data.impressions * 100) if metric_data.impressions > 0 else 0
                cpc = metric_data.spend / metric_data.clicks if metric_data.clicks > 0 else 0
                
                # Create metric
                metric = CampaignMetric(
                    campaign_id=internal_campaign_id,
                    date=metric_data.date.date(),
                    impressions=metric_data.impressions,
                    clicks=metric_data.clicks,
                    conversions=metric_data.conversions,
                    spend=metric_data.spend,
                    revenue=metric_data.revenue,
                    ctr=ctr,
                    cpc=cpc
                )
                
                db.add(metric)
                job.imported_metrics += 1
                
            except Exception as e:
                job.error_count += 1
                job.errors.append(f"Metric import error: {str(e)}")
            
            job.processed_records += 1
            job.progress = (job.processed_records / job.total_records) * 100
        
        await db.commit()
    
    async def _import_customers_from_df(self, job: ImportJob, df: pd.DataFrame, db: AsyncSession):
        """Import customers from DataFrame"""
        customer_columns = ["customer_id"]
        
        if not all(col in df.columns for col in customer_columns):
            missing = [col for col in customer_columns if col not in df.columns]
            raise ValueError(f"Missing required columns for customers: {missing}")
        
        for _, row in df.iterrows():
            try:
                customer_data = CustomerImportData(
                    customer_id=str(row["customer_id"]),
                    email=str(row.get("email", "")) if pd.notna(row.get("email")) else None,
                    first_name=str(row.get("first_name", "")) if pd.notna(row.get("first_name")) else None,
                    last_name=str(row.get("last_name", "")) if pd.notna(row.get("last_name")) else None,
                    total_purchases=int(row.get("total_purchases", 0)),
                    total_spent=float(row.get("total_spent", 0)),
                    last_purchase_date=row.get("last_purchase_date"),
                    email_engagement_rate=float(row.get("email_engagement_rate", 0))
                )
                
                # Check if customer exists
                existing = await db.execute(
                    select(CustomerProfile).where(
                        CustomerProfile.client_id == job.client_id,
                        CustomerProfile.customer_id == customer_data.customer_id
                    )
                )
                
                if existing.scalar_one_or_none() and not job.overwrite_existing:
                    continue
                
                # Calculate derived metrics
                avg_order_value = customer_data.total_spent / customer_data.total_purchases if customer_data.total_purchases > 0 else 0
                
                # Create customer profile
                customer = CustomerProfile(
                    client_id=job.client_id,
                    customer_id=customer_data.customer_id,
                    email=customer_data.email,
                    first_name=customer_data.first_name,
                    last_name=customer_data.last_name,
                    total_purchases=customer_data.total_purchases,
                    total_spent=customer_data.total_spent,
                    average_order_value=avg_order_value,
                    last_purchase_date=customer_data.last_purchase_date,
                    email_engagement_rate=customer_data.email_engagement_rate,
                    segment="unknown",  # Will be calculated by EngageSense
                    risk_level="low"    # Will be calculated by Retention Reactor
                )
                
                db.add(customer)
                job.imported_customers += 1
                
            except Exception as e:
                job.error_count += 1
                job.errors.append(f"Customer import error: {str(e)}")
            
            job.processed_records += 1
            job.progress = (job.processed_records / job.total_records) * 100
        
        await db.commit()
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def _import_from_google_ads(self, job: ImportJob):
        """Import data from Google Ads API"""
        customer_id = job.source_config.get("customer_id")
        if not customer_id:
            raise ValueError("Google Ads customer_id required")
        
        connector = GoogleAdsConnector(customer_id)
        
        if not await connector.authenticate():
            raise Exception("Google Ads authentication failed")
        
        # Import campaigns
        if job.data_type in ["campaigns", "all"]:
            await self._import_google_ads_campaigns(job, connector)
        
        # Import metrics
        if job.data_type in ["metrics", "all"]:
            await self._import_google_ads_metrics(job, connector)
    
    async def _import_google_ads_campaigns(self, job: ImportJob, connector: GoogleAdsConnector):
        """Import campaigns from Google Ads"""
        # Implementation would fetch campaigns from Google Ads API
        # For now, this is a placeholder
        job.imported_campaigns = 5  # Placeholder
        logger.info("Google Ads campaigns imported", job_id=job.job_id)
    
    async def _import_google_ads_metrics(self, job: ImportJob, connector: GoogleAdsConnector):
        """Import metrics from Google Ads"""
        start_date = job.start_date or (datetime.utcnow() - timedelta(days=30))
        end_date = job.end_date or datetime.utcnow()
        
        metrics = await connector.get_campaign_metrics(start_date, end_date)
        
        async with get_db() as db:
            for metric in metrics:
                # Convert to internal format and save
                # Implementation details...
                job.imported_metrics += 1
        
        logger.info("Google Ads metrics imported", job_id=job.job_id, count=len(metrics))
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def _import_from_meta_ads(self, job: ImportJob):
        """Import data from Meta Ads API"""
        account_id = job.source_config.get("account_id")
        if not account_id:
            raise ValueError("Meta Ads account_id required")
        
        connector = MetaAdsConnector(account_id)
        
        if not await connector.authenticate():
            raise Exception("Meta Ads authentication failed")
        
        # Similar implementation to Google Ads
        job.imported_campaigns = 3  # Placeholder
        job.imported_metrics = 150  # Placeholder
        
        logger.info("Meta Ads data imported", job_id=job.job_id)
    
    async def _import_from_api(self, job: ImportJob):
        """Import data from external API"""
        api_url = job.source_config.get("api_url")
        headers = job.source_config.get("headers", {})
        
        if not api_url:
            raise ValueError("API URL required")
        
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            # Process API data...
            
        logger.info("API data imported", job_id=job.job_id)
    
    def get_job_status(self, job_id: str) -> Optional[ImportJob]:
        """Get import job status"""
        if job_id in self.active_jobs:
            return self.active_jobs[job_id]
        
        # Check history
        for job in self.job_history:
            if job.job_id == job_id:
                return job
        
        return None
    
    def get_active_jobs(self, client_id: Optional[str] = None) -> List[ImportJob]:
        """Get active import jobs"""
        jobs = list(self.active_jobs.values())
        
        if client_id:
            jobs = [job for job in jobs if job.client_id == client_id]
        
        return jobs

# Global import manager instance
import_manager = DataImportManager()
