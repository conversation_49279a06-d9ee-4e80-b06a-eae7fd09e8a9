#!/bin/bash

# Omnify Marketing Cloud - Production Environment Setup
# Sets up AWS infrastructure, database, and deployment pipeline

set -e

echo "🚀 Setting up Omnify Marketing Cloud Production Environment..."

# Check required tools
command -v aws >/dev/null 2>&1 || { echo "AWS CLI is required but not installed. Aborting." >&2; exit 1; }
command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed. Aborting." >&2; exit 1; }

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
CLUSTER_NAME="omnify-production"
SERVICE_NAME="omnify-api-production"
DB_NAME="omnify_production"
DOMAIN_NAME=${DOMAIN_NAME:-api.omnify.com}

echo "📋 Configuration:"
echo "  AWS Region: $AWS_REGION"
echo "  Cluster: $CLUSTER_NAME"
echo "  Service: $SERVICE_NAME"
echo "  Database: $DB_NAME"
echo "  Domain: $DOMAIN_NAME"
echo ""

# Create VPC and networking
echo "🌐 Creating VPC and networking..."
VPC_ID=$(aws ec2 create-vpc \
  --cidr-block 10.0.0.0/16 \
  --tag-specifications 'ResourceType=vpc,Tags=[{Key=Name,Value=omnify-vpc}]' \
  --query 'Vpc.VpcId' \
  --output text)

echo "Created VPC: $VPC_ID"

# Create subnets
SUBNET_1_ID=$(aws ec2 create-subnet \
  --vpc-id $VPC_ID \
  --cidr-block 10.0.1.0/24 \
  --availability-zone ${AWS_REGION}a \
  --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=omnify-subnet-1}]' \
  --query 'Subnet.SubnetId' \
  --output text)

SUBNET_2_ID=$(aws ec2 create-subnet \
  --vpc-id $VPC_ID \
  --cidr-block 10.0.2.0/24 \
  --availability-zone ${AWS_REGION}b \
  --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=omnify-subnet-2}]' \
  --query 'Subnet.SubnetId' \
  --output text)

echo "Created subnets: $SUBNET_1_ID, $SUBNET_2_ID"

# Create Internet Gateway
IGW_ID=$(aws ec2 create-internet-gateway \
  --tag-specifications 'ResourceType=internet-gateway,Tags=[{Key=Name,Value=omnify-igw}]' \
  --query 'InternetGateway.InternetGatewayId' \
  --output text)

aws ec2 attach-internet-gateway \
  --vpc-id $VPC_ID \
  --internet-gateway-id $IGW_ID

echo "Created Internet Gateway: $IGW_ID"

# Create route table
ROUTE_TABLE_ID=$(aws ec2 create-route-table \
  --vpc-id $VPC_ID \
  --tag-specifications 'ResourceType=route-table,Tags=[{Key=Name,Value=omnify-rt}]' \
  --query 'RouteTable.RouteTableId' \
  --output text)

aws ec2 create-route \
  --route-table-id $ROUTE_TABLE_ID \
  --destination-cidr-block 0.0.0.0/0 \
  --gateway-id $IGW_ID

aws ec2 associate-route-table \
  --subnet-id $SUBNET_1_ID \
  --route-table-id $ROUTE_TABLE_ID

aws ec2 associate-route-table \
  --subnet-id $SUBNET_2_ID \
  --route-table-id $ROUTE_TABLE_ID

echo "Created route table: $ROUTE_TABLE_ID"

# Create security groups
echo "🔒 Creating security groups..."

# ALB Security Group
ALB_SG_ID=$(aws ec2 create-security-group \
  --group-name omnify-alb-sg \
  --description "Security group for Omnify ALB" \
  --vpc-id $VPC_ID \
  --tag-specifications 'ResourceType=security-group,Tags=[{Key=Name,Value=omnify-alb-sg}]' \
  --query 'GroupId' \
  --output text)

aws ec2 authorize-security-group-ingress \
  --group-id $ALB_SG_ID \
  --protocol tcp \
  --port 80 \
  --cidr 0.0.0.0/0

aws ec2 authorize-security-group-ingress \
  --group-id $ALB_SG_ID \
  --protocol tcp \
  --port 443 \
  --cidr 0.0.0.0/0

# ECS Security Group
ECS_SG_ID=$(aws ec2 create-security-group \
  --group-name omnify-ecs-sg \
  --description "Security group for Omnify ECS tasks" \
  --vpc-id $VPC_ID \
  --tag-specifications 'ResourceType=security-group,Tags=[{Key=Name,Value=omnify-ecs-sg}]' \
  --query 'GroupId' \
  --output text)

aws ec2 authorize-security-group-ingress \
  --group-id $ECS_SG_ID \
  --protocol tcp \
  --port 8000 \
  --source-group $ALB_SG_ID

# RDS Security Group
RDS_SG_ID=$(aws ec2 create-security-group \
  --group-name omnify-rds-sg \
  --description "Security group for Omnify RDS" \
  --vpc-id $VPC_ID \
  --tag-specifications 'ResourceType=security-group,Tags=[{Key=Name,Value=omnify-rds-sg}]' \
  --query 'GroupId' \
  --output text)

aws ec2 authorize-security-group-ingress \
  --group-id $RDS_SG_ID \
  --protocol tcp \
  --port 5432 \
  --source-group $ECS_SG_ID

echo "Created security groups: ALB=$ALB_SG_ID, ECS=$ECS_SG_ID, RDS=$RDS_SG_ID"

# Create RDS subnet group
echo "🗄️ Creating RDS subnet group..."
aws rds create-db-subnet-group \
  --db-subnet-group-name omnify-db-subnet-group \
  --db-subnet-group-description "Subnet group for Omnify RDS" \
  --subnet-ids $SUBNET_1_ID $SUBNET_2_ID \
  --tags Key=Name,Value=omnify-db-subnet-group

# Create RDS instance
echo "🗄️ Creating RDS PostgreSQL instance..."
DB_PASSWORD=$(openssl rand -base64 32)
echo "Database password: $DB_PASSWORD" > .db_password

aws rds create-db-instance \
  --db-instance-identifier omnify-production \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --engine-version 15.4 \
  --master-username omnify \
  --master-user-password "$DB_PASSWORD" \
  --allocated-storage 20 \
  --storage-type gp2 \
  --vpc-security-group-ids $RDS_SG_ID \
  --db-subnet-group-name omnify-db-subnet-group \
  --backup-retention-period 7 \
  --storage-encrypted \
  --tags Key=Name,Value=omnify-production

echo "RDS instance creation initiated. This will take several minutes..."

# Create ElastiCache Redis cluster
echo "🔴 Creating Redis cluster..."
aws elasticache create-cache-subnet-group \
  --cache-subnet-group-name omnify-redis-subnet-group \
  --cache-subnet-group-description "Subnet group for Omnify Redis" \
  --subnet-ids $SUBNET_1_ID $SUBNET_2_ID

aws elasticache create-cache-cluster \
  --cache-cluster-id omnify-redis-production \
  --cache-node-type cache.t3.micro \
  --engine redis \
  --num-cache-nodes 1 \
  --cache-subnet-group-name omnify-redis-subnet-group \
  --security-group-ids $ECS_SG_ID \
  --tags Key=Name,Value=omnify-redis-production

# Create ECS cluster
echo "🐳 Creating ECS cluster..."
aws ecs create-cluster \
  --cluster-name $CLUSTER_NAME \
  --capacity-providers FARGATE \
  --default-capacity-provider-strategy capacityProvider=FARGATE,weight=1 \
  --tags key=Name,value=$CLUSTER_NAME

# Create Application Load Balancer
echo "⚖️ Creating Application Load Balancer..."
ALB_ARN=$(aws elbv2 create-load-balancer \
  --name omnify-alb \
  --subnets $SUBNET_1_ID $SUBNET_2_ID \
  --security-groups $ALB_SG_ID \
  --scheme internet-facing \
  --type application \
  --ip-address-type ipv4 \
  --tags Key=Name,Value=omnify-alb \
  --query 'LoadBalancers[0].LoadBalancerArn' \
  --output text)

# Create target group
TARGET_GROUP_ARN=$(aws elbv2 create-target-group \
  --name omnify-tg \
  --protocol HTTP \
  --port 8000 \
  --vpc-id $VPC_ID \
  --target-type ip \
  --health-check-path /health \
  --health-check-interval-seconds 30 \
  --health-check-timeout-seconds 5 \
  --healthy-threshold-count 2 \
  --unhealthy-threshold-count 3 \
  --tags Key=Name,Value=omnify-tg \
  --query 'TargetGroups[0].TargetGroupArn' \
  --output text)

# Create ALB listener
aws elbv2 create-listener \
  --load-balancer-arn $ALB_ARN \
  --protocol HTTP \
  --port 80 \
  --default-actions Type=forward,TargetGroupArn=$TARGET_GROUP_ARN

echo "Created ALB: $ALB_ARN"
echo "Created Target Group: $TARGET_GROUP_ARN"

# Create CloudWatch Log Group
echo "📊 Creating CloudWatch log group..."
aws logs create-log-group \
  --log-group-name /ecs/omnify-api \
  --tags Name=omnify-api-logs

# Create ECS Task Definition
echo "📋 Creating ECS task definition..."
cat > task-definition.json << EOF
{
  "family": "omnify-api-production",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "omnify-api",
      "image": "ghcr.io/omnify/marketing-cloud:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/omnify-api",
          "awslogs-region": "$AWS_REGION",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        },
        {
          "name": "DATABASE_URL",
          "value": "postgresql://omnify:$<EMAIL>:5432/omnify_production"
        },
        {
          "name": "REDIS_URL",
          "value": "redis://omnify-redis-production.xxx.cache.amazonaws.com:6379/0"
        }
      ],
      "secrets": [
        {
          "name": "SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:$AWS_REGION:$(aws sts get-caller-identity --query Account --output text):secret:omnify/production/secret-key"
        },
        {
          "name": "OPENAI_API_KEY",
          "valueFrom": "arn:aws:secretsmanager:$AWS_REGION:$(aws sts get-caller-identity --query Account --output text):secret:omnify/production/openai-api-key"
        }
      ]
    }
  ]
}
EOF

aws ecs register-task-definition \
  --cli-input-json file://task-definition.json

# Create ECS Service
echo "🚀 Creating ECS service..."
aws ecs create-service \
  --cluster $CLUSTER_NAME \
  --service-name $SERVICE_NAME \
  --task-definition omnify-api-production \
  --desired-count 2 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_1_ID,$SUBNET_2_ID],securityGroups=[$ECS_SG_ID],assignPublicIp=ENABLED}" \
  --load-balancers "targetGroupArn=$TARGET_GROUP_ARN,containerName=omnify-api,containerPort=8000" \
  --tags key=Name,value=$SERVICE_NAME

# Create Secrets Manager secrets
echo "🔐 Creating secrets..."
aws secretsmanager create-secret \
  --name omnify/production/secret-key \
  --description "Secret key for Omnify production" \
  --secret-string "$(openssl rand -base64 32)"

echo "Secret key created in AWS Secrets Manager"

# Create CloudWatch alarms
echo "🚨 Creating CloudWatch alarms..."
aws cloudwatch put-metric-alarm \
  --alarm-name "omnify-high-cpu" \
  --alarm-description "High CPU utilization" \
  --metric-name CPUUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2 \
  --alarm-actions "arn:aws:sns:$AWS_REGION:$(aws sts get-caller-identity --query Account --output text):omnify-alerts" \
  --dimensions Name=ServiceName,Value=$SERVICE_NAME Name=ClusterName,Value=$CLUSTER_NAME

aws cloudwatch put-metric-alarm \
  --alarm-name "omnify-high-memory" \
  --alarm-description "High memory utilization" \
  --metric-name MemoryUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2 \
  --alarm-actions "arn:aws:sns:$AWS_REGION:$(aws sts get-caller-identity --query Account --output text):omnify-alerts" \
  --dimensions Name=ServiceName,Value=$SERVICE_NAME Name=ClusterName,Value=$CLUSTER_NAME

# Create backup script
cat > backup_database.sh << 'EOF'
#!/bin/bash
# Database backup script

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="omnify-backup-$DATE"

# Create RDS snapshot
aws rds create-db-snapshot \
  --db-instance-identifier omnify-production \
  --db-snapshot-identifier $BACKUP_NAME

echo "Database backup created: $BACKUP_NAME"
EOF

chmod +x backup_database.sh

# Output configuration
echo ""
echo "✅ Production environment setup complete!"
echo ""
echo "📋 Configuration Summary:"
echo "  VPC ID: $VPC_ID"
echo "  Subnets: $SUBNET_1_ID, $SUBNET_2_ID"
echo "  ALB ARN: $ALB_ARN"
echo "  Target Group ARN: $TARGET_GROUP_ARN"
echo "  ECS Cluster: $CLUSTER_NAME"
echo "  ECS Service: $SERVICE_NAME"
echo ""
echo "🔐 Security:"
echo "  Database password saved to: .db_password"
echo "  Secret key stored in AWS Secrets Manager"
echo ""
echo "📝 Next Steps:"
echo "1. Wait for RDS instance to be available (10-15 minutes)"
echo "2. Update DATABASE_URL in task definition with actual RDS endpoint"
echo "3. Update REDIS_URL in task definition with actual Redis endpoint"
echo "4. Add your API keys to AWS Secrets Manager:"
echo "   aws secretsmanager create-secret --name omnify/production/openai-api-key --secret-string 'your-openai-key'"
echo "   aws secretsmanager create-secret --name omnify/production/google-ads-key --secret-string 'your-google-ads-key'"
echo "5. Deploy your application:"
echo "   docker build -t omnify/marketing-cloud ."
echo "   docker tag omnify/marketing-cloud ghcr.io/omnify/marketing-cloud:latest"
echo "   docker push ghcr.io/omnify/marketing-cloud:latest"
echo "6. Update ECS service to trigger deployment"
echo ""
echo "🌐 Your application will be available at the ALB DNS name once deployed."

# Get ALB DNS name
ALB_DNS=$(aws elbv2 describe-load-balancers \
  --load-balancer-arns $ALB_ARN \
  --query 'LoadBalancers[0].DNSName' \
  --output text)

echo "ALB DNS Name: $ALB_DNS"
echo ""
echo "🎉 Production environment is ready!"
