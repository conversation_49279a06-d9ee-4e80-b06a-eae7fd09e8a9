# Database Configuration
DATABASE_URL=postgresql+asyncpg://omnify_user:omnify_password@localhost:5432/omnify_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# JWT Authentication
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-1106-preview

# Manus RL Configuration (if available)
MANUS_RL_API_KEY=your-manus-rl-api-key
MANUS_RL_ENDPOINT=https://api.manus.rl/v1

# Google Ads API
GOOGLE_ADS_DEVELOPER_TOKEN=your-google-ads-developer-token
GOOGLE_ADS_CLIENT_ID=your-google-ads-client-id
GOOGLE_ADS_CLIENT_SECRET=your-google-ads-client-secret
GOOGLE_ADS_REFRESH_TOKEN=your-google-ads-refresh-token

# Meta/Facebook Ads API
META_APP_ID=your-meta-app-id
META_APP_SECRET=your-meta-app-secret
META_ACCESS_TOKEN=your-meta-access-token

# n8n Configuration
N8N_WEBHOOK_URL=http://localhost:5678/webhook
N8N_API_KEY=your-n8n-api-key

# Monitoring & Logging
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO

# Environment
ENVIRONMENT=development
DEBUG=true

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password

# Retool Dashboard
RETOOL_API_KEY=your-retool-api-key
RETOOL_WORKSPACE_URL=https://your-workspace.retool.com
