# 📖 Omnify Marketing Cloud - Complete API Reference

## Overview
The Omnify Marketing Cloud API provides programmatic access to all platform features, enabling you to integrate AI-driven marketing automation into your existing workflows.

**Base URL**: `https://api.omnify.com/api/v1`
**Documentation**: Interactive docs at `https://api.omnify.com/docs`
**Authentication**: <PERSON><PERSON> (JWT) with refresh token support
**Rate Limits**: 60-300 requests/minute based on subscription tier
**WebSocket**: `wss://api.omnify.com/ws/{client_id}` for real-time updates

## 🔐 Authentication

### Enhanced Authentication Flow

#### Login with Refresh Token
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 28800,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "admin",
    "client_id": 1,
    "client_name": "Company Inc"
  }
}
```

#### Refresh Access Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### User Registration
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "John",
  "last_name": "Doe",
  "company_name": "Company Inc"
}
```

## 📊 Dashboard APIs

### Dashboard Overview
```http
GET /dashboard/overview?client_id={client_id}&days={days}
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "client_id": "1",
  "total_spend": 15420.50,
  "total_revenue": 48650.25,
  "current_roas": 3.15,
  "current_cac": 42.30,
  "ai_decisions_today": 23,
  "estimated_savings": 2340.80,
  "roas_trend": "improving",
  "period_comparison": {
    "spend_change": 12.5,
    "revenue_change": 18.3,
    "roas_change": 5.2
  }
}
```

### Campaign Performance
```http
GET /dashboard/campaigns?client_id={client_id}&platform={platform}
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "campaigns": [
    {
      "campaign_id": "camp_123",
      "name": "Holiday Sale 2024",
      "platform": "google_ads",
      "status": "active",
      "spend": 2450.30,
      "revenue": 7890.50,
      "roas": 3.22,
      "cac": 38.50,
      "target_cac": 45.00,
      "budget_utilization": 68.5,
      "last_ai_optimization": "2024-01-15T10:30:00Z",
      "alert_level": "none"
    }
  ],
  "summary": {
    "total_campaigns": 8,
    "active_campaigns": 6,
    "avg_roas": 3.15,
    "total_spend": 15420.50
  }
}
```

### AI Agents Status
```http
GET /dashboard/ai-agents?client_id={client_id}
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "roi_engine": {
    "status": "active",
    "last_run": "2024-01-15T10:30:00Z",
    "decisions_today": 15,
    "success_rate": 87.5,
    "avg_confidence": 0.89,
    "total_savings": 12450.30
  },
  "retention_reactor": {
    "status": "active",
    "customers_analyzed": 1250,
    "at_risk_identified": 89,
    "actions_triggered": 34,
    "success_rate": 72.3
  },
  "engage_sense": {
    "status": "active",
    "segments_updated": 6,
    "campaigns_triggered": 12,
    "engagement_improvement": 23.5
  }
}
```

## 🤖 AI Agents APIs

### Trigger AI Analysis
```http
POST /agents/analyze
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "client_id": "1",
  "agent": "roi_engine",
  "campaign_ids": ["camp_123", "camp_456"],
  "force_analysis": true
}
```

**Response**:
```json
{
  "status": "analysis_started",
  "agent": "roi_engine",
  "campaign_count": 2,
  "estimated_completion": "5-10 minutes",
  "analysis_id": "analysis_789"
}
```

### Get AI Decision History
```http
GET /agents/decisions?client_id={client_id}&agent={agent}&limit={limit}
Authorization: Bearer {access_token}
```

## 📈 Advanced Campaign Management

### Campaign Insights
```http
GET /campaigns/{campaign_id}/insights
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "campaign_id": "camp_123",
  "performance_score": 78.5,
  "ai_recommendations": [
    {
      "type": "bid_optimization",
      "confidence": 0.89,
      "description": "Increase bids for high-converting keywords",
      "expected_improvement": "15-20% ROAS increase"
    }
  ],
  "optimization_opportunities": [
    {
      "type": "audience_expansion",
      "priority": "high",
      "description": "Expand to similar audiences",
      "expected_impact": "25% reach increase"
    }
  ],
  "risk_factors": [
    {
      "type": "budget_exhaustion",
      "severity": "medium",
      "description": "Budget 85% utilized",
      "recommended_action": "Increase budget or optimize spend"
    }
  ],
  "predicted_performance": {
    "roas_7_days": 3.45,
    "spend_7_days": 1750.00,
    "revenue_7_days": 6037.50
  }
}
```

### Campaign Optimization
```http
POST /campaigns/optimize
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "campaign_ids": ["camp_123", "camp_456"],
  "optimization_type": "bid_optimization",
  "force_optimization": true,
  "target_metrics": {
    "target_roas": 3.5,
    "max_cac": 40.0
  }
}
```

### Bulk Campaign Actions
```http
POST /campaigns/bulk-action
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "campaign_ids": ["camp_123", "camp_456"],
  "action": "pause",
  "parameters": {
    "reason": "budget_exhausted"
  }
}
```

### Campaign Comparison
```http
POST /campaigns/compare
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "campaign_ids": ["camp_123", "camp_456", "camp_789"]
}
```

## 💳 Billing & Subscription APIs

### Subscription Information
```http
GET /billing/subscription
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "client_id": "1",
  "plan": "professional",
  "status": "active",
  "current_period_start": "2024-01-01T00:00:00Z",
  "current_period_end": "2024-02-01T00:00:00Z",
  "usage": {
    "campaigns_count": 15,
    "monthly_spend": 25000.00,
    "customers_count": 5000,
    "ai_decisions_this_month": 450,
    "email_sends_this_month": 12000
  },
  "plan_config": {
    "name": "Professional",
    "price_monthly": 997.0,
    "features": {
      "max_campaigns": 50,
      "max_monthly_spend": 100000.0,
      "max_customers": 25000,
      "ai_decisions_per_month": 25000,
      "advanced_analytics": true
    }
  },
  "usage_warnings": []
}
```

### Available Plans
```http
GET /billing/plans
Authorization: Bearer {access_token}
```

### Create Subscription
```http
POST /billing/subscription/create
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "plan": "professional",
  "billing_cycle": "monthly",
  "payment_method_id": "pm_1234567890"
}
```

### Usage Information
```http
GET /billing/usage
Authorization: Bearer {access_token}
```

### Invoices
```http
GET /billing/invoices?limit={limit}
Authorization: Bearer {access_token}
```

## 🧙‍♂️ Smart Integration Wizard APIs

The Smart Integration Wizard provides AI-powered integration assistance with step-by-step guidance, intelligent recommendations, and multi-cloud deployment capabilities.

### Get Integration Recommendations
```http
GET /integrations/wizard/recommendations
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "recommendations": [
    {
      "integration_type": "google_ads",
      "priority": "high",
      "reason": "Google Ads provides access to the largest search platform",
      "benefits": [
        "Access to 90%+ of search traffic",
        "Advanced audience targeting",
        "Real-time bid optimization"
      ],
      "estimated_setup_time": "10-15 minutes",
      "difficulty": "easy",
      "prerequisites": ["Google Ads account", "Google Cloud project"]
    }
  ],
  "smart_suggestions": [
    {
      "suggestion_id": "integrate_google_ads",
      "type": "integration",
      "title": "Connect Google Ads",
      "description": "Unlock search advertising optimization",
      "impact": "high",
      "effort": "low",
      "estimated_benefit": "15-30% CAC reduction"
    }
  ],
  "total_recommendations": 3,
  "priority_actions": 2
}
```

### Start Integration Wizard
```http
GET /integrations/wizard/{integration_type}
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "integration_name": "Google Ads",
  "difficulty": "easy",
  "estimated_time": "10-15 minutes",
  "progress": {
    "client_id": "1",
    "integration_type": "google_ads",
    "current_step": 0,
    "total_steps": 4,
    "progress_percentage": 0.0,
    "status": "not_started",
    "steps": [
      {
        "step_id": "google_ads_1",
        "title": "Enable Google Ads API",
        "description": "Enable the Google Ads API in your Google Cloud Console",
        "status": "pending",
        "instructions": [
          "Go to Google Cloud Console (console.cloud.google.com)",
          "Select or create a project",
          "Navigate to APIs & Services > Library"
        ],
        "estimated_time": "3-5 minutes",
        "required_data": [
          {
            "field": "project_id",
            "type": "string",
            "description": "Google Cloud Project ID"
          }
        ],
        "help_links": [
          {
            "title": "Google Ads API Setup Guide",
            "url": "https://developers.google.com/google-ads/api/docs/first-call/overview"
          }
        ]
      }
    ],
    "next_recommended_action": "Start with: Enable Google Ads API",
    "estimated_completion": "10-15 minutes"
  }
}
```

### Complete Integration Step
```http
POST /integrations/wizard/{integration_type}/step/{step_id}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "project_id": "my-google-cloud-project",
  "developer_token": "your-developer-token-here"
}
```

**Response**:
```json
{
  "status": "success",
  "step_completed": "Enable Google Ads API",
  "progress_percentage": 25.0,
  "next_action": "Next: Get Developer Token",
  "integration_status": "in_progress",
  "message": "Step 'Enable Google Ads API' completed successfully!"
}
```

### Get Smart Suggestions
```http
GET /integrations/suggestions
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "suggestions": [
    {
      "suggestion_id": "optimize_roas",
      "type": "optimization",
      "title": "Improve ROAS with AI Optimization",
      "description": "Your current ROAS could be improved with automated bid optimization",
      "impact": "high",
      "effort": "low",
      "category": "ai_optimization",
      "action_url": "/campaigns/optimize",
      "estimated_benefit": "15-30% ROAS improvement"
    }
  ],
  "total_suggestions": 5,
  "high_impact_suggestions": 2,
  "quick_wins": 3
}
```

### Get Cloud Variant Recommendations
```http
GET /integrations/cloud-variants
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "cloud_variants": [
    {
      "variant": "aws",
      "variant_name": "AWS-Manus Hybrid (Patent-Focused)",
      "priority": "high",
      "reason": "Best performance with proprietary Manus RL algorithms and AWS Bedrock fallback",
      "benefits": [
        "Proprietary AI algorithms for competitive advantage",
        "20% cost reduction with Graviton3 instances",
        "Enterprise-grade security with AWS PrivateLink",
        "Auto-scaling with predictive scaling"
      ],
      "estimated_setup_time": "8 weeks",
      "cost_estimate": "$150K+ cloud credits",
      "enterprise_readiness": "High (9/10)",
      "use_cases": [
        "High-performance marketing optimization",
        "Large-scale enterprise deployments",
        "IP-sensitive competitive environments"
      ]
    },
    {
      "variant": "azure",
      "variant_name": "Azure OpenAI Accelerator",
      "priority": "high",
      "reason": "Fastest enterprise deployment with native Office 365 integration",
      "benefits": [
        "Pre-built OpenAI integrations",
        "Native Office 365 and Dynamics 365 connectivity",
        "Built-in GDPR and SOC 2 compliance"
      ],
      "estimated_setup_time": "7 weeks",
      "cost_estimate": "$200K+ cloud credits",
      "enterprise_readiness": "High (8/10)",
      "use_cases": [
        "Microsoft ecosystem organizations",
        "Rapid enterprise deployment",
        "GDPR compliance requirements"
      ]
    }
  ],
  "total_variants": 5,
  "recommended_variant": {
    "variant": "aws",
    "variant_name": "AWS-Manus Hybrid (Patent-Focused)"
  },
  "client_profile": {
    "monthly_spend": 75000,
    "team_size": 8,
    "industry": "enterprise",
    "technical_expertise": "high"
  }
}
```

### Deploy Cloud Variant
```http
POST /integrations/deploy-variant
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "variant": "aws",
  "environment": "production",
  "region": "us-east-1",
  "custom_config": {
    "scaling": {
      "min_instances": 3,
      "max_instances": 50
    }
  }
}
```

**Response**:
```json
{
  "status": "deployment_started",
  "variant": "aws",
  "environment": "production",
  "region": "us-east-1",
  "estimated_completion": "30-60 minutes",
  "tracking_id": "deploy_aws_production_123"
}
```

## 🔔 Alerts & Notifications

### Active Alerts
```http
GET /dashboard/alerts?client_id={client_id}&severity={severity}
Authorization: Bearer {access_token}
```

### Acknowledge Alert
```http
POST /system/alerts/{alert_id}/acknowledge
Authorization: Bearer {access_token}
```

### Resolve Alert
```http
POST /system/alerts/{alert_id}/resolve
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "resolution_notes": "Issue resolved by increasing budget"
}
```

## 👥 Customer Intelligence

### Customer Segments
```http
GET /dashboard/customer-segments?client_id={client_id}
Authorization: Bearer {access_token}
```

**Response**:
```json
{
  "segments": {
    "champions": {
      "count": 245,
      "percentage": 12.3,
      "avg_engagement_score": 92.5,
      "avg_clv": 2450.30
    },
    "at_risk": {
      "count": 89,
      "percentage": 4.5,
      "avg_engagement_score": 34.2,
      "churn_probability": 0.78
    }
  },
  "total_customers": 2000,
  "segment_trends": {
    "champions_growth": 8.5,
    "at_risk_reduction": -12.3
  }
}
```

### Customer Profile
```http
GET /customers/{customer_id}?client_id={client_id}
Authorization: Bearer {access_token}
```

## 🔗 Integration Management

### Start Onboarding
```http
POST /onboarding/start
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "client_id": "1",
  "google_ads_credentials": {
    "customer_id": "************",
    "developer_token": "your_dev_token"
  },
  "meta_ads_credentials": {
    "account_id": "act_123456789",
    "access_token": "your_access_token"
  }
}
```

### Onboarding Status
```http
GET /onboarding/status/{process_id}
Authorization: Bearer {access_token}
```

## 🌐 WebSocket Real-time Updates

### Connection
```javascript
const ws = new WebSocket('wss://api.omnify.com/ws/1');

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);

  switch(data.type) {
    case 'metric_update':
      updateDashboardMetrics(data.data);
      break;
    case 'ai_decision':
      showAIDecisionNotification(data.data);
      break;
    case 'alert':
      showAlert(data.data);
      break;
  }
};
```

### Message Types
- `connection_established`: Welcome message
- `metric_update`: Real-time metric updates
- `ai_decision`: AI optimization notifications
- `alert`: System and performance alerts
- `campaign_update`: Campaign status changes
- `system_status`: System health updates

## 📝 Error Handling

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Rate Limited
- `500` - Internal Server Error

### Error Response Format
```json
{
  "error": {
    "code": "INVALID_CAMPAIGN_ID",
    "message": "Campaign not found",
    "details": {
      "campaign_id": "camp_invalid",
      "client_id": "1"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 🚀 Rate Limiting

### Limits by Plan
- **Starter**: 60 requests/minute
- **Professional**: 120 requests/minute
- **Enterprise**: 300 requests/minute

### Headers
```http
X-RateLimit-Limit: 120
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## 🧪 Testing Environment

### Sandbox
**Base URL**: `https://sandbox-api.omnify.com/api/v1`

### Test Data
- 3 sample campaigns with realistic metrics
- 500 sample customers across all segments
- Historical data for the past 90 days
- All AI agents active with simulated decisions

---

**📞 Support**: <EMAIL>
**Status Page**: https://status.omnify.com
**Interactive Docs**: https://api.omnify.com/docs
