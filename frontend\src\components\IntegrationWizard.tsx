/**
 * Smart Integration Wizard Component
 * User-friendly step-by-step integration helper with AI recommendations
 */
import React, { useState, useEffect } from 'react';
import { 
  CheckCircleIcon, 
  ClockIcon, 
  ExclamationTriangleIcon,
  LightBulbIcon,
  ArrowRightIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

interface IntegrationStep {
  step_id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  instructions: string[];
  estimated_time: string;
  required_data: Array<{
    field: string;
    type: string;
    description: string;
    options?: string[];
  }>;
  help_links: Array<{
    title: string;
    url: string;
  }>;
  troubleshooting: Array<{
    issue: string;
    solution: string;
  }>;
}

interface IntegrationRecommendation {
  integration_type: string;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  benefits: string[];
  estimated_setup_time: string;
  difficulty: 'easy' | 'medium' | 'advanced';
  prerequisites: string[];
}

interface SmartSuggestion {
  suggestion_id: string;
  type: 'integration' | 'optimization' | 'feature';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
  category: string;
  action_url: string;
  estimated_benefit: string;
}

const IntegrationWizard: React.FC = () => {
  const [currentIntegration, setCurrentIntegration] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<IntegrationStep[]>([]);
  const [recommendations, setRecommendations] = useState<IntegrationRecommendation[]>([]);
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]);
  const [stepData, setStepData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [showTroubleshooting, setShowTroubleshooting] = useState<string | null>(null);

  useEffect(() => {
    fetchRecommendations();
    fetchSuggestions();
  }, []);

  const fetchRecommendations = async () => {
    try {
      const response = await fetch('/api/v1/integrations/wizard/recommendations');
      const data = await response.json();
      setRecommendations(data.recommendations);
    } catch (error) {
      console.error('Failed to fetch recommendations:', error);
    }
  };

  const fetchSuggestions = async () => {
    try {
      const response = await fetch('/api/v1/integrations/suggestions');
      const data = await response.json();
      setSuggestions(data.suggestions);
    } catch (error) {
      console.error('Failed to fetch suggestions:', error);
    }
  };

  const startIntegration = async (integrationType: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/integrations/wizard/${integrationType}`);
      const data = await response.json();
      setCurrentIntegration(integrationType);
      setSteps(data.progress.steps);
      setCurrentStep(0);
      setStepData({});
    } catch (error) {
      console.error('Failed to start integration:', error);
    } finally {
      setLoading(false);
    }
  };

  const completeStep = async (stepId: string) => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/v1/integrations/wizard/${currentIntegration}/step/${stepId}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(stepData)
        }
      );
      
      if (response.ok) {
        const result = await response.json();
        
        // Update step status
        const updatedSteps = [...steps];
        updatedSteps[currentStep].status = 'completed';
        setSteps(updatedSteps);
        
        // Move to next step
        if (currentStep + 1 < steps.length) {
          setCurrentStep(currentStep + 1);
          updatedSteps[currentStep + 1].status = 'in_progress';
        }
        
        // Show success message
        showNotification(result.message, 'success');
      } else {
        const error = await response.json();
        showNotification(error.detail, 'error');
      }
    } catch (error) {
      console.error('Failed to complete step:', error);
      showNotification('Failed to complete step. Please try again.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showNotification = (message: string, type: 'success' | 'error') => {
    // Implementation would show toast notification
    console.log(`${type}: ${message}`);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'high': return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'medium': return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'low': return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      default: return <QuestionMarkCircleIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  if (currentIntegration && steps.length > 0) {
    const step = steps[currentStep];
    
    return (
      <div className="max-w-4xl mx-auto p-6">
        {/* Progress Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {currentIntegration.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Integration
            </h1>
            <button
              onClick={() => setCurrentIntegration(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ← Back to Recommendations
            </button>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
          
          <div className="flex justify-between text-sm text-gray-600">
            <span>Step {currentStep + 1} of {steps.length}</span>
            <span>{Math.round(((currentStep + 1) / steps.length) * 100)}% Complete</span>
          </div>
        </div>

        {/* Current Step */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              {step.status === 'completed' ? (
                <CheckCircleIcon className="h-8 w-8 text-green-500" />
              ) : step.status === 'in_progress' ? (
                <ClockIcon className="h-8 w-8 text-blue-500 animate-pulse" />
              ) : (
                <div className="h-8 w-8 rounded-full border-2 border-gray-300" />
              )}
            </div>
            
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {step.title}
              </h2>
              <p className="text-gray-600 mb-4">{step.description}</p>
              
              <div className="bg-blue-50 rounded-lg p-4 mb-4">
                <div className="flex items-center mb-2">
                  <ClockIcon className="h-5 w-5 text-blue-500 mr-2" />
                  <span className="text-sm font-medium text-blue-900">
                    Estimated time: {step.estimated_time}
                  </span>
                </div>
              </div>

              {/* Instructions */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-3">Instructions:</h3>
                <ol className="list-decimal list-inside space-y-2">
                  {step.instructions.map((instruction, index) => (
                    <li key={index} className="text-gray-700">{instruction}</li>
                  ))}
                </ol>
              </div>

              {/* Required Data Form */}
              {step.required_data.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-medium text-gray-900 mb-3">Required Information:</h3>
                  <div className="space-y-4">
                    {step.required_data.map((field) => (
                      <div key={field.field}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {field.description}
                        </label>
                        {field.type === 'select' ? (
                          <select
                            value={stepData[field.field] || ''}
                            onChange={(e) => setStepData({
                              ...stepData,
                              [field.field]: e.target.value
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">Select an option</option>
                            {field.options?.map((option) => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                        ) : (
                          <input
                            type={field.type === 'email' ? 'email' : 'text'}
                            value={stepData[field.field] || ''}
                            onChange={(e) => setStepData({
                              ...stepData,
                              [field.field]: e.target.value
                            })}
                            placeholder={`Enter your ${field.description.toLowerCase()}`}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Help Links */}
              {step.help_links.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-medium text-gray-900 mb-3">Helpful Resources:</h3>
                  <div className="space-y-2">
                    {step.help_links.map((link, index) => (
                      <a
                        key={index}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-blue-600 hover:text-blue-800"
                      >
                        {link.title}
                        <ArrowRightIcon className="h-4 w-4 ml-1" />
                      </a>
                    ))}
                  </div>
                </div>
              )}

              {/* Troubleshooting */}
              {step.troubleshooting.length > 0 && (
                <div className="mb-6">
                  <button
                    onClick={() => setShowTroubleshooting(
                      showTroubleshooting === step.step_id ? null : step.step_id
                    )}
                    className="flex items-center text-yellow-600 hover:text-yellow-800 font-medium"
                  >
                    <QuestionMarkCircleIcon className="h-5 w-5 mr-2" />
                    Need help? View troubleshooting tips
                  </button>
                  
                  {showTroubleshooting === step.step_id && (
                    <div className="mt-3 bg-yellow-50 rounded-lg p-4">
                      <h4 className="font-medium text-yellow-900 mb-2">Common Issues:</h4>
                      <div className="space-y-3">
                        {step.troubleshooting.map((item, index) => (
                          <div key={index}>
                            <p className="font-medium text-yellow-800">{item.issue}</p>
                            <p className="text-yellow-700">{item.solution}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  onClick={() => completeStep(step.step_id)}
                  disabled={loading || step.required_data.some(field => !stepData[field.field])}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Processing...' : 'Complete Step'}
                </button>
                
                {currentStep > 0 && (
                  <button
                    onClick={() => setCurrentStep(currentStep - 1)}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Previous Step
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Smart Integration Helper
        </h1>
        <p className="text-gray-600">
          Get personalized recommendations and step-by-step guidance to connect your marketing tools.
        </p>
      </div>

      {/* Smart Suggestions */}
      {suggestions.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <LightBulbIcon className="h-6 w-6 text-yellow-500 mr-2" />
            Smart Suggestions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {suggestions.slice(0, 3).map((suggestion) => (
              <div key={suggestion.suggestion_id} className="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{suggestion.title}</h3>
                  {getImpactIcon(suggestion.impact)}
                </div>
                <p className="text-gray-600 text-sm mb-3">{suggestion.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">{suggestion.estimated_benefit}</span>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Take Action →
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Integration Recommendations */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Recommended Integrations
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {recommendations.map((rec) => (
            <div key={rec.integration_type} className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {rec.integration_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(rec.priority)}`}>
                  {rec.priority} priority
                </span>
              </div>
              
              <p className="text-gray-600 mb-4">{rec.reason}</p>
              
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Benefits:</h4>
                <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                  {rec.benefits.map((benefit, index) => (
                    <li key={index}>{benefit}</li>
                  ))}
                </ul>
              </div>
              
              <div className="flex items-center justify-between mb-4">
                <div className="text-sm text-gray-500">
                  <span className="font-medium">Setup time:</span> {rec.estimated_setup_time}
                </div>
                <div className="text-sm text-gray-500">
                  <span className="font-medium">Difficulty:</span> {rec.difficulty}
                </div>
              </div>
              
              {rec.prerequisites.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Prerequisites:</h4>
                  <ul className="list-disc list-inside text-sm text-gray-600">
                    {rec.prerequisites.map((prereq, index) => (
                      <li key={index}>{prereq}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              <button
                onClick={() => startIntegration(rec.integration_type)}
                disabled={loading}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Loading...' : 'Start Integration'}
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default IntegrationWizard;
