"""
Infrastructure tests for Terraform configurations and cloud resources
Tests all cloud provider infrastructure configurations
"""
import pytest
import subprocess
import json
import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

class TestTerraformConfigurations:
    """Test Terraform configuration files"""
    
    @pytest.fixture
    def terraform_dirs(self):
        """Get all Terraform directories"""
        base_dir = Path(__file__).parent.parent / "infrastructure"
        return [
            base_dir / "aws",
            base_dir / "azure", 
            base_dir / "gcp"
        ]
    
    def test_terraform_syntax_validation(self, terraform_dirs):
        """Test Terraform syntax validation for all configurations"""
        for tf_dir in terraform_dirs:
            if tf_dir.exists():
                # Run terraform validate
                result = subprocess.run(
                    ["terraform", "validate"],
                    cwd=tf_dir,
                    capture_output=True,
                    text=True
                )
                
                # Check if terraform is available
                if result.returncode == 127:  # Command not found
                    pytest.skip("Terraform not installed")
                
                assert result.returncode == 0, f"Terraform validation failed for {tf_dir}: {result.stderr}"
    
    def test_terraform_format_check(self, terraform_dirs):
        """Test Terraform formatting"""
        for tf_dir in terraform_dirs:
            if tf_dir.exists():
                result = subprocess.run(
                    ["terraform", "fmt", "-check"],
                    cwd=tf_dir,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 127:  # Command not found
                    pytest.skip("Terraform not installed")
                
                assert result.returncode == 0, f"Terraform formatting check failed for {tf_dir}"
    
    def test_terraform_plan_generation(self, terraform_dirs):
        """Test Terraform plan generation (dry run)"""
        for tf_dir in terraform_dirs:
            if tf_dir.exists():
                # Initialize terraform
                init_result = subprocess.run(
                    ["terraform", "init", "-backend=false"],
                    cwd=tf_dir,
                    capture_output=True,
                    text=True
                )
                
                if init_result.returncode == 127:  # Command not found
                    pytest.skip("Terraform not installed")
                
                assert init_result.returncode == 0, f"Terraform init failed for {tf_dir}: {init_result.stderr}"
                
                # Generate plan
                plan_result = subprocess.run(
                    ["terraform", "plan", "-out=test.tfplan"],
                    cwd=tf_dir,
                    capture_output=True,
                    text=True,
                    env={**os.environ, "TF_VAR_environment": "test"}
                )
                
                # Clean up plan file
                plan_file = tf_dir / "test.tfplan"
                if plan_file.exists():
                    plan_file.unlink()
                
                # Plan should succeed (even if resources can't be created due to missing credentials)
                # We're just testing the configuration syntax and logic
                assert plan_result.returncode in [0, 1], f"Terraform plan failed for {tf_dir}: {plan_result.stderr}"

class TestDockerConfigurations:
    """Test Docker configurations"""
    
    @pytest.fixture
    def docker_dirs(self):
        """Get all Docker directories"""
        base_dir = Path(__file__).parent.parent / "docker"
        return [
            base_dir / "aws",
            base_dir / "azure",
            base_dir / "gcp",
            base_dir / "local"
        ]
    
    def test_dockerfile_syntax(self, docker_dirs):
        """Test Dockerfile syntax"""
        for docker_dir in docker_dirs:
            dockerfile = docker_dir / "Dockerfile"
            if dockerfile.exists():
                # Use docker to validate Dockerfile syntax
                result = subprocess.run(
                    ["docker", "build", "--dry-run", "-f", str(dockerfile), "."],
                    cwd=dockerfile.parent.parent,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 127:  # Command not found
                    pytest.skip("Docker not installed")
                
                # Docker build should succeed or fail with specific errors (not syntax errors)
                assert "Dockerfile parse error" not in result.stderr, f"Dockerfile syntax error in {dockerfile}"
    
    def test_docker_compose_syntax(self):
        """Test Docker Compose file syntax"""
        compose_files = [
            Path(__file__).parent.parent / "docker-compose.demo.yml",
            Path(__file__).parent.parent / "docker-compose.yml"
        ]
        
        for compose_file in compose_files:
            if compose_file.exists():
                result = subprocess.run(
                    ["docker-compose", "-f", str(compose_file), "config"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 127:  # Command not found
                    pytest.skip("Docker Compose not installed")
                
                assert result.returncode == 0, f"Docker Compose validation failed for {compose_file}: {result.stderr}"

class TestCloudResourceValidation:
    """Test cloud resource configurations"""
    
    def test_aws_resource_naming_conventions(self):
        """Test AWS resource naming conventions"""
        aws_dir = Path(__file__).parent.parent / "infrastructure" / "aws"
        
        if not aws_dir.exists():
            pytest.skip("AWS infrastructure directory not found")
        
        # Check that all resources follow naming conventions
        tf_files = list(aws_dir.glob("*.tf"))
        
        for tf_file in tf_files:
            content = tf_file.read_text()
            
            # Check for consistent naming patterns
            assert "omnify" in content.lower(), f"Resources in {tf_file} should include 'omnify' in names"
            
            # Check for environment variable usage
            if "resource " in content:
                assert "var.environment" in content, f"Resources in {tf_file} should use environment variables"
    
    def test_azure_resource_naming_conventions(self):
        """Test Azure resource naming conventions"""
        azure_dir = Path(__file__).parent.parent / "infrastructure" / "azure"
        
        if not azure_dir.exists():
            pytest.skip("Azure infrastructure directory not found")
        
        tf_files = list(azure_dir.glob("*.tf"))
        
        for tf_file in tf_files:
            content = tf_file.read_text()
            
            # Check for consistent naming patterns
            assert "omnify" in content.lower(), f"Resources in {tf_file} should include 'omnify' in names"
            
            # Check for resource group usage
            if "azurerm_" in content:
                assert "resource_group_name" in content, f"Azure resources in {tf_file} should use resource groups"
    
    def test_gcp_resource_naming_conventions(self):
        """Test GCP resource naming conventions"""
        gcp_dir = Path(__file__).parent.parent / "infrastructure" / "gcp"
        
        if not gcp_dir.exists():
            pytest.skip("GCP infrastructure directory not found")
        
        tf_files = list(gcp_dir.glob("*.tf"))
        
        for tf_file in tf_files:
            content = tf_file.read_text()
            
            # Check for consistent naming patterns
            assert "omnify" in content.lower(), f"Resources in {tf_file} should include 'omnify' in names"
            
            # Check for project usage
            if "google_" in content:
                assert "project" in content, f"GCP resources in {tf_file} should specify project"

class TestSecurityConfigurations:
    """Test security configurations in infrastructure"""
    
    def test_aws_security_groups(self):
        """Test AWS security group configurations"""
        aws_dir = Path(__file__).parent.parent / "infrastructure" / "aws"
        
        if not aws_dir.exists():
            pytest.skip("AWS infrastructure directory not found")
        
        # Check network security file
        network_file = aws_dir / "network_security.tf"
        if network_file.exists():
            content = network_file.read_text()
            
            # Should have security groups
            assert "aws_security_group" in content, "Should define security groups"
            
            # Should not allow unrestricted access
            assert "0.0.0.0/0" not in content or "ingress" not in content, "Should not have unrestricted ingress"
            
            # Should have egress rules
            assert "egress" in content, "Should define egress rules"
    
    def test_encryption_configurations(self):
        """Test encryption configurations"""
        infra_dirs = [
            Path(__file__).parent.parent / "infrastructure" / "aws",
            Path(__file__).parent.parent / "infrastructure" / "azure",
            Path(__file__).parent.parent / "infrastructure" / "gcp"
        ]
        
        for infra_dir in infra_dirs:
            if infra_dir.exists():
                tf_files = list(infra_dir.glob("*.tf"))
                
                has_encryption = False
                for tf_file in tf_files:
                    content = tf_file.read_text()
                    
                    # Check for encryption configurations
                    if any(keyword in content.lower() for keyword in ["encrypt", "kms", "key_vault", "secret"]):
                        has_encryption = True
                        break
                
                assert has_encryption, f"Infrastructure in {infra_dir} should have encryption configurations"

class TestNetworkConfigurations:
    """Test network configurations"""
    
    def test_vpc_configurations(self):
        """Test VPC/VNet configurations"""
        network_files = [
            Path(__file__).parent.parent / "infrastructure" / "aws" / "network_security.tf",
            Path(__file__).parent.parent / "infrastructure" / "azure" / "load_balancer.tf",
            Path(__file__).parent.parent / "infrastructure" / "gcp" / "load_balancer.tf"
        ]
        
        for network_file in network_files:
            if network_file.exists():
                content = network_file.read_text()
                
                # Should have private subnets
                assert "private" in content.lower(), f"Should define private subnets in {network_file}"
                
                # Should have proper CIDR blocks
                cidr_patterns = ["10.", "172.", "192.168."]
                has_private_cidr = any(pattern in content for pattern in cidr_patterns)
                assert has_private_cidr, f"Should use private CIDR blocks in {network_file}"

class TestMonitoringConfigurations:
    """Test monitoring and logging configurations"""
    
    def test_logging_configurations(self):
        """Test logging configurations"""
        infra_dirs = [
            Path(__file__).parent.parent / "infrastructure" / "aws",
            Path(__file__).parent.parent / "infrastructure" / "azure",
            Path(__file__).parent.parent / "infrastructure" / "gcp"
        ]
        
        for infra_dir in infra_dirs:
            if infra_dir.exists():
                tf_files = list(infra_dir.glob("*.tf"))
                
                has_logging = False
                for tf_file in tf_files:
                    content = tf_file.read_text()
                    
                    # Check for logging configurations
                    logging_keywords = ["log", "cloudwatch", "monitor", "audit"]
                    if any(keyword in content.lower() for keyword in logging_keywords):
                        has_logging = True
                        break
                
                assert has_logging, f"Infrastructure in {infra_dir} should have logging configurations"

class TestBackupConfigurations:
    """Test backup and disaster recovery configurations"""
    
    def test_backup_configurations(self):
        """Test backup configurations"""
        infra_dirs = [
            Path(__file__).parent.parent / "infrastructure" / "aws",
            Path(__file__).parent.parent / "infrastructure" / "azure",
            Path(__file__).parent.parent / "infrastructure" / "gcp"
        ]
        
        for infra_dir in infra_dirs:
            if infra_dir.exists():
                tf_files = list(infra_dir.glob("*.tf"))
                
                has_backup = False
                for tf_file in tf_files:
                    content = tf_file.read_text()
                    
                    # Check for backup configurations
                    backup_keywords = ["backup", "snapshot", "retention", "replica"]
                    if any(keyword in content.lower() for keyword in backup_keywords):
                        has_backup = True
                        break
                
                # Note: Not all infrastructure files need backup configs
                # This is more of a recommendation check
                if not has_backup:
                    print(f"Warning: No backup configurations found in {infra_dir}")

class TestComplianceConfigurations:
    """Test compliance-related configurations"""
    
    def test_tagging_standards(self):
        """Test resource tagging standards"""
        infra_dirs = [
            Path(__file__).parent.parent / "infrastructure" / "aws",
            Path(__file__).parent.parent / "infrastructure" / "azure",
            Path(__file__).parent.parent / "infrastructure" / "gcp"
        ]
        
        for infra_dir in infra_dirs:
            if infra_dir.exists():
                tf_files = list(infra_dir.glob("*.tf"))
                
                for tf_file in tf_files:
                    content = tf_file.read_text()
                    
                    # Check for resource definitions
                    if "resource " in content:
                        # Should have tags
                        has_tags = any(tag_keyword in content.lower() for tag_keyword in ["tags", "labels"])
                        
                        if has_tags:
                            # Should include environment and project tags
                            assert "environment" in content.lower(), f"Resources in {tf_file} should have environment tags"
                            assert any(project_keyword in content.lower() for project_keyword in ["project", "omnify"]), f"Resources in {tf_file} should have project identification"

# Test configuration
pytest_plugins = ["pytest_asyncio"]
