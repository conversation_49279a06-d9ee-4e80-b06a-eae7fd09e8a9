# Multi-stage Dockerfile for AWS variant
# Optimized for Graviton3 ARM64 architecture

# Build stage
FROM --platform=linux/arm64 python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements/aws.txt /tmp/requirements.txt
RUN pip install --upgrade pip && \
    pip install -r /tmp/requirements.txt

# Production stage
FROM --platform=linux/arm64 python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    CLOUD_VARIANT=aws \
    AI_ENGINE=manus_rl_bedrock

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Create app user
RUN groupadd -r omnify && useradd -r -g omnify omnify

# Create app directory
WORKDIR /app

# Copy application code
COPY --chown=omnify:omnify . .

# Copy AWS-specific configuration
COPY --chown=omnify:omnify config/aws/ config/
COPY --chown=omnify:omnify docker/aws/entrypoint.sh /entrypoint.sh

# Make entrypoint executable
RUN chmod +x /entrypoint.sh

# Create directories for models and logs
RUN mkdir -p /opt/models /app/logs && \
    chown -R omnify:omnify /opt/models /app/logs

# Switch to app user
USER omnify

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Set entrypoint
ENTRYPOINT ["/entrypoint.sh"]

# Default command
CMD ["uvicorn", "apps.core.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
