# Omnify Marketing Cloud - Frontend Dashboard Specification

## Overview
The Omnify dashboard is the primary interface for clients to monitor their AI-driven marketing automation. Built with React/Next.js and integrated with Retool for rapid development.

## Core Dashboard Components

### 1. **Executive Overview Dashboard**
**Route**: `/dashboard`
**API Endpoint**: `GET /api/v1/dashboard/overview`

**Key Metrics Cards**:
- Total Ad Spend (current period)
- Total Revenue Generated
- Current ROAS with trend indicator
- Current CAC with vs target comparison
- AI Decisions Made Today
- Estimated Savings This Month

**Charts**:
- ROAS Trend (7/30/90 day views)
- CAC vs Target Over Time
- Spend vs Revenue Correlation
- AI Decision Impact Timeline

### 2. **Campaign Performance Dashboard**
**Route**: `/campaigns`
**API Endpoint**: `GET /api/v1/dashboard/campaigns`

**Campaign Table**:
- Campaign Name & Platform
- Status (Active/Paused/AI Optimizing)
- Spend & Budget Utilization
- ROAS & CAC
- Last AI Optimization
- Alert Status

**Filters**:
- Platform (Google Ads, Meta Ads)
- Performance (High/Medium/Low ROAS)
- AI Status (Recently Optimized, Needs Attention)
- Date Range

### 3. **AI Agents Dashboard**
**Route**: `/ai-agents`
**API Endpoint**: `GET /api/v1/dashboard/ai-agents`

**Agent Status Cards**:
- ROI Engine X™ (Last Run, Decisions Today, Success Rate)
- Retention Reactor Pro™ (Customers Analyzed, At-Risk Identified)
- EngageSense Ultra™ (Segments Updated, Campaigns Triggered)

**AI Decision Timeline**:
- Chronological list of AI decisions
- Confidence scores and reasoning
- Impact measurements
- Manual override options

### 4. **Customer Intelligence Dashboard**
**Route**: `/customers`
**API Endpoint**: `GET /api/v1/dashboard/customer-segments`

**Segment Distribution**:
- Champion, Loyal, Potential Loyalist, At-Risk, Hibernating
- Segment sizes and percentages
- Average engagement scores
- Churn risk distribution

**Customer Actions**:
- Recent retention actions taken
- Email campaigns sent
- Success rates by action type

### 5. **Alerts & Notifications**
**Route**: `/alerts`
**API Endpoint**: `GET /api/v1/dashboard/alerts`

**Alert Management**:
- Active alerts by severity
- Alert history and resolution
- Notification preferences
- Alert rule configuration

## Technical Implementation

### Frontend Stack
```typescript
// Next.js 14 with App Router
// TypeScript for type safety
// Tailwind CSS for styling
// Recharts for data visualization
// React Query for API state management

// Example component structure:
interface DashboardOverview {
  client_id: string;
  total_spend: number;
  total_revenue: number;
  current_roas: number;
  current_cac: number;
  ai_decisions_today: number;
  estimated_savings: number;
  roas_trend: "improving" | "stable" | "declining";
}

const DashboardPage = () => {
  const { data: overview } = useQuery({
    queryKey: ['dashboard-overview'],
    queryFn: () => api.get('/dashboard/overview')
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <MetricCard 
        title="Total Spend" 
        value={overview?.total_spend} 
        format="currency" 
      />
      <MetricCard 
        title="ROAS" 
        value={overview?.current_roas} 
        trend={overview?.roas_trend}
        format="ratio" 
      />
      {/* More metric cards */}
    </div>
  );
};
```

### Retool Integration
```javascript
// Retool app configuration for rapid dashboard development

// Data Sources
const omnifyAPI = {
  baseURL: "http://localhost:8000/api/v1",
  headers: {
    "Authorization": "Bearer {{ current_user.jwt_token }}"
  }
};

// Dashboard Overview Query
const overviewQuery = {
  resource: omnifyAPI,
  endpoint: "/dashboard/overview",
  params: {
    client_id: "{{ current_user.client_id }}",
    days: "{{ dateRange.value || 7 }}"
  },
  runWhenPageLoads: true,
  runWhenInputsChange: true
};

// Campaign Performance Table
const campaignTable = {
  data: "{{ campaignQuery.data }}",
  columns: [
    { key: "campaign_name", label: "Campaign" },
    { key: "platform", label: "Platform" },
    { key: "spend", label: "Spend", format: "currency" },
    { key: "roas", label: "ROAS", format: "number" },
    { key: "alert_level", label: "Status", format: "badge" }
  ],
  pagination: true,
  sorting: true,
  filtering: true
};
```

## User Experience Flow

### 1. **Login Flow**
```
/login → Authentication → /dashboard (overview)
```

### 2. **Daily Workflow**
```
Dashboard Overview → Check Alerts → Review AI Decisions → Campaign Deep Dive
```

### 3. **Alert Response**
```
Alert Notification → Alert Details → Recommended Actions → Approve/Override
```

### 4. **Campaign Management**
```
Campaign List → Campaign Details → Performance Analysis → AI Recommendations
```

## Mobile Responsiveness
- Responsive design for tablet and mobile
- Key metrics accessible on mobile
- Touch-friendly alert management
- Simplified navigation for small screens

## Real-time Updates
- WebSocket connection for live updates
- Real-time alert notifications
- Live campaign performance updates
- AI decision notifications

## Security Features
- JWT token authentication
- Role-based access control
- Session timeout handling
- Secure API communication

## Performance Optimization
- React Query for caching
- Lazy loading for large datasets
- Optimized chart rendering
- Progressive loading for dashboard

## Accessibility
- WCAG 2.1 AA compliance
- Keyboard navigation
- Screen reader support
- High contrast mode

## Development Phases

### Phase 1: Core Dashboard (Week 1-2)
- Executive overview
- Basic campaign table
- Authentication flow

### Phase 2: AI Integration (Week 3)
- AI agent status
- Decision timeline
- Alert management

### Phase 3: Advanced Features (Week 4)
- Customer intelligence
- Advanced filtering
- Real-time updates

### Phase 4: Polish & Testing (Week 5)
- Mobile optimization
- Performance tuning
- User testing

## API Integration Examples

### Dashboard Data Fetching
```typescript
// Custom hooks for API integration
const useDashboardOverview = (clientId: string, days: number = 7) => {
  return useQuery({
    queryKey: ['dashboard', 'overview', clientId, days],
    queryFn: async () => {
      const response = await fetch(
        `/api/v1/dashboard/overview?client_id=${clientId}&days=${days}`,
        {
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        }
      );
      return response.json();
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });
};

const useCampaignPerformance = (clientId: string) => {
  return useQuery({
    queryKey: ['dashboard', 'campaigns', clientId],
    queryFn: async () => {
      const response = await fetch(
        `/api/v1/dashboard/campaigns?client_id=${clientId}`,
        {
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        }
      );
      return response.json();
    },
  });
};
```

### Real-time Alert Handling
```typescript
// WebSocket integration for real-time alerts
const useRealTimeAlerts = (clientId: string) => {
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:8000/ws/alerts/${clientId}`);
    
    ws.onmessage = (event) => {
      const alert = JSON.parse(event.data);
      setAlerts(prev => [alert, ...prev]);
      
      // Show toast notification
      toast({
        title: alert.title,
        description: alert.message,
        variant: alert.severity === 'critical' ? 'destructive' : 'default'
      });
    };

    return () => ws.close();
  }, [clientId]);

  return alerts;
};
```

This frontend specification provides a complete blueprint for building the client-facing dashboard that integrates with all the backend APIs we've implemented.
