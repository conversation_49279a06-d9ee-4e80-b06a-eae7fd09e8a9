# Omnify Marketing Cloud MVP

🚀 **AI-native SaaS platform for mid-market marketing automation** delivering measurable ROI improvements through autonomous AI agents.

## 🤖 Core AI Agents

### **ROI Engine X™** - CAC Optimization
- Real-time bid adjustments with 85%+ confidence thresholds
- Hybrid AI decision making (Manus RL + OpenAI GPT-4 fallback)
- Automated Google Ads & Meta Ads campaign optimization
- **Target**: 15-30% CAC reduction within 30 days

### **Retention Reactor Pro™** - Churn Prevention
- ML-based churn prediction with 80%+ accuracy
- Automated retention campaigns (email, discounts, personal outreach)
- Customer risk scoring and segmentation
- **Target**: 20-40% reduction in customer churn

### **EngageSense Ultra™** - Personalization at Scale
- Customer behavior scoring and segmentation
- AI-powered personalized content recommendations
- Automated email campaigns based on engagement patterns
- **Target**: 25-50% increase in customer engagement

## 🏗️ Production-Ready Tech Stack

- **Backend**: FastAPI + PostgreSQL + Redis + Alembic migrations
- **AI**: OpenAI GPT-4 + Manus RL (hybrid decision engine)
- **Automation**: n8n workflows (15-min to 6-hour cycles)
- **Monitoring**: Prometheus + Grafana + Advanced alerting
- **Dashboard**: Retool integration with real-time APIs
- **Infrastructure**: Docker + AWS ECS + Multi-level caching
- **Integrations**: Google Ads, Meta Ads, SendGrid, Slack

## Project Structure
```
/
├── apps/
│   ├── core/           # Business logic and API endpoints
│   ├── auth/           # Authentication service
│   └── dashboard/      # Dashboard backend
├── lib/
│   ├── ai/             # AI decision engines
│   ├── connectors/     # API connectors (Google Ads, Meta, CRM)
│   └── utils/          # Shared utilities
├── workflows/          # n8n workflow definitions
├── infrastructure/     # Terraform and Docker configs
├── tests/             # Test suites
└── docs/              # Documentation

```

## 🎬 LIVE DEMO (30 seconds to start!)

### **🚀 One-Command Demo (All Platforms):**
```bash
python omnify_demo.py
```
**✅ Automatic:** Port detection, dependency installation, browser launch
**✅ Features:** Live AI agents, real-time metrics, professional UI
**✅ Compatible:** Windows, macOS, Linux with Unicode handling

### **📍 Demo URLs (Auto-detected):**
- **Live Dashboard**: http://localhost:[PORT]/demo
- **API Documentation**: http://localhost:[PORT]/docs
- **Health Check**: http://localhost:[PORT]/health

### **🎯 Platform-Specific Launchers:**
```bash
# Windows
start_demo.bat

# macOS/Linux
./start_demo.sh

# PowerShell
.\start_demo.ps1
```

### **📚 Complete Demo Documentation:**
- **[Demo Documentation Index](DEMO_DOCUMENTATION_INDEX.md)** - All demo guides
- **[Universal Demo Guide](UNIVERSAL_DEMO_GUIDE.md)** - Cross-platform instructions
- **[Demo Scripts](demo_scripts/)** - Professional presentation scripts

---

## 🚀 Development Setup

### 1. **System Check**
```bash
# Run comprehensive system check
python scripts/start.py
```

### 2. **Environment Setup**
```bash
cp .env.example .env
# Edit .env with your API keys (OpenAI, Google Ads, Meta Ads)
```

### 3. **Start Infrastructure**
```bash
# Development environment
docker-compose up -d

# Production environment
docker-compose -f docker-compose.prod.yml up -d
```

### 4. **Initialize Database**
```bash
pip install -r requirements.txt
alembic upgrade head
```

### 5. **Start API**
```bash
# Development
uvicorn apps.core.main:app --reload

# Production
python scripts/deploy.py --environment production
```

### 6. **Access Services**
- **API Documentation**: http://localhost:8000/docs
- **Real-time Dashboard**: http://localhost:8000/api/v1/dashboard/overview?client_id=1
- **n8n Workflows**: http://localhost:5678 (admin/omnify123)
- **Grafana Monitoring**: http://localhost:3000 (admin/omnify123)
- **Prometheus Metrics**: http://localhost:9090
- **System Status**: http://localhost:8000/api/v1/system/status

## ✅ MVP Status (Production Ready!)

### **Phase 1: Foundation** ✅ COMPLETE
- ✅ **Data Integration**: Google Ads & Meta Ads connectors with OAuth2 + rate limiting
- ✅ **Core API**: FastAPI with JWT auth, rate limiting, structured logging
- ✅ **Database**: PostgreSQL with async SQLAlchemy + Alembic migrations
- ✅ **Infrastructure**: Docker development environment with monitoring

### **Phase 2A: AI Agent Suite** ✅ COMPLETE
- ✅ **ROI Engine X™**: Hybrid AI (Manus RL + OpenAI) with confidence thresholds
- ✅ **Retention Reactor Pro™**: ML churn prediction + automated retention actions
- ✅ **EngageSense Ultra™**: Customer behavior scoring + personalization engine
- ✅ **n8n Workflows**: Automated triggers for all three agents (15min-6hr cycles)

### **Phase 2B: Production Features** ✅ COMPLETE
- ✅ **Real-time Dashboard API**: Comprehensive endpoints for Retool integration
- ✅ **Advanced Email System**: SendGrid/SMTP with template management + fallback
- ✅ **Monitoring & Alerting**: Prometheus/Grafana + intelligent alert system
- ✅ **Client Onboarding**: Automated 7-day setup process with API validation
- ✅ **Data Import System**: Bulk import from CSV, Google Ads, Meta Ads
- ✅ **Feature Flags**: Advanced configuration management + A/B testing
- ✅ **Multi-level Caching**: Redis + in-memory with performance optimization
- ✅ **Production Deployment**: Automated scripts with rollback capability
- ✅ **Comprehensive Testing**: Unit tests + integration tests + system tests

### **Ready for Pilot Clients** 🎯
- 🚀 **Complete AI Agent Suite** operating autonomously
- 📊 **Real-time Analytics** with ROAS/CAC tracking
- 🔄 **Automated Workflows** running every 15 minutes to 6 hours
- 📧 **Intelligent Alerting** for performance issues
- 🎛️ **Production Infrastructure** with monitoring and scaling
- 📈 **Measurable ROI** tracking for client demonstrations

## Development Workflow
1. Feature branches from `main`
2. PR reviews required
3. Automated testing on CI/CD
4. Deploy to staging → production

## License
Proprietary - Omnify Marketing Cloud
