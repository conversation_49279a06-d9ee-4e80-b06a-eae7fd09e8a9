"""
Deployment tests for universal deployment system
Tests deployment scripts, configurations, and validation
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import json
import tempfile
from pathlib import Path

from scripts.universal_deploy import UniversalDeploymentOrchestrator, CloudProvider, DeploymentConfig
from scripts.enhanced_setup_wizard import EnhancedSetupWizard
from scripts.health_check import HealthChecker

class TestUniversalDeployment:
    """Test universal deployment orchestrator"""
    
    @pytest.fixture
    def deployment_orchestrator(self):
        """Create deployment orchestrator instance"""
        return UniversalDeploymentOrchestrator()
    
    @pytest.fixture
    def mock_cloud_layer(self):
        """Mock cloud abstraction layer"""
        with patch('scripts.universal_deploy.CloudAbstractionLayer') as mock_layer:
            mock_instance = AsyncMock()
            mock_layer.return_value = mock_instance
            
            # Mock successful deployment
            mock_instance.deploy_variant.return_value = {
                "status": "success",
                "infrastructure": {
                    "status": "success",
                    "endpoints": {
                        "api": "https://api.test.com",
                        "dashboard": "https://dashboard.test.com"
                    }
                }
            }
            
            yield mock_instance
    
    def test_deployment_config_creation(self, deployment_orchestrator):
        """Test deployment configuration creation"""
        config = DeploymentConfig(
            variant=CloudProvider.AWS,
            environment="test",
            region="us-east-1",
            ai_engine_config={"engine": "demo"},
            database_config={"type": "postgresql"},
            monitoring_config={"enabled": True},
            scaling_config={"min_instances": 1, "max_instances": 5}
        )
        
        assert config.variant == CloudProvider.AWS
        assert config.environment == "test"
        assert config.region == "us-east-1"
    
    @pytest.mark.asyncio
    async def test_deploy_aws_variant(self, deployment_orchestrator, mock_cloud_layer):
        """Test AWS variant deployment"""
        with patch.object(deployment_orchestrator, 'cloud_layer', mock_cloud_layer):
            with patch.multiple(
                deployment_orchestrator,
                _setup_secrets_management=AsyncMock(return_value={"status": "success"}),
                _setup_data_protection=AsyncMock(return_value={"status": "success"}),
                _setup_security_features=AsyncMock(return_value={"status": "success"}),
                _setup_compliance_monitoring=AsyncMock(return_value={"status": "success"}),
                _setup_comprehensive_monitoring=AsyncMock(return_value={"status": "success"}),
                _setup_cost_management=AsyncMock(return_value={"status": "success"}),
                _setup_backup_dr=AsyncMock(return_value={"status": "success"}),
                _setup_autoscaling=AsyncMock(return_value={"status": "success"}),
                _validate_comprehensive_deployment=AsyncMock(return_value={"status": "passed"}),
                _generate_comprehensive_deployment_report=Mock(return_value={"status": "success"})
            ):
                result = await deployment_orchestrator.deploy_variant("aws", "test")
                
                assert result["status"] == "success"
                assert result["variant"] == "aws"
                assert result["environment"] == "test"
                assert "deployment_phases" in result
    
    @pytest.mark.asyncio
    async def test_deploy_azure_variant(self, deployment_orchestrator, mock_cloud_layer):
        """Test Azure variant deployment"""
        with patch.object(deployment_orchestrator, 'cloud_layer', mock_cloud_layer):
            with patch.multiple(
                deployment_orchestrator,
                _setup_secrets_management=AsyncMock(return_value={"status": "success"}),
                _setup_data_protection=AsyncMock(return_value={"status": "success"}),
                _setup_security_features=AsyncMock(return_value={"status": "success"}),
                _setup_compliance_monitoring=AsyncMock(return_value={"status": "success"}),
                _setup_comprehensive_monitoring=AsyncMock(return_value={"status": "success"}),
                _setup_cost_management=AsyncMock(return_value={"status": "success"}),
                _setup_backup_dr=AsyncMock(return_value={"status": "success"}),
                _setup_autoscaling=AsyncMock(return_value={"status": "success"}),
                _validate_comprehensive_deployment=AsyncMock(return_value={"status": "passed"}),
                _generate_comprehensive_deployment_report=Mock(return_value={"status": "success"})
            ):
                result = await deployment_orchestrator.deploy_variant("azure", "test")
                
                assert result["status"] == "success"
                assert result["variant"] == "azure"
    
    @pytest.mark.asyncio
    async def test_deploy_gcp_variant(self, deployment_orchestrator, mock_cloud_layer):
        """Test GCP variant deployment"""
        with patch.object(deployment_orchestrator, 'cloud_layer', mock_cloud_layer):
            with patch.multiple(
                deployment_orchestrator,
                _setup_secrets_management=AsyncMock(return_value={"status": "success"}),
                _setup_data_protection=AsyncMock(return_value={"status": "success"}),
                _setup_security_features=AsyncMock(return_value={"status": "success"}),
                _setup_compliance_monitoring=AsyncMock(return_value={"status": "success"}),
                _setup_comprehensive_monitoring=AsyncMock(return_value={"status": "success"}),
                _setup_cost_management=AsyncMock(return_value={"status": "success"}),
                _setup_backup_dr=AsyncMock(return_value={"status": "success"}),
                _setup_autoscaling=AsyncMock(return_value={"status": "success"}),
                _validate_comprehensive_deployment=AsyncMock(return_value={"status": "passed"}),
                _generate_comprehensive_deployment_report=Mock(return_value={"status": "success"})
            ):
                result = await deployment_orchestrator.deploy_variant("gcp", "test")
                
                assert result["status"] == "success"
                assert result["variant"] == "gcp"
    
    @pytest.mark.asyncio
    async def test_deployment_failure_handling(self, deployment_orchestrator):
        """Test deployment failure handling"""
        with patch.object(deployment_orchestrator, 'cloud_layer') as mock_layer:
            mock_layer.deploy_variant.side_effect = Exception("Deployment failed")
            
            result = await deployment_orchestrator.deploy_variant("aws", "test")
            
            assert result["status"] == "failed"
            assert "error" in result
    
    def test_get_enabled_features(self, deployment_orchestrator):
        """Test enabled features listing"""
        features = deployment_orchestrator._get_enabled_features("aws", "prod")
        
        assert isinstance(features, list)
        assert len(features) > 10  # Should have many features
        assert "Multi-cloud architecture" in features
        assert "AWS KMS encryption" in features
    
    @pytest.mark.asyncio
    async def test_secrets_management_setup(self, deployment_orchestrator):
        """Test secrets management setup"""
        with patch('lib.cloud.secrets_manager.UniversalSecretsManager') as mock_manager:
            mock_instance = AsyncMock()
            mock_manager.return_value = mock_instance
            mock_instance.setup_omnify_secrets.return_value = {"status": "success"}
            
            result = await deployment_orchestrator._setup_secrets_management("aws", "test", "us-east-1")
            
            assert result["status"] == "success"
            mock_instance.setup_omnify_secrets.assert_called_once_with("test")

class TestEnhancedSetupWizard:
    """Test enhanced setup wizard"""
    
    @pytest.fixture
    def setup_wizard(self):
        """Create setup wizard instance"""
        return EnhancedSetupWizard()
    
    @pytest.mark.asyncio
    async def test_business_profile_analysis(self, setup_wizard):
        """Test business profile analysis"""
        business_profile = {
            "company_size": "startup",
            "industry": "technology",
            "monthly_budget": 5000,
            "compliance_requirements": ["gdpr"],
            "technical_expertise": "medium",
            "growth_stage": "early"
        }
        
        with patch.object(setup_wizard, '_analyze_business_profile') as mock_analyze:
            mock_analyze.return_value = {
                "recommended_variant": "aws",
                "reasoning": "Cost-effective for startups",
                "confidence": 0.85
            }
            
            result = await setup_wizard.analyze_business_profile(business_profile)
            
            assert "recommended_variant" in result
            assert result["confidence"] > 0.8
    
    @pytest.mark.asyncio
    async def test_cloud_variant_recommendation(self, setup_wizard):
        """Test cloud variant recommendation"""
        requirements = {
            "budget": 10000,
            "compliance": ["gdpr", "soc2"],
            "regions": ["us", "eu"],
            "ai_requirements": "advanced"
        }
        
        with patch.object(setup_wizard, '_get_cloud_recommendations') as mock_recommend:
            mock_recommend.return_value = [
                {
                    "variant": "aws",
                    "score": 0.9,
                    "pros": ["Mature AI services", "Global presence"],
                    "cons": ["Higher complexity"]
                },
                {
                    "variant": "azure",
                    "score": 0.85,
                    "pros": ["Good enterprise integration"],
                    "cons": ["Limited AI services"]
                }
            ]
            
            result = await setup_wizard.get_cloud_recommendations(requirements)
            
            assert isinstance(result, list)
            assert len(result) >= 2
            assert result[0]["score"] >= result[1]["score"]  # Should be sorted by score

class TestHealthChecker:
    """Test health checking functionality"""
    
    @pytest.fixture
    def health_checker(self):
        """Create health checker instance"""
        return HealthChecker()
    
    @pytest.mark.asyncio
    async def test_api_health_check(self, health_checker):
        """Test API health check"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"status": "healthy"}
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            result = await health_checker.check_api_health("http://localhost:8000")
            
            assert result["status"] == "healthy"
            assert result["response_time"] is not None
    
    @pytest.mark.asyncio
    async def test_database_health_check(self, health_checker):
        """Test database health check"""
        with patch('asyncpg.connect') as mock_connect:
            mock_conn = AsyncMock()
            mock_connect.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchval.return_value = 1
            
            result = await health_checker.check_database_health("postgresql://test")
            
            assert result["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_redis_health_check(self, health_checker):
        """Test Redis health check"""
        with patch('redis.asyncio.from_url') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            mock_client.ping.return_value = True
            
            result = await health_checker.check_redis_health("redis://localhost:6379")
            
            assert result["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_comprehensive_health_check(self, health_checker):
        """Test comprehensive health check"""
        with patch.multiple(
            health_checker,
            check_api_health=AsyncMock(return_value={"status": "healthy"}),
            check_database_health=AsyncMock(return_value={"status": "healthy"}),
            check_redis_health=AsyncMock(return_value={"status": "healthy"}),
            check_ai_services=AsyncMock(return_value={"status": "healthy"}),
            check_monitoring=AsyncMock(return_value={"status": "healthy"}),
            check_security=AsyncMock(return_value={"status": "healthy"}),
            check_compliance=AsyncMock(return_value={"status": "healthy"}),
            check_backup_systems=AsyncMock(return_value={"status": "healthy"})
        ):
            result = await health_checker.run_comprehensive_health_check("aws", "test")
            
            assert result["overall_status"] == "healthy"
            assert result["success_rate"] == 100.0
            assert len(result["checks"]) >= 8

class TestDeploymentValidation:
    """Test deployment validation"""
    
    @pytest.mark.asyncio
    async def test_infrastructure_validation(self):
        """Test infrastructure validation"""
        deployment_result = {
            "infrastructure": {
                "status": "success",
                "endpoints": {
                    "api": "https://api.test.com",
                    "dashboard": "https://dashboard.test.com"
                }
            }
        }
        
        # Mock validation checks
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            # This would be part of the deployment orchestrator
            # For now, just test the concept
            assert deployment_result["infrastructure"]["status"] == "success"
    
    @pytest.mark.asyncio
    async def test_security_validation(self):
        """Test security validation"""
        # Test that security features are properly configured
        security_checks = [
            "encryption_enabled",
            "access_controls_configured",
            "network_security_applied",
            "secrets_management_active",
            "audit_logging_enabled"
        ]
        
        # Mock security validation
        validation_results = {}
        for check in security_checks:
            validation_results[check] = True
        
        assert all(validation_results.values()), "All security checks should pass"
    
    @pytest.mark.asyncio
    async def test_compliance_validation(self):
        """Test compliance validation"""
        # Test that compliance features are properly configured
        compliance_checks = [
            "gdpr_compliance",
            "audit_logging",
            "data_retention_policies",
            "access_controls",
            "encryption_standards"
        ]
        
        # Mock compliance validation
        validation_results = {}
        for check in compliance_checks:
            validation_results[check] = True
        
        assert all(validation_results.values()), "All compliance checks should pass"

class TestDeploymentRollback:
    """Test deployment rollback functionality"""
    
    @pytest.mark.asyncio
    async def test_rollback_on_failure(self):
        """Test rollback on deployment failure"""
        # Mock a failed deployment that needs rollback
        deployment_state = {
            "infrastructure_created": True,
            "application_deployed": False,
            "monitoring_configured": False
        }
        
        # Mock rollback process
        rollback_steps = []
        if deployment_state["monitoring_configured"]:
            rollback_steps.append("remove_monitoring")
        if deployment_state["application_deployed"]:
            rollback_steps.append("remove_application")
        if deployment_state["infrastructure_created"]:
            rollback_steps.append("destroy_infrastructure")
        
        # Should rollback infrastructure since application deployment failed
        assert "destroy_infrastructure" in rollback_steps
        assert "remove_application" not in rollback_steps  # Wasn't deployed
    
    @pytest.mark.asyncio
    async def test_partial_rollback(self):
        """Test partial rollback functionality"""
        # Test that we can rollback specific components
        components_to_rollback = ["monitoring", "application"]
        
        # Mock rollback execution
        rollback_results = {}
        for component in components_to_rollback:
            rollback_results[component] = {"status": "success"}
        
        assert all(result["status"] == "success" for result in rollback_results.values())

# Test configuration
pytest_plugins = ["pytest_asyncio"]
