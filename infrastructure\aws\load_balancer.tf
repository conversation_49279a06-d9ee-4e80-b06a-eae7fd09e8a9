# Application Load Balancer for AWS variant
# Provides high availability and SSL termination

# Application Load Balancer
resource "aws_lb" "omnify_alb" {
  name               = "omnify-alb-${var.environment}"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = aws_subnet.public_subnets[*].id

  enable_deletion_protection = var.environment == "prod" ? true : false

  tags = {
    Name        = "omnify-alb-${var.environment}"
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Security Group for ALB
resource "aws_security_group" "alb_sg" {
  name_prefix = "omnify-alb-${var.environment}"
  vpc_id      = aws_vpc.omnify_vpc.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "omnify-alb-sg-${var.environment}"
    Environment = var.environment
  }
}

# Target Group for API
resource "aws_lb_target_group" "omnify_api_tg" {
  name     = "omnify-api-tg-${var.environment}"
  port     = 8000
  protocol = "HTTP"
  vpc_id   = aws_vpc.omnify_vpc.id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 30
    path                = "/health"
    matcher             = "200"
    port                = "traffic-port"
    protocol            = "HTTP"
  }

  tags = {
    Name        = "omnify-api-tg-${var.environment}"
    Environment = var.environment
  }
}

# SSL Certificate
resource "aws_acm_certificate" "omnify_cert" {
  domain_name       = var.environment == "prod" ? "api.omnify.com" : "api-${var.environment}.omnify.com"
  validation_method = "DNS"

  subject_alternative_names = [
    var.environment == "prod" ? "app.omnify.com" : "app-${var.environment}.omnify.com",
    var.environment == "prod" ? "*.omnify.com" : "*.${var.environment}.omnify.com"
  ]

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Name        = "omnify-cert-${var.environment}"
    Environment = var.environment
  }
}

# HTTPS Listener
resource "aws_lb_listener" "omnify_https" {
  load_balancer_arn = aws_lb.omnify_alb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  certificate_arn   = aws_acm_certificate.omnify_cert.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.omnify_api_tg.arn
  }
}

# HTTP Listener (redirect to HTTPS)
resource "aws_lb_listener" "omnify_http" {
  load_balancer_arn = aws_lb.omnify_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

# CloudFront Distribution
resource "aws_cloudfront_distribution" "omnify_cdn" {
  origin {
    domain_name = aws_lb.omnify_alb.dns_name
    origin_id   = "omnify-alb-${var.environment}"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = "Omnify Marketing Cloud CDN - ${var.environment}"
  default_root_object = "index.html"

  aliases = var.environment == "prod" ? ["app.omnify.com", "api.omnify.com"] : ["app-${var.environment}.omnify.com", "api-${var.environment}.omnify.com"]

  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "omnify-alb-${var.environment}"

    forwarded_values {
      query_string = true
      headers      = ["Authorization", "Content-Type", "X-Forwarded-For"]

      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
    compress               = true
  }

  # API cache behavior
  ordered_cache_behavior {
    path_pattern     = "/api/*"
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "omnify-alb-${var.environment}"

    forwarded_values {
      query_string = true
      headers      = ["*"]

      cookies {
        forward = "all"
      }
    }

    min_ttl                = 0
    default_ttl            = 0
    max_ttl                = 0
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  # Static assets cache behavior
  ordered_cache_behavior {
    path_pattern     = "/static/*"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "omnify-alb-${var.environment}"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    min_ttl                = 86400
    default_ttl            = 86400
    max_ttl                = 31536000
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  price_class = var.environment == "prod" ? "PriceClass_All" : "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate.omnify_cert.arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  tags = {
    Name        = "omnify-cdn-${var.environment}"
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# WAF Web ACL
resource "aws_wafv2_web_acl" "omnify_waf" {
  name  = "omnify-waf-${var.environment}"
  scope = "CLOUDFRONT"

  default_action {
    allow {}
  }

  # Rate limiting rule
  rule {
    name     = "RateLimitRule"
    priority = 1

    override_action {
      none {}
    }

    statement {
      rate_based_statement {
        limit              = 2000
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                 = "RateLimitRule"
      sampled_requests_enabled    = true
    }

    action {
      block {}
    }
  }

  # AWS Managed Rules
  rule {
    name     = "AWSManagedRulesCommonRuleSet"
    priority = 2

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                 = "CommonRuleSetMetric"
      sampled_requests_enabled    = true
    }
  }

  tags = {
    Name        = "omnify-waf-${var.environment}"
    Environment = var.environment
  }
}

# Associate WAF with CloudFront
resource "aws_wafv2_web_acl_association" "omnify_waf_association" {
  resource_arn = aws_cloudfront_distribution.omnify_cdn.arn
  web_acl_arn  = aws_wafv2_web_acl.omnify_waf.arn
}

# Route53 DNS Records
resource "aws_route53_zone" "omnify_zone" {
  count = var.environment == "prod" ? 1 : 0
  name  = "omnify.com"

  tags = {
    Name        = "omnify-zone"
    Environment = var.environment
  }
}

resource "aws_route53_record" "omnify_api" {
  count   = var.environment == "prod" ? 1 : 0
  zone_id = aws_route53_zone.omnify_zone[0].zone_id
  name    = "api.omnify.com"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.omnify_cdn.domain_name
    zone_id                = aws_cloudfront_distribution.omnify_cdn.hosted_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "omnify_app" {
  count   = var.environment == "prod" ? 1 : 0
  zone_id = aws_route53_zone.omnify_zone[0].zone_id
  name    = "app.omnify.com"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.omnify_cdn.domain_name
    zone_id                = aws_cloudfront_distribution.omnify_cdn.hosted_zone_id
    evaluate_target_health = false
  }
}

# Outputs
output "load_balancer_dns" {
  description = "Load balancer DNS name"
  value       = aws_lb.omnify_alb.dns_name
}

output "cloudfront_domain" {
  description = "CloudFront distribution domain"
  value       = aws_cloudfront_distribution.omnify_cdn.domain_name
}

output "target_group_arn" {
  description = "Target group ARN"
  value       = aws_lb_target_group.omnify_api_tg.arn
}
