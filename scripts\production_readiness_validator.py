#!/usr/bin/env python3
"""
Production Readiness Validator for Omnify Marketing Cloud
Validates that all systems are ready for production deployment
"""
import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
import structlog

logger = structlog.get_logger()

class ProductionReadinessValidator:
    """Validates production readiness across all systems"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.validation_results = {}
        
    async def validate_production_readiness(self, cloud_variant: str = "aws", environment: str = "prod") -> Dict[str, Any]:
        """Comprehensive production readiness validation"""
        print("🎯 OMNIFY PRODUCTION READINESS VALIDATION")
        print("=" * 60)
        print(f"Cloud Variant: {cloud_variant}")
        print(f"Environment: {environment}")
        print(f"Validation Time: {datetime.utcnow().isoformat()}")
        print("=" * 60)
        
        # Critical validation categories
        validation_categories = [
            ("Security Readiness", self._validate_security_readiness),
            ("Compliance Readiness", self._validate_compliance_readiness),
            ("Infrastructure Readiness", self._validate_infrastructure_readiness),
            ("Application Readiness", self._validate_application_readiness),
            ("Monitoring Readiness", self._validate_monitoring_readiness),
            ("Backup & DR Readiness", self._validate_backup_dr_readiness),
            ("Performance Readiness", self._validate_performance_readiness),
            ("Operational Readiness", self._validate_operational_readiness),
            ("Documentation Readiness", self._validate_documentation_readiness),
            ("Deployment Readiness", self._validate_deployment_readiness)
        ]
        
        total_categories = len(validation_categories)
        passed_categories = 0
        critical_failures = []
        
        for i, (category_name, validation_func) in enumerate(validation_categories, 1):
            print(f"\n🔍 [{i}/{total_categories}] {category_name}")
            print("-" * 40)
            
            try:
                result = await validation_func(cloud_variant, environment)
                self.validation_results[category_name] = result
                
                if result.get("status") == "ready":
                    passed_categories += 1
                    print(f"✅ {category_name}: READY")
                    if result.get("warnings"):
                        for warning in result["warnings"]:
                            print(f"   ⚠️  {warning}")
                else:
                    print(f"❌ {category_name}: NOT READY")
                    if result.get("critical_issues"):
                        critical_failures.extend(result["critical_issues"])
                        for issue in result["critical_issues"]:
                            print(f"   🚨 {issue}")
                    if result.get("issues"):
                        for issue in result["issues"]:
                            print(f"   ❌ {issue}")
                
            except Exception as e:
                print(f"❌ {category_name}: VALIDATION ERROR - {str(e)}")
                self.validation_results[category_name] = {
                    "status": "error",
                    "error": str(e),
                    "critical_issues": [f"Validation error: {str(e)}"]
                }
                critical_failures.append(f"{category_name}: {str(e)}")
        
        # Generate final assessment
        return self._generate_production_assessment(
            passed_categories, total_categories, critical_failures
        )
    
    async def _validate_security_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate security readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check encryption configuration
        try:
            from lib.security.encryption_manager import DataProtectionManager
            protection_manager = DataProtectionManager(cloud_variant)
            
            # Test encryption functionality
            test_data = {"test": "data"}
            encrypted = await protection_manager.encrypt_sensitive_data(test_data, "confidential")
            if not encrypted.get("encrypted"):
                critical_issues.append("Data encryption not working")
        except Exception as e:
            critical_issues.append(f"Encryption system error: {str(e)}")
        
        # Check secrets management
        try:
            from lib.cloud.secrets_manager import UniversalSecretsManager
            secrets_manager = UniversalSecretsManager(cloud_variant)
            # Assume secrets are configured if no error
        except Exception as e:
            critical_issues.append(f"Secrets management error: {str(e)}")
        
        # Check security configurations
        security_files = [
            "lib/security/encryption_manager.py",
            "infrastructure/aws/network_security.tf",
            "infrastructure/aws/iam.tf"
        ]
        
        for security_file in security_files:
            if not (self.project_root / security_file).exists():
                issues.append(f"Missing security file: {security_file}")
        
        # Check environment variables
        env_example = self.project_root / ".env.example"
        if env_example.exists():
            content = env_example.read_text()
            required_secrets = ["DATABASE_URL", "REDIS_URL", "SECRET_KEY"]
            for secret in required_secrets:
                if secret not in content:
                    warnings.append(f"Missing environment variable example: {secret}")
        
        status = "ready" if not critical_issues and len(issues) == 0 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": [
                "encryption_functionality",
                "secrets_management",
                "security_configurations",
                "environment_variables"
            ]
        }
    
    async def _validate_compliance_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate compliance readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check compliance manager
        try:
            from lib.compliance.audit_manager import UniversalComplianceManager
            compliance_manager = UniversalComplianceManager(cloud_variant)
            
            # Test audit logging
            log_result = await compliance_manager.log_user_action(
                user_id="test_user",
                action="validation_test",
                resource="system",
                ip_address="127.0.0.1"
            )
            
            if not log_result:
                critical_issues.append("Audit logging not functional")
                
        except Exception as e:
            critical_issues.append(f"Compliance system error: {str(e)}")
        
        # Check GDPR compliance features
        compliance_file = self.project_root / "lib/compliance/audit_manager.py"
        if compliance_file.exists():
            content = compliance_file.read_text()
            gdpr_features = ["gdpr_request", "data_export", "data_deletion"]
            for feature in gdpr_features:
                if feature not in content.lower():
                    issues.append(f"Missing GDPR feature: {feature}")
        else:
            critical_issues.append("Compliance manager not found")
        
        # Check data retention policies
        if "data_retention" not in content.lower():
            warnings.append("Data retention policies not explicitly defined")
        
        status = "ready" if not critical_issues and len(issues) <= 1 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": [
                "audit_logging",
                "gdpr_compliance",
                "data_retention_policies"
            ]
        }
    
    async def _validate_infrastructure_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate infrastructure readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check Terraform configurations
        infra_dir = self.project_root / "infrastructure" / cloud_variant
        if not infra_dir.exists():
            critical_issues.append(f"Infrastructure configuration missing for {cloud_variant}")
            return {
                "status": "not_ready",
                "critical_issues": critical_issues,
                "issues": [],
                "warnings": []
            }
        
        # Check required infrastructure files
        required_files = ["main.tf", "variables.tf", "outputs.tf"]
        for req_file in required_files:
            if not (infra_dir / req_file).exists():
                issues.append(f"Missing infrastructure file: {req_file}")
        
        # Check for security configurations
        security_files = ["network_security.tf", "iam.tf"]
        for sec_file in security_files:
            if not (infra_dir / sec_file).exists():
                warnings.append(f"Missing security configuration: {sec_file}")
        
        # Check for monitoring configurations
        if not (infra_dir / "monitoring.tf").exists():
            warnings.append("Missing monitoring configuration")
        
        # Check for backup configurations
        if not (infra_dir / "backup.tf").exists():
            warnings.append("Missing backup configuration")
        
        status = "ready" if not critical_issues and len(issues) <= 1 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": [
                "terraform_configurations",
                "security_configurations",
                "monitoring_configurations",
                "backup_configurations"
            ]
        }
    
    async def _validate_application_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate application readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check core application files
        core_files = [
            "apps/core/main.py",
            "apps/core/database.py",
            "lib/ai/decision_engine.py"
        ]
        
        for core_file in core_files:
            if not (self.project_root / core_file).exists():
                critical_issues.append(f"Missing core application file: {core_file}")
        
        # Check Docker configurations
        docker_file = self.project_root / "docker" / cloud_variant / "Dockerfile"
        if not docker_file.exists():
            issues.append(f"Missing Dockerfile for {cloud_variant}")
        
        # Check requirements
        requirements_file = self.project_root / "requirements.txt"
        if not requirements_file.exists():
            critical_issues.append("Missing requirements.txt")
        
        # Check AI agents
        ai_agents = [
            "lib/ai/decision_engine.py",
            "lib/ai/retention_reactor.py",
            "lib/ai/engage_sense.py"
        ]
        
        for agent in ai_agents:
            if not (self.project_root / agent).exists():
                issues.append(f"Missing AI agent: {agent}")
        
        status = "ready" if not critical_issues and len(issues) <= 1 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": [
                "core_application_files",
                "docker_configurations",
                "dependencies",
                "ai_agents"
            ]
        }
    
    async def _validate_monitoring_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate monitoring readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check monitoring manager
        try:
            from lib.cloud.monitoring_manager import UniversalMonitoringManager
            monitoring_manager = UniversalMonitoringManager(cloud_variant)
        except Exception as e:
            critical_issues.append(f"Monitoring system error: {str(e)}")
        
        # Check monitoring configuration
        monitoring_file = self.project_root / "lib/cloud/monitoring_manager.py"
        if not monitoring_file.exists():
            critical_issues.append("Monitoring manager not found")
        
        # Check health check system
        health_check_file = self.project_root / "scripts/health_check.py"
        if not health_check_file.exists():
            issues.append("Health check system not found")
        
        status = "ready" if not critical_issues and len(issues) == 0 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": [
                "monitoring_manager",
                "health_check_system"
            ]
        }
    
    async def _validate_backup_dr_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate backup and disaster recovery readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check backup manager
        try:
            from lib.cloud.backup_manager import UniversalBackupManager
            backup_manager = UniversalBackupManager(cloud_variant)
        except Exception as e:
            critical_issues.append(f"Backup system error: {str(e)}")
        
        # Check backup configuration
        backup_file = self.project_root / "lib/cloud/backup_manager.py"
        if not backup_file.exists():
            critical_issues.append("Backup manager not found")
        
        status = "ready" if not critical_issues else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": ["backup_manager"]
        }
    
    async def _validate_performance_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate performance readiness"""
        issues = []
        warnings = []
        
        # Check auto-scaling configuration
        autoscaling_file = self.project_root / "infrastructure" / cloud_variant / "autoscaling.tf"
        if not autoscaling_file.exists():
            warnings.append("Auto-scaling configuration not found")
        
        # Check load balancer configuration
        lb_file = self.project_root / "infrastructure" / cloud_variant / "load_balancer.tf"
        if not lb_file.exists():
            issues.append("Load balancer configuration not found")
        
        status = "ready" if len(issues) == 0 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": [],
            "checks_performed": [
                "autoscaling_configuration",
                "load_balancer_configuration"
            ]
        }
    
    async def _validate_operational_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate operational readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check deployment scripts
        deploy_script = self.project_root / "scripts/universal_deploy.py"
        if not deploy_script.exists():
            critical_issues.append("Universal deployment script not found")
        
        # Check CI/CD configuration
        ci_config = self.project_root / ".github/workflows/multi-cloud-deploy.yml"
        if not ci_config.exists():
            warnings.append("CI/CD configuration not found")
        
        # Check cost management
        cost_manager = self.project_root / "lib/cloud/cost_manager.py"
        if not cost_manager.exists():
            issues.append("Cost management system not found")
        
        status = "ready" if not critical_issues and len(issues) <= 1 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": [
                "deployment_scripts",
                "ci_cd_configuration",
                "cost_management"
            ]
        }
    
    async def _validate_documentation_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate documentation readiness"""
        issues = []
        warnings = []
        
        # Check README
        readme = self.project_root / "README.md"
        if not readme.exists():
            issues.append("README.md not found")
        
        # Check API documentation
        if not (self.project_root / "docs").exists():
            warnings.append("Documentation directory not found")
        
        status = "ready" if len(issues) == 0 else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": [],
            "checks_performed": [
                "readme_documentation",
                "api_documentation"
            ]
        }
    
    async def _validate_deployment_readiness(self, cloud_variant: str, environment: str) -> Dict[str, Any]:
        """Validate deployment readiness"""
        issues = []
        warnings = []
        critical_issues = []
        
        # Check deployment orchestrator
        try:
            from scripts.universal_deploy import UniversalDeploymentOrchestrator
            orchestrator = UniversalDeploymentOrchestrator()
            
            # Check if cloud variant is supported
            if cloud_variant not in orchestrator.deployment_configs:
                critical_issues.append(f"Cloud variant {cloud_variant} not supported")
                
        except Exception as e:
            critical_issues.append(f"Deployment orchestrator error: {str(e)}")
        
        status = "ready" if not critical_issues else "not_ready"
        
        return {
            "status": status,
            "issues": issues,
            "warnings": warnings,
            "critical_issues": critical_issues,
            "checks_performed": ["deployment_orchestrator"]
        }
    
    def _generate_production_assessment(self, passed_categories: int, total_categories: int, critical_failures: List[str]) -> Dict[str, Any]:
        """Generate final production readiness assessment"""
        success_rate = (passed_categories / total_categories) * 100
        
        # Determine readiness level
        if success_rate >= 90 and not critical_failures:
            readiness_level = "PRODUCTION_READY"
            recommendation = "✅ READY FOR PRODUCTION DEPLOYMENT"
        elif success_rate >= 80 and len(critical_failures) <= 1:
            readiness_level = "MOSTLY_READY"
            recommendation = "⚠️  MOSTLY READY - Address critical issues before production"
        elif success_rate >= 70:
            readiness_level = "NEEDS_WORK"
            recommendation = "🔧 NEEDS WORK - Significant issues must be resolved"
        else:
            readiness_level = "NOT_READY"
            recommendation = "❌ NOT READY FOR PRODUCTION"
        
        print(f"\n🎯 PRODUCTION READINESS ASSESSMENT")
        print("=" * 60)
        print(f"Readiness Level: {readiness_level}")
        print(f"Categories Passed: {passed_categories}/{total_categories} ({success_rate:.1f}%)")
        print(f"Critical Failures: {len(critical_failures)}")
        print(f"Recommendation: {recommendation}")
        
        if critical_failures:
            print(f"\n🚨 CRITICAL ISSUES TO RESOLVE:")
            for failure in critical_failures:
                print(f"   • {failure}")
        
        return {
            "readiness_level": readiness_level,
            "success_rate": success_rate,
            "categories_passed": passed_categories,
            "total_categories": total_categories,
            "critical_failures": critical_failures,
            "recommendation": recommendation,
            "detailed_results": self.validation_results,
            "validation_time": datetime.utcnow().isoformat()
        }

async def main():
    """Main validation function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Omnify Production Readiness Validator")
    parser.add_argument("--cloud-variant", default="aws", choices=["aws", "azure", "gcp", "multi"])
    parser.add_argument("--environment", default="prod", choices=["prod", "staging"])
    parser.add_argument("--output", help="Output file for validation results (JSON)")
    
    args = parser.parse_args()
    
    validator = ProductionReadinessValidator()
    results = await validator.validate_production_readiness(args.cloud_variant, args.environment)
    
    if args.output:
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"\n📄 Validation results saved to: {args.output}")
    
    # Exit with appropriate code
    sys.exit(0 if results["readiness_level"] == "PRODUCTION_READY" else 1)

if __name__ == "__main__":
    asyncio.run(main())
