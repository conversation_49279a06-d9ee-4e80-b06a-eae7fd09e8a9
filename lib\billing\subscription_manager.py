"""
Billing and Subscription Management for Omnify Marketing Cloud
"""
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from enum import Enum
import structlog
from pydantic import BaseModel, Field
import stripe
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from apps.core.database import Client, get_db
from apps.core.config import settings

logger = structlog.get_logger()

# Configure Stripe
stripe.api_key = getattr(settings, 'STRIPE_SECRET_KEY', 'sk_test_...')

class SubscriptionPlan(str, Enum):
    """Subscription plan types"""
    TRIAL = "trial"
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"

class SubscriptionStatus(str, Enum):
    """Subscription status"""
    TRIAL = "trial"
    ACTIVE = "active"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    UNPAID = "unpaid"

class PlanFeatures(BaseModel):
    """Plan feature limits"""
    max_campaigns: int
    max_monthly_spend: float
    max_customers: int
    ai_decisions_per_month: int
    email_sends_per_month: int
    advanced_analytics: bool
    priority_support: bool
    custom_integrations: bool

class SubscriptionPlanConfig(BaseModel):
    """Subscription plan configuration"""
    plan_id: str
    name: str
    description: str
    price_monthly: float
    price_yearly: float
    stripe_price_id_monthly: str
    stripe_price_id_yearly: str
    features: PlanFeatures
    is_popular: bool = False

class UsageMetrics(BaseModel):
    """Current usage metrics"""
    campaigns_count: int
    monthly_spend: float
    customers_count: int
    ai_decisions_this_month: int
    email_sends_this_month: int
    storage_used_gb: float

class SubscriptionInfo(BaseModel):
    """Complete subscription information"""
    client_id: str
    plan: SubscriptionPlan
    status: SubscriptionStatus
    current_period_start: datetime
    current_period_end: datetime
    trial_end: Optional[datetime]
    stripe_subscription_id: Optional[str]
    stripe_customer_id: Optional[str]
    usage: UsageMetrics
    plan_config: SubscriptionPlanConfig
    usage_warnings: List[str] = Field(default_factory=list)

class BillingManager:
    """Manages billing and subscriptions"""
    
    def __init__(self):
        self.plan_configs = self._initialize_plan_configs()
    
    def _initialize_plan_configs(self) -> Dict[str, SubscriptionPlanConfig]:
        """Initialize subscription plan configurations"""
        return {
            SubscriptionPlan.TRIAL: SubscriptionPlanConfig(
                plan_id="trial",
                name="Free Trial",
                description="14-day free trial with full access",
                price_monthly=0.0,
                price_yearly=0.0,
                stripe_price_id_monthly="",
                stripe_price_id_yearly="",
                features=PlanFeatures(
                    max_campaigns=5,
                    max_monthly_spend=10000.0,
                    max_customers=1000,
                    ai_decisions_per_month=1000,
                    email_sends_per_month=5000,
                    advanced_analytics=True,
                    priority_support=False,
                    custom_integrations=False
                )
            ),
            SubscriptionPlan.STARTER: SubscriptionPlanConfig(
                plan_id="starter",
                name="Starter",
                description="Perfect for small businesses getting started",
                price_monthly=497.0,
                price_yearly=4970.0,  # 2 months free
                stripe_price_id_monthly="price_starter_monthly",
                stripe_price_id_yearly="price_starter_yearly",
                features=PlanFeatures(
                    max_campaigns=10,
                    max_monthly_spend=25000.0,
                    max_customers=5000,
                    ai_decisions_per_month=5000,
                    email_sends_per_month=25000,
                    advanced_analytics=True,
                    priority_support=False,
                    custom_integrations=False
                )
            ),
            SubscriptionPlan.PROFESSIONAL: SubscriptionPlanConfig(
                plan_id="professional",
                name="Professional",
                description="Advanced features for growing businesses",
                price_monthly=997.0,
                price_yearly=9970.0,  # 2 months free
                stripe_price_id_monthly="price_professional_monthly",
                stripe_price_id_yearly="price_professional_yearly",
                features=PlanFeatures(
                    max_campaigns=50,
                    max_monthly_spend=100000.0,
                    max_customers=25000,
                    ai_decisions_per_month=25000,
                    email_sends_per_month=100000,
                    advanced_analytics=True,
                    priority_support=True,
                    custom_integrations=True
                ),
                is_popular=True
            ),
            SubscriptionPlan.ENTERPRISE: SubscriptionPlanConfig(
                plan_id="enterprise",
                name="Enterprise",
                description="Unlimited features for large organizations",
                price_monthly=2497.0,
                price_yearly=24970.0,  # 2 months free
                stripe_price_id_monthly="price_enterprise_monthly",
                stripe_price_id_yearly="price_enterprise_yearly",
                features=PlanFeatures(
                    max_campaigns=999999,
                    max_monthly_spend=999999999.0,
                    max_customers=999999,
                    ai_decisions_per_month=999999,
                    email_sends_per_month=999999,
                    advanced_analytics=True,
                    priority_support=True,
                    custom_integrations=True
                )
            )
        }
    
    async def get_subscription_info(
        self, 
        client_id: str, 
        db: AsyncSession
    ) -> SubscriptionInfo:
        """Get complete subscription information for client"""
        
        # Get client
        client_result = await db.execute(
            select(Client).where(Client.id == client_id)
        )
        client = client_result.scalar_one_or_none()
        
        if not client:
            raise ValueError(f"Client {client_id} not found")
        
        # Get current usage
        usage = await self._calculate_usage_metrics(client_id, db)
        
        # Determine current plan and status
        current_plan = SubscriptionPlan(client.subscription_plan or "trial")
        current_status = SubscriptionStatus(client.subscription_status or "trial")
        
        # Get plan configuration
        plan_config = self.plan_configs[current_plan]
        
        # Check usage warnings
        usage_warnings = self._check_usage_limits(usage, plan_config.features)
        
        # Calculate subscription dates
        current_period_start = client.subscription_current_period_start or client.created_at
        current_period_end = client.subscription_current_period_end or (
            client.created_at + timedelta(days=14)  # Default trial period
        )
        
        return SubscriptionInfo(
            client_id=client_id,
            plan=current_plan,
            status=current_status,
            current_period_start=current_period_start,
            current_period_end=current_period_end,
            trial_end=client.trial_end,
            stripe_subscription_id=client.stripe_subscription_id,
            stripe_customer_id=client.stripe_customer_id,
            usage=usage,
            plan_config=plan_config,
            usage_warnings=usage_warnings
        )
    
    async def _calculate_usage_metrics(
        self, 
        client_id: str, 
        db: AsyncSession
    ) -> UsageMetrics:
        """Calculate current usage metrics for client"""
        
        # Get current month start
        now = datetime.utcnow()
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Count campaigns
        from apps.core.database import Campaign
        campaigns_result = await db.execute(
            select(Campaign).where(Campaign.client_id == client_id)
        )
        campaigns_count = len(campaigns_result.scalars().all())
        
        # Calculate monthly spend
        from apps.core.database import CampaignMetric
        spend_result = await db.execute(
            select(CampaignMetric.spend).join(Campaign).where(
                Campaign.client_id == client_id,
                CampaignMetric.date >= month_start.date()
            )
        )
        monthly_spend = sum(row[0] for row in spend_result.fetchall())
        
        # Count customers
        from apps.core.database import CustomerProfile
        customers_result = await db.execute(
            select(CustomerProfile).where(CustomerProfile.client_id == client_id)
        )
        customers_count = len(customers_result.scalars().all())
        
        # Count AI decisions this month
        from apps.core.database import AIDecision
        ai_decisions_result = await db.execute(
            select(AIDecision).where(
                AIDecision.client_id == client_id,
                AIDecision.created_at >= month_start
            )
        )
        ai_decisions_this_month = len(ai_decisions_result.scalars().all())
        
        # Count email sends this month (simplified)
        email_sends_this_month = 0  # Would track from email service
        
        # Calculate storage used (simplified)
        storage_used_gb = 0.1  # Would calculate actual storage
        
        return UsageMetrics(
            campaigns_count=campaigns_count,
            monthly_spend=monthly_spend,
            customers_count=customers_count,
            ai_decisions_this_month=ai_decisions_this_month,
            email_sends_this_month=email_sends_this_month,
            storage_used_gb=storage_used_gb
        )
    
    def _check_usage_limits(
        self, 
        usage: UsageMetrics, 
        features: PlanFeatures
    ) -> List[str]:
        """Check usage against plan limits and return warnings"""
        warnings = []
        
        # Check campaign limit
        if usage.campaigns_count >= features.max_campaigns * 0.9:
            warnings.append(f"Approaching campaign limit ({usage.campaigns_count}/{features.max_campaigns})")
        
        # Check monthly spend limit
        if usage.monthly_spend >= features.max_monthly_spend * 0.9:
            warnings.append(f"Approaching monthly spend limit (${usage.monthly_spend:,.0f}/${features.max_monthly_spend:,.0f})")
        
        # Check customer limit
        if usage.customers_count >= features.max_customers * 0.9:
            warnings.append(f"Approaching customer limit ({usage.customers_count}/{features.max_customers})")
        
        # Check AI decisions limit
        if usage.ai_decisions_this_month >= features.ai_decisions_per_month * 0.9:
            warnings.append(f"Approaching AI decisions limit ({usage.ai_decisions_this_month}/{features.ai_decisions_per_month})")
        
        return warnings
    
    async def create_stripe_customer(
        self, 
        client_id: str, 
        email: str, 
        name: str,
        db: AsyncSession
    ) -> str:
        """Create Stripe customer"""
        
        try:
            customer = stripe.Customer.create(
                email=email,
                name=name,
                metadata={
                    "client_id": client_id,
                    "platform": "omnify"
                }
            )
            
            # Update client with Stripe customer ID
            client_result = await db.execute(
                select(Client).where(Client.id == client_id)
            )
            client = client_result.scalar_one_or_none()
            
            if client:
                client.stripe_customer_id = customer.id
                await db.commit()
            
            logger.info(
                "Stripe customer created",
                client_id=client_id,
                stripe_customer_id=customer.id
            )
            
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error("Failed to create Stripe customer", error=str(e))
            raise
    
    async def create_subscription(
        self,
        client_id: str,
        plan: SubscriptionPlan,
        billing_cycle: str = "monthly",  # "monthly" or "yearly"
        db: AsyncSession = None
    ) -> str:
        """Create Stripe subscription"""
        
        try:
            # Get client
            client_result = await db.execute(
                select(Client).where(Client.id == client_id)
            )
            client = client_result.scalar_one_or_none()
            
            if not client or not client.stripe_customer_id:
                raise ValueError("Client must have Stripe customer ID")
            
            # Get plan config
            plan_config = self.plan_configs[plan]
            
            # Select price ID based on billing cycle
            price_id = (
                plan_config.stripe_price_id_yearly 
                if billing_cycle == "yearly" 
                else plan_config.stripe_price_id_monthly
            )
            
            # Create subscription
            subscription = stripe.Subscription.create(
                customer=client.stripe_customer_id,
                items=[{"price": price_id}],
                metadata={
                    "client_id": client_id,
                    "plan": plan.value
                },
                trial_period_days=14 if plan != SubscriptionPlan.TRIAL else None
            )
            
            # Update client subscription info
            client.subscription_plan = plan.value
            client.subscription_status = "active"
            client.stripe_subscription_id = subscription.id
            client.subscription_current_period_start = datetime.fromtimestamp(
                subscription.current_period_start
            )
            client.subscription_current_period_end = datetime.fromtimestamp(
                subscription.current_period_end
            )
            
            await db.commit()
            
            logger.info(
                "Subscription created",
                client_id=client_id,
                plan=plan.value,
                subscription_id=subscription.id
            )
            
            return subscription.id
            
        except stripe.error.StripeError as e:
            logger.error("Failed to create subscription", error=str(e))
            raise
    
    async def cancel_subscription(
        self,
        client_id: str,
        immediate: bool = False,
        db: AsyncSession = None
    ) -> bool:
        """Cancel subscription"""
        
        try:
            # Get client
            client_result = await db.execute(
                select(Client).where(Client.id == client_id)
            )
            client = client_result.scalar_one_or_none()
            
            if not client or not client.stripe_subscription_id:
                raise ValueError("Client must have active subscription")
            
            # Cancel subscription
            if immediate:
                stripe.Subscription.delete(client.stripe_subscription_id)
                client.subscription_status = "canceled"
            else:
                stripe.Subscription.modify(
                    client.stripe_subscription_id,
                    cancel_at_period_end=True
                )
                client.subscription_status = "active"  # Still active until period end
            
            await db.commit()
            
            logger.info(
                "Subscription canceled",
                client_id=client_id,
                immediate=immediate
            )
            
            return True
            
        except stripe.error.StripeError as e:
            logger.error("Failed to cancel subscription", error=str(e))
            raise
    
    def get_all_plans(self) -> List[SubscriptionPlanConfig]:
        """Get all available subscription plans"""
        return list(self.plan_configs.values())
    
    async def handle_stripe_webhook(self, event: Dict[str, Any], db: AsyncSession):
        """Handle Stripe webhook events"""
        
        event_type = event["type"]
        
        if event_type == "customer.subscription.updated":
            await self._handle_subscription_updated(event["data"]["object"], db)
        elif event_type == "customer.subscription.deleted":
            await self._handle_subscription_deleted(event["data"]["object"], db)
        elif event_type == "invoice.payment_failed":
            await self._handle_payment_failed(event["data"]["object"], db)
        elif event_type == "invoice.payment_succeeded":
            await self._handle_payment_succeeded(event["data"]["object"], db)
    
    async def _handle_subscription_updated(self, subscription: Dict, db: AsyncSession):
        """Handle subscription update webhook"""
        client_id = subscription["metadata"].get("client_id")
        if not client_id:
            return
        
        # Update client subscription status
        client_result = await db.execute(
            select(Client).where(Client.id == client_id)
        )
        client = client_result.scalar_one_or_none()
        
        if client:
            client.subscription_status = subscription["status"]
            client.subscription_current_period_start = datetime.fromtimestamp(
                subscription["current_period_start"]
            )
            client.subscription_current_period_end = datetime.fromtimestamp(
                subscription["current_period_end"]
            )
            await db.commit()
    
    async def _handle_subscription_deleted(self, subscription: Dict, db: AsyncSession):
        """Handle subscription deletion webhook"""
        client_id = subscription["metadata"].get("client_id")
        if not client_id:
            return
        
        # Update client to canceled status
        client_result = await db.execute(
            select(Client).where(Client.id == client_id)
        )
        client = client_result.scalar_one_or_none()
        
        if client:
            client.subscription_status = "canceled"
            await db.commit()
    
    async def _handle_payment_failed(self, invoice: Dict, db: AsyncSession):
        """Handle failed payment webhook"""
        # Would implement payment failure handling
        logger.warning("Payment failed", invoice_id=invoice["id"])
    
    async def _handle_payment_succeeded(self, invoice: Dict, db: AsyncSession):
        """Handle successful payment webhook"""
        # Would implement payment success handling
        logger.info("Payment succeeded", invoice_id=invoice["id"])

# Global billing manager instance
billing_manager = BillingManager()
