#!/bin/bash
set -e

# Azure-specific entrypoint script for Omnify Marketing Cloud
echo "🚀 Starting Omnify Marketing Cloud - Azure Variant"

# Set Azure-specific environment variables
export CLOUD_PROVIDER="azure"
export AI_ENGINE_PRIMARY="azure_openai"
export AI_ENGINE_FALLBACK="azure_cognitive"
export DATABASE_TYPE="azure_sql"
export CACHE_TYPE="azure_redis"

# Azure region detection
if [ -z "$AZURE_REGION" ]; then
    export AZURE_REGION=$(curl -s -H Metadata:true "http://***************/metadata/instance/compute/location?api-version=2021-02-01&format=text" 2>/dev/null || echo "eastus")
fi

echo "📍 Azure Region: $AZURE_REGION"

# Wait for Azure SQL Database to be ready
echo "⏳ Waiting for Azure SQL Database to be ready..."
python -c "
import time
import pyodbc
import os
from urllib.parse import urlparse

db_url = os.environ.get('DATABASE_URL')
if db_url:
    # Parse Azure SQL connection string
    if 'mssql://' in db_url:
        parsed = urlparse(db_url)
        server = parsed.hostname
        database = parsed.path[1:] if parsed.path else 'omnify'
        username = parsed.username
        password = parsed.password
        
        connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;'
        
        max_retries = 30
        for i in range(max_retries):
            try:
                conn = pyodbc.connect(connection_string)
                conn.close()
                print('✅ Azure SQL Database connection successful')
                break
            except Exception as e:
                if i == max_retries - 1:
                    print(f'❌ Database connection failed after {max_retries} retries: {e}')
                    exit(1)
                print(f'⏳ Database not ready, retrying in 5 seconds... ({i+1}/{max_retries})')
                time.sleep(5)
"

# Wait for Azure Cache for Redis to be ready
echo "⏳ Waiting for Azure Cache for Redis to be ready..."
python -c "
import time
import redis
import os

redis_url = os.environ.get('REDIS_URL')
if redis_url:
    max_retries = 30
    for i in range(max_retries):
        try:
            r = redis.from_url(redis_url, ssl_cert_reqs=None)
            r.ping()
            print('✅ Azure Redis connection successful')
            break
        except Exception as e:
            if i == max_retries - 1:
                print(f'❌ Redis connection failed after {max_retries} retries: {e}')
                exit(1)
            print(f'⏳ Redis not ready, retrying in 5 seconds... ({i+1}/{max_retries})')
            time.sleep(5)
"

# Run database migrations
echo "🔄 Running database migrations..."
alembic upgrade head

# Test Azure OpenAI connectivity
echo "🧠 Testing Azure OpenAI connectivity..."
python -c "
import openai
import os
try:
    openai.api_type = 'azure'
    openai.api_base = os.environ.get('AZURE_OPENAI_ENDPOINT')
    openai.api_version = '2024-02-01'
    openai.api_key = os.environ.get('AZURE_OPENAI_KEY')
    
    # Test with a simple completion
    response = openai.ChatCompletion.create(
        engine='gpt-4',
        messages=[{'role': 'user', 'content': 'Hello'}],
        max_tokens=10
    )
    print('✅ Azure OpenAI connection successful')
except Exception as e:
    print(f'⚠️ Azure OpenAI connection failed: {e}')
    print('🔄 Will use fallback AI engine')
"

# Initialize Azure-specific AI models
echo "🤖 Initializing Azure AI models..."
python -c "
import os
from lib.ai.azure_openai_engine import AzureOpenAIEngine

try:
    engine = AzureOpenAIEngine(
        endpoint=os.environ.get('AZURE_OPENAI_ENDPOINT'),
        api_key=os.environ.get('AZURE_OPENAI_KEY')
    )
    print('✅ Azure OpenAI engine initialized')
except Exception as e:
    print(f'⚠️ Azure OpenAI engine initialization failed: {e}')
"

# Start background workers
echo "🔧 Starting background workers..."
celery -A apps.core.celery worker --loglevel=info --detach

# Start the application
echo "🎯 Starting Omnify Marketing Cloud API server..."
echo "🌐 Cloud Variant: Azure OpenAI Accelerator"
echo "🤖 AI Engine: Azure OpenAI Service"
echo "💾 Database: Azure SQL Database"
echo "⚡ Cache: Azure Cache for Redis"
echo "🏗️ Compute: Azure Container Instances"

exec "$@"
