#!/usr/bin/env python3
"""
Omnify Marketing Cloud - Production Deployment Script
"""
import asyncio
import sys
import os
import subprocess
import json
from pathlib import Path
from datetime import datetime
import structlog

logger = structlog.get_logger()

class DeploymentManager:
    """Manages production deployment process"""
    
    def __init__(self, environment="production"):
        self.environment = environment
        self.project_root = Path(__file__).parent.parent
        self.deployment_id = f"deploy_{int(datetime.utcnow().timestamp())}"
        
    async def deploy(self):
        """Execute full deployment process"""
        try:
            print(f"🚀 Starting Omnify Marketing Cloud deployment to {self.environment}")
            print(f"📋 Deployment ID: {self.deployment_id}")
            print("="*60)
            
            # Pre-deployment checks
            await self._pre_deployment_checks()
            
            # Database migrations
            await self._run_database_migrations()
            
            # Build and deploy application
            await self._build_and_deploy()
            
            # Update infrastructure
            await self._update_infrastructure()
            
            # Post-deployment verification
            await self._post_deployment_verification()
            
            # Cleanup
            await self._cleanup()
            
            print("✅ Deployment completed successfully!")
            return True
            
        except Exception as e:
            logger.error("Deployment failed", error=str(e))
            await self._rollback()
            return False
    
    async def _pre_deployment_checks(self):
        """Run pre-deployment checks"""
        print("🔍 Running pre-deployment checks...")
        
        # Check environment variables
        required_vars = [
            "DATABASE_URL", "OPENAI_API_KEY", "SECRET_KEY",
            "GOOGLE_ADS_DEVELOPER_TOKEN", "META_APP_ID"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise Exception(f"Missing required environment variables: {missing_vars}")
        
        # Check database connectivity
        await self._check_database_connection()
        
        # Run tests
        await self._run_tests()
        
        # Check API credentials
        await self._validate_api_credentials()
        
        print("✅ Pre-deployment checks passed")
    
    async def _check_database_connection(self):
        """Check database connectivity"""
        print("  📊 Checking database connection...")
        
        try:
            # Import here to avoid circular imports
            from apps.core.database import engine
            
            async with engine.connect() as conn:
                result = await conn.execute("SELECT 1")
                assert result.scalar() == 1
            
            print("  ✅ Database connection successful")
            
        except Exception as e:
            raise Exception(f"Database connection failed: {e}")
    
    async def _run_tests(self):
        """Run test suite"""
        print("  🧪 Running test suite...")
        
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/", "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=self.project_root)
        
        if result.returncode != 0:
            print(f"❌ Tests failed:\n{result.stdout}\n{result.stderr}")
            raise Exception("Test suite failed")
        
        print("  ✅ All tests passed")
    
    async def _validate_api_credentials(self):
        """Validate external API credentials"""
        print("  🔑 Validating API credentials...")
        
        # Test OpenAI
        try:
            import openai
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            await client.models.list()
            print("    ✅ OpenAI API credentials valid")
        except Exception as e:
            print(f"    ⚠️  OpenAI API validation failed: {e}")
        
        # Test Google Ads (if configured)
        if os.getenv("GOOGLE_ADS_DEVELOPER_TOKEN"):
            try:
                from lib.connectors.google_ads import GoogleAdsConnector
                # Basic validation without customer ID
                print("    ✅ Google Ads credentials configured")
            except Exception as e:
                print(f"    ⚠️  Google Ads validation failed: {e}")
        
        print("  ✅ API credential validation completed")
    
    async def _run_database_migrations(self):
        """Run database migrations"""
        print("📊 Running database migrations...")
        
        result = subprocess.run([
            "alembic", "upgrade", "head"
        ], capture_output=True, text=True, cwd=self.project_root)
        
        if result.returncode != 0:
            raise Exception(f"Database migration failed: {result.stderr}")
        
        print("✅ Database migrations completed")
    
    async def _build_and_deploy(self):
        """Build and deploy application"""
        print("🏗️  Building and deploying application...")
        
        if self.environment == "production":
            # Build Docker image
            await self._build_docker_image()
            
            # Deploy to container orchestration
            await self._deploy_containers()
        
        elif self.environment == "staging":
            # Deploy to staging environment
            await self._deploy_staging()
        
        print("✅ Application deployment completed")
    
    async def _build_docker_image(self):
        """Build Docker image for production"""
        print("  🐳 Building Docker image...")
        
        image_tag = f"omnify-marketing-cloud:{self.deployment_id}"
        
        result = subprocess.run([
            "docker", "build", 
            "-t", image_tag,
            "-t", "omnify-marketing-cloud:latest",
            "."
        ], capture_output=True, text=True, cwd=self.project_root)
        
        if result.returncode != 0:
            raise Exception(f"Docker build failed: {result.stderr}")
        
        print(f"  ✅ Docker image built: {image_tag}")
    
    async def _deploy_containers(self):
        """Deploy containers to production"""
        print("  🚢 Deploying containers...")
        
        # Update docker-compose for production
        result = subprocess.run([
            "docker-compose", "-f", "docker-compose.prod.yml",
            "up", "-d", "--remove-orphans"
        ], capture_output=True, text=True, cwd=self.project_root)
        
        if result.returncode != 0:
            raise Exception(f"Container deployment failed: {result.stderr}")
        
        print("  ✅ Containers deployed successfully")
    
    async def _deploy_staging(self):
        """Deploy to staging environment"""
        print("  🎭 Deploying to staging...")
        
        # Staging deployment logic
        result = subprocess.run([
            "docker-compose", "up", "-d", "--build"
        ], capture_output=True, text=True, cwd=self.project_root)
        
        if result.returncode != 0:
            raise Exception(f"Staging deployment failed: {result.stderr}")
        
        print("  ✅ Staging deployment completed")
    
    async def _update_infrastructure(self):
        """Update infrastructure components"""
        print("🏗️  Updating infrastructure...")
        
        # Update n8n workflows
        await self._update_n8n_workflows()
        
        # Update monitoring configuration
        await self._update_monitoring()
        
        print("✅ Infrastructure updates completed")
    
    async def _update_n8n_workflows(self):
        """Update n8n workflows"""
        print("  🔄 Updating n8n workflows...")
        
        workflows_dir = self.project_root / "workflows"
        
        for workflow_file in workflows_dir.glob("*.json"):
            print(f"    📋 Updating workflow: {workflow_file.name}")
            # In production, this would use n8n API to update workflows
        
        print("  ✅ n8n workflows updated")
    
    async def _update_monitoring(self):
        """Update monitoring configuration"""
        print("  📊 Updating monitoring configuration...")
        
        # Update Prometheus configuration
        # Update Grafana dashboards
        # Configure alerts
        
        print("  ✅ Monitoring configuration updated")
    
    async def _post_deployment_verification(self):
        """Verify deployment success"""
        print("🔍 Running post-deployment verification...")
        
        # Health check
        await self._health_check()
        
        # API endpoint tests
        await self._test_api_endpoints()
        
        # AI agent functionality tests
        await self._test_ai_agents()
        
        print("✅ Post-deployment verification completed")
    
    async def _health_check(self):
        """Perform health check"""
        print("  ❤️  Performing health check...")
        
        import httpx
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8000/health", timeout=10)
                
                if response.status_code != 200:
                    raise Exception(f"Health check failed: {response.status_code}")
                
                health_data = response.json()
                if health_data.get("status") != "healthy":
                    raise Exception(f"Application not healthy: {health_data}")
            
            print("  ✅ Health check passed")
            
        except Exception as e:
            raise Exception(f"Health check failed: {e}")
    
    async def _test_api_endpoints(self):
        """Test critical API endpoints"""
        print("  🔌 Testing API endpoints...")
        
        import httpx
        
        endpoints = [
            "/",
            "/health",
            "/api/v1/analytics/roas?client_id=1&days=7",
            "/api/v1/agents/status/roi_engine?client_id=1"
        ]
        
        async with httpx.AsyncClient() as client:
            for endpoint in endpoints:
                try:
                    response = await client.get(f"http://localhost:8000{endpoint}", timeout=10)
                    if response.status_code not in [200, 401]:  # 401 is OK for protected endpoints
                        print(f"    ⚠️  Endpoint {endpoint} returned {response.status_code}")
                    else:
                        print(f"    ✅ Endpoint {endpoint} OK")
                except Exception as e:
                    print(f"    ❌ Endpoint {endpoint} failed: {e}")
        
        print("  ✅ API endpoint tests completed")
    
    async def _test_ai_agents(self):
        """Test AI agent functionality"""
        print("  🤖 Testing AI agents...")
        
        try:
            # Test ROI Engine X™
            from lib.ai.decision_engine import ROIEngineX
            roi_engine = ROIEngineX()
            print("    ✅ ROI Engine X™ initialized")
            
            # Test Retention Reactor Pro™
            from lib.ai.retention_reactor import RetentionReactorPro
            retention_reactor = RetentionReactorPro()
            await retention_reactor.initialize_model()
            print("    ✅ Retention Reactor Pro™ initialized")
            
            # Test EngageSense Ultra™
            from lib.ai.engage_sense import EngageSenseUltra
            engage_sense = EngageSenseUltra()
            await engage_sense.initialize_models()
            print("    ✅ EngageSense Ultra™ initialized")
            
        except Exception as e:
            print(f"    ⚠️  AI agent test failed: {e}")
        
        print("  ✅ AI agent tests completed")
    
    async def _cleanup(self):
        """Cleanup deployment artifacts"""
        print("🧹 Cleaning up deployment artifacts...")
        
        # Remove old Docker images
        subprocess.run([
            "docker", "image", "prune", "-f"
        ], capture_output=True)
        
        print("✅ Cleanup completed")
    
    async def _rollback(self):
        """Rollback deployment on failure"""
        print("🔄 Rolling back deployment...")
        
        try:
            # Rollback database migrations
            subprocess.run([
                "alembic", "downgrade", "-1"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            # Rollback container deployment
            subprocess.run([
                "docker-compose", "down"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            print("✅ Rollback completed")
            
        except Exception as e:
            print(f"❌ Rollback failed: {e}")

async def main():
    """Main deployment function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deploy Omnify Marketing Cloud")
    parser.add_argument("--environment", default="production", 
                       choices=["production", "staging", "development"],
                       help="Deployment environment")
    parser.add_argument("--skip-tests", action="store_true",
                       help="Skip test execution")
    
    args = parser.parse_args()
    
    deployment = DeploymentManager(args.environment)
    
    if args.skip_tests:
        deployment._run_tests = lambda: asyncio.sleep(0)  # Skip tests
    
    success = await deployment.deploy()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
