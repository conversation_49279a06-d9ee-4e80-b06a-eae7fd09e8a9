"""
Advanced Caching Layer for Omnify Marketing Cloud
Supports Redis, in-memory, and multi-level caching
"""
import asyncio
import json
import pickle
from typing import Any, Optional, Dict, List, Callable, Union
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
import structlog
from pydantic import BaseModel
import hashlib

logger = structlog.get_logger()

class CacheEntry(BaseModel):
    """Cache entry model"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime] = None
    access_count: int = 0
    last_accessed: datetime
    tags: List[str] = []
    size_bytes: int = 0

class CacheStats(BaseModel):
    """Cache statistics"""
    total_keys: int
    hit_count: int
    miss_count: int
    hit_rate: float
    total_size_bytes: int
    avg_access_time_ms: float
    eviction_count: int

class CacheInterface(ABC):
    """Abstract cache interface"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        pass
    
    @abstractmethod
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        tags: List[str] = None
    ) -> bool:
        """Set value in cache"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache entries"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        pass

class MemoryCache(CacheInterface):
    """In-memory cache implementation"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.hit_count = 0
        self.miss_count = 0
        self.eviction_count = 0
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from memory cache"""
        async with self._lock:
            entry = self.cache.get(key)
            
            if not entry:
                self.miss_count += 1
                return None
            
            # Check expiration
            if entry.expires_at and datetime.utcnow() > entry.expires_at:
                del self.cache[key]
                self.miss_count += 1
                return None
            
            # Update access stats
            entry.access_count += 1
            entry.last_accessed = datetime.utcnow()
            self.hit_count += 1
            
            return entry.value
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        tags: List[str] = None
    ) -> bool:
        """Set value in memory cache"""
        async with self._lock:
            # Calculate expiration
            expires_at = None
            if ttl or self.default_ttl:
                expires_at = datetime.utcnow() + timedelta(seconds=ttl or self.default_ttl)
            
            # Calculate size
            try:
                size_bytes = len(pickle.dumps(value))
            except:
                size_bytes = len(str(value))
            
            # Create entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                expires_at=expires_at,
                last_accessed=datetime.utcnow(),
                tags=tags or [],
                size_bytes=size_bytes
            )
            
            # Evict if necessary
            if len(self.cache) >= self.max_size and key not in self.cache:
                await self._evict_lru()
            
            self.cache[key] = entry
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete key from memory cache"""
        async with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in memory cache"""
        async with self._lock:
            entry = self.cache.get(key)
            if not entry:
                return False
            
            # Check expiration
            if entry.expires_at and datetime.utcnow() > entry.expires_at:
                del self.cache[key]
                return False
            
            return True
    
    async def clear(self) -> bool:
        """Clear all entries from memory cache"""
        async with self._lock:
            self.cache.clear()
            return True
    
    async def get_stats(self) -> CacheStats:
        """Get memory cache statistics"""
        async with self._lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
            
            total_size = sum(entry.size_bytes for entry in self.cache.values())
            
            return CacheStats(
                total_keys=len(self.cache),
                hit_count=self.hit_count,
                miss_count=self.miss_count,
                hit_rate=hit_rate,
                total_size_bytes=total_size,
                avg_access_time_ms=0.1,  # Memory access is very fast
                eviction_count=self.eviction_count
            )
    
    async def _evict_lru(self):
        """Evict least recently used entry"""
        if not self.cache:
            return
        
        # Find LRU entry
        lru_key = min(self.cache.keys(), key=lambda k: self.cache[k].last_accessed)
        del self.cache[lru_key]
        self.eviction_count += 1

class RedisCache(CacheInterface):
    """Redis cache implementation"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0", default_ttl: int = 3600):
        self.redis_url = redis_url
        self.default_ttl = default_ttl
        self.redis = None
        self.hit_count = 0
        self.miss_count = 0
        self._stats_key = "omnify:cache:stats"
    
    async def _get_redis(self):
        """Get Redis connection"""
        if not self.redis:
            import aioredis
            self.redis = await aioredis.from_url(self.redis_url)
        return self.redis
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        try:
            redis = await self._get_redis()
            
            # Get value and metadata
            pipe = redis.pipeline()
            pipe.get(f"data:{key}")
            pipe.hgetall(f"meta:{key}")
            results = await pipe.execute()
            
            data, meta = results
            
            if not data:
                self.miss_count += 1
                await self._update_stats("miss")
                return None
            
            # Update access stats
            await redis.hincrby(f"meta:{key}", "access_count", 1)
            await redis.hset(f"meta:{key}", "last_accessed", datetime.utcnow().isoformat())
            
            self.hit_count += 1
            await self._update_stats("hit")
            
            # Deserialize value
            return pickle.loads(data)
            
        except Exception as e:
            logger.error("Redis cache get error", error=str(e), key=key)
            self.miss_count += 1
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        tags: List[str] = None
    ) -> bool:
        """Set value in Redis cache"""
        try:
            redis = await self._get_redis()
            
            # Serialize value
            serialized_value = pickle.dumps(value)
            
            # Set TTL
            expire_seconds = ttl or self.default_ttl
            
            # Store data and metadata
            pipe = redis.pipeline()
            pipe.setex(f"data:{key}", expire_seconds, serialized_value)
            
            # Store metadata
            metadata = {
                "created_at": datetime.utcnow().isoformat(),
                "size_bytes": len(serialized_value),
                "access_count": 0,
                "last_accessed": datetime.utcnow().isoformat(),
                "tags": json.dumps(tags or [])
            }
            
            pipe.hset(f"meta:{key}", mapping=metadata)
            pipe.expire(f"meta:{key}", expire_seconds)
            
            await pipe.execute()
            
            await self._update_stats("set")
            return True
            
        except Exception as e:
            logger.error("Redis cache set error", error=str(e), key=key)
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from Redis cache"""
        try:
            redis = await self._get_redis()
            
            pipe = redis.pipeline()
            pipe.delete(f"data:{key}")
            pipe.delete(f"meta:{key}")
            results = await pipe.execute()
            
            return any(results)
            
        except Exception as e:
            logger.error("Redis cache delete error", error=str(e), key=key)
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis cache"""
        try:
            redis = await self._get_redis()
            return await redis.exists(f"data:{key}")
        except Exception as e:
            logger.error("Redis cache exists error", error=str(e), key=key)
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries from Redis"""
        try:
            redis = await self._get_redis()
            
            # Find all cache keys
            data_keys = await redis.keys("data:*")
            meta_keys = await redis.keys("meta:*")
            
            if data_keys or meta_keys:
                await redis.delete(*(data_keys + meta_keys))
            
            return True
            
        except Exception as e:
            logger.error("Redis cache clear error", error=str(e))
            return False
    
    async def get_stats(self) -> CacheStats:
        """Get Redis cache statistics"""
        try:
            redis = await self._get_redis()
            
            # Get basic stats
            stats = await redis.hgetall(self._stats_key)
            
            hit_count = int(stats.get("hit_count", 0))
            miss_count = int(stats.get("miss_count", 0))
            total_requests = hit_count + miss_count
            hit_rate = (hit_count / total_requests * 100) if total_requests > 0 else 0
            
            # Count keys and calculate total size
            data_keys = await redis.keys("data:*")
            total_size = 0
            
            if data_keys:
                # Sample a few keys to estimate average size
                sample_keys = data_keys[:min(10, len(data_keys))]
                sample_sizes = []
                
                for key in sample_keys:
                    meta = await redis.hgetall(key.replace("data:", "meta:"))
                    if meta and "size_bytes" in meta:
                        sample_sizes.append(int(meta["size_bytes"]))
                
                if sample_sizes:
                    avg_size = sum(sample_sizes) / len(sample_sizes)
                    total_size = int(avg_size * len(data_keys))
            
            return CacheStats(
                total_keys=len(data_keys),
                hit_count=hit_count,
                miss_count=miss_count,
                hit_rate=hit_rate,
                total_size_bytes=total_size,
                avg_access_time_ms=1.0,  # Redis network latency
                eviction_count=0  # Redis handles eviction internally
            )
            
        except Exception as e:
            logger.error("Redis cache stats error", error=str(e))
            return CacheStats(
                total_keys=0,
                hit_count=0,
                miss_count=0,
                hit_rate=0,
                total_size_bytes=0,
                avg_access_time_ms=0,
                eviction_count=0
            )
    
    async def _update_stats(self, operation: str):
        """Update cache statistics"""
        try:
            redis = await self._get_redis()
            await redis.hincrby(self._stats_key, f"{operation}_count", 1)
        except:
            pass  # Don't fail cache operations due to stats

class MultiLevelCache(CacheInterface):
    """Multi-level cache with L1 (memory) and L2 (Redis)"""
    
    def __init__(
        self, 
        l1_cache: CacheInterface,
        l2_cache: CacheInterface,
        l1_ttl: int = 300,  # 5 minutes
        l2_ttl: int = 3600  # 1 hour
    ):
        self.l1_cache = l1_cache
        self.l2_cache = l2_cache
        self.l1_ttl = l1_ttl
        self.l2_ttl = l2_ttl
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from multi-level cache"""
        # Try L1 first
        value = await self.l1_cache.get(key)
        if value is not None:
            return value
        
        # Try L2
        value = await self.l2_cache.get(key)
        if value is not None:
            # Promote to L1
            await self.l1_cache.set(key, value, self.l1_ttl)
            return value
        
        return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        tags: List[str] = None
    ) -> bool:
        """Set value in multi-level cache"""
        # Set in both levels
        l1_success = await self.l1_cache.set(key, value, ttl or self.l1_ttl, tags)
        l2_success = await self.l2_cache.set(key, value, ttl or self.l2_ttl, tags)
        
        return l1_success or l2_success
    
    async def delete(self, key: str) -> bool:
        """Delete key from multi-level cache"""
        l1_success = await self.l1_cache.delete(key)
        l2_success = await self.l2_cache.delete(key)
        
        return l1_success or l2_success
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in multi-level cache"""
        return await self.l1_cache.exists(key) or await self.l2_cache.exists(key)
    
    async def clear(self) -> bool:
        """Clear all cache levels"""
        l1_success = await self.l1_cache.clear()
        l2_success = await self.l2_cache.clear()
        
        return l1_success and l2_success
    
    async def get_stats(self) -> CacheStats:
        """Get combined cache statistics"""
        l1_stats = await self.l1_cache.get_stats()
        l2_stats = await self.l2_cache.get_stats()
        
        return CacheStats(
            total_keys=l1_stats.total_keys + l2_stats.total_keys,
            hit_count=l1_stats.hit_count + l2_stats.hit_count,
            miss_count=l1_stats.miss_count + l2_stats.miss_count,
            hit_rate=(l1_stats.hit_count + l2_stats.hit_count) / 
                     (l1_stats.hit_count + l1_stats.miss_count + l2_stats.hit_count + l2_stats.miss_count) * 100
                     if (l1_stats.hit_count + l1_stats.miss_count + l2_stats.hit_count + l2_stats.miss_count) > 0 else 0,
            total_size_bytes=l1_stats.total_size_bytes + l2_stats.total_size_bytes,
            avg_access_time_ms=(l1_stats.avg_access_time_ms + l2_stats.avg_access_time_ms) / 2,
            eviction_count=l1_stats.eviction_count + l2_stats.eviction_count
        )

class CacheManager:
    """Main cache manager with decorators and utilities"""
    
    def __init__(self, cache: CacheInterface):
        self.cache = cache
    
    def cached(
        self, 
        ttl: int = 3600, 
        key_prefix: str = "",
        tags: List[str] = None
    ):
        """Decorator for caching function results"""
        def decorator(func: Callable):
            async def wrapper(*args, **kwargs):
                # Generate cache key
                key_parts = [key_prefix, func.__name__]
                
                # Add args and kwargs to key
                if args:
                    key_parts.extend(str(arg) for arg in args)
                if kwargs:
                    key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                
                cache_key = hashlib.md5(":".join(key_parts).encode()).hexdigest()
                
                # Try to get from cache
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = await func(*args, **kwargs)
                await self.cache.set(cache_key, result, ttl, tags)
                
                return result
            
            return wrapper
        return decorator
    
    async def get_or_set(
        self, 
        key: str, 
        factory: Callable, 
        ttl: int = 3600,
        tags: List[str] = None
    ) -> Any:
        """Get value from cache or set it using factory function"""
        value = await self.cache.get(key)
        if value is not None:
            return value
        
        # Generate value using factory
        if asyncio.iscoroutinefunction(factory):
            value = await factory()
        else:
            value = factory()
        
        await self.cache.set(key, value, ttl, tags)
        return value
    
    async def invalidate_by_tags(self, tags: List[str]):
        """Invalidate cache entries by tags (simplified implementation)"""
        # This would require a more sophisticated implementation
        # For now, we'll just log the intent
        logger.info("Cache invalidation by tags requested", tags=tags)

# Initialize cache instances
memory_cache = MemoryCache(max_size=1000, default_ttl=300)

try:
    import os
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    redis_cache = RedisCache(redis_url=redis_url, default_ttl=3600)
    
    # Use multi-level cache in production
    cache = MultiLevelCache(memory_cache, redis_cache)
except ImportError:
    # Fall back to memory cache if Redis is not available
    cache = memory_cache

# Global cache manager
cache_manager = CacheManager(cache)
