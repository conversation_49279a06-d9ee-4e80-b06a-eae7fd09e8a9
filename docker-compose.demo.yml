# Docker Compose for Omnify Marketing Cloud Demo
# Provides PostgreSQL, Redis, and optional services for local development

version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: omnify-postgres-demo
    environment:
      POSTGRES_DB: omnify_db
      POSTGRES_USER: omnify_user
      POSTGRES_PASSWORD: omnify_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_demo_db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omnify_user -d omnify_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omnify-network

  redis:
    image: redis:7-alpine
    container_name: omnify-redis-demo
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omnify-network

  # Optional: n8n for workflow automation demo
  n8n:
    image: n8nio/n8n:latest
    container_name: omnify-n8n-demo
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=demo
      - N8N_BASIC_AUTH_PASSWORD=demo123
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - omnify-network
    profiles:
      - full-demo

  # Optional: Grafana for monitoring demo
  grafana:
    image: grafana/grafana:latest
    container_name: omnify-grafana-demo
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=demo123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - omnify-network
    profiles:
      - full-demo

  # Optional: Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: omnify-prometheus-demo
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - omnify-network
    profiles:
      - full-demo

  # Demo API service (optional - can run locally instead)
  omnify-api:
    build:
      context: .
      dockerfile: docker/local/Dockerfile
    container_name: omnify-api-demo
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://omnify_user:omnify_password@postgres:5432/omnify_db
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=demo
      - DEBUG=true
      - DEMO_MODE=true
      - CLOUD_VARIANT=local
      - AI_ENGINE=demo
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./apps:/app/apps
      - ./lib:/app/lib
      - ./scripts:/app/scripts
    networks:
      - omnify-network
    profiles:
      - containerized

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  n8n_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  omnify-network:
    driver: bridge
