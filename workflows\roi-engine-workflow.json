{"name": "ROI Engine X™ - CAC Optimization Workflow", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "value": 15}]}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://localhost:8000/api/v1/analytics/cac", "options": {"queryParameters": {"parameters": [{"name": "client_id", "value": "={{$json[\"client_id\"]}}"}, {"name": "days", "value": "7"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Fetch CAC Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [460, 300], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"performance_status\"]}}", "operation": "notEqual", "value2": "on_target"}]}}, "name": "Check CAC Performance", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "http://localhost:8000/api/v1/agents/optimize", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"agent_type\": \"roi_engine\",\n  \"force_execution\": false\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Trigger AI Decision", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 200], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"action\"]}}", "operation": "notEqual", "value2": "no_action"}]}}, "name": "Check AI Recommendation", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"url": "http://localhost:8000/api/v1/webhooks/n8n/ai-decision", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"campaign_id\": \"{{$json[\"campaign_id\"]}}\",\n  \"action\": \"{{$json[\"action\"]}}\",\n  \"params\": {{$json[\"params\"]}},\n  \"confidence\": {{$json[\"confidence\"]}}\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Execute Bid Adjustment", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 100], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"url": "http://localhost:8000/api/v1/webhooks/external/performance-alert", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"event_type\": \"roi_optimization_completed\",\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"data\": {\n    \"action_taken\": \"{{$json[\"action\"]}}\",\n    \"confidence\": {{$json[\"confidence\"]}},\n    \"timestamp\": \"{{$now}}\"\n  }\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Log Optimization Result", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1560, 100], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"message": "No action needed - CAC performance is on target"}, "name": "No Action Needed", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [900, 400]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Fetch CAC Data", "type": "main", "index": 0}]]}, "Fetch CAC Data": {"main": [[{"node": "Check CAC Performance", "type": "main", "index": 0}]]}, "Check CAC Performance": {"main": [[{"node": "Trigger AI Decision", "type": "main", "index": 0}], [{"node": "No Action Needed", "type": "main", "index": 0}]]}, "Trigger AI Decision": {"main": [[{"node": "Check AI Recommendation", "type": "main", "index": 0}]]}, "Check AI Recommendation": {"main": [[{"node": "Execute Bid Adjustment", "type": "main", "index": 0}]]}, "Execute Bid Adjustment": {"main": [[{"node": "Log Optimization Result", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "id": "1"}