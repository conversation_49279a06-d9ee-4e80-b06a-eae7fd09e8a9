"""
Configuration settings for Omnify Marketing Cloud
"""
from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os

class Settings(BaseSettings):
    """Application settings"""
    
    # Environment
    ENVIRONMENT: str = Field(default="development")
    DEBUG: bool = Field(default=True)
    LOG_LEVEL: str = Field(default="INFO")
    
    # Database
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://omnify_user:omnify_password@localhost:5432/omnify_db"
    )
    
    # Redis
    REDIS_URL: str = Field(default="redis://localhost:6379/0")
    
    # JWT Authentication
    SECRET_KEY: str = Field(default="your-super-secret-jwt-key-change-this-in-production")
    ALGORITHM: str = Field(default="HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30)
    
    # OpenAI Configuration
    OPENAI_API_KEY: Optional[str] = Field(default=None)
    OPENAI_MODEL: str = Field(default="gpt-4-1106-preview")
    
    # Manus RL Configuration
    MANUS_RL_API_KEY: Optional[str] = Field(default=None)
    MANUS_RL_ENDPOINT: Optional[str] = Field(default=None)
    
    # Google Ads API
    GOOGLE_ADS_DEVELOPER_TOKEN: Optional[str] = Field(default=None)
    GOOGLE_ADS_CLIENT_ID: Optional[str] = Field(default=None)
    GOOGLE_ADS_CLIENT_SECRET: Optional[str] = Field(default=None)
    GOOGLE_ADS_REFRESH_TOKEN: Optional[str] = Field(default=None)
    
    # Meta/Facebook Ads API
    META_APP_ID: Optional[str] = Field(default=None)
    META_APP_SECRET: Optional[str] = Field(default=None)
    META_ACCESS_TOKEN: Optional[str] = Field(default=None)
    
    # n8n Configuration
    N8N_WEBHOOK_URL: str = Field(default="http://localhost:5678/webhook")
    N8N_API_KEY: Optional[str] = Field(default=None)
    
    # Monitoring & Logging
    SENTRY_DSN: Optional[str] = Field(default=None)
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=60)
    RATE_LIMIT_BURST: int = Field(default=10)
    
    # Email Configuration
    SMTP_HOST: str = Field(default="smtp.gmail.com")
    SMTP_PORT: int = Field(default=587)
    SMTP_USER: Optional[str] = Field(default=None)
    SMTP_PASSWORD: Optional[str] = Field(default=None)
    
    # Retool Dashboard
    RETOOL_API_KEY: Optional[str] = Field(default=None)
    RETOOL_WORKSPACE_URL: Optional[str] = Field(default=None)
    
    # AI Agent Configuration
    AI_CONFIDENCE_THRESHOLD: float = Field(default=0.85)
    MAX_RETRY_ATTEMPTS: int = Field(default=3)
    RETRY_BACKOFF_FACTOR: float = Field(default=2.0)
    
    # Campaign Management
    DEFAULT_BID_ADJUSTMENT_LIMIT: float = Field(default=0.3)  # 30% max adjustment
    MIN_CAMPAIGN_BUDGET: float = Field(default=100.0)  # $100 minimum
    MAX_CAMPAIGN_BUDGET: float = Field(default=10000.0)  # $10k maximum
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Global settings instance
settings = Settings()
