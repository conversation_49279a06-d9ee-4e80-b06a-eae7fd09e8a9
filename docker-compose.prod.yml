version: '3.8'

services:
  # Omnify API - Production
  omnify-api:
    build:
      context: .
      dockerfile: Dockerfile
    image: omnify-marketing-cloud:latest
    container_name: omnify-api-prod
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=postgresql+asyncpg://omnify_prod:${DB_PASSWORD}@postgres:5432/omnify_production
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_ADS_DEVELOPER_TOKEN=${GOOGLE_ADS_DEVELOPER_TOKEN}
      - META_APP_ID=${META_APP_ID}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
    networks:
      - omnify-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL - Production
  postgres:
    image: postgres:15-alpine
    container_name: omnify-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: omnify_production
      POSTGRES_USER: omnify_prod
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "5432:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - omnify-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omnify_prod -d omnify_production"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # Redis - Production
  redis:
    image: redis:7-alpine
    container_name: omnify-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - omnify-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server /usr/local/etc/redis/redis.conf

  # n8n - Production
  n8n:
    image: n8nio/n8n:latest
    container_name: omnify-n8n-prod
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n_production
      - DB_POSTGRESDB_USER=omnify_prod
      - DB_POSTGRESDB_PASSWORD=${DB_PASSWORD}
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - WEBHOOK_URL=https://workflows.omnify.com
      - N8N_METRICS=true
    volumes:
      - n8n_prod_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
    networks:
      - omnify-network
    depends_on:
      postgres:
        condition: service_healthy

  # Prometheus - Production
  prometheus:
    image: prom/prometheus:latest
    container_name: omnify-prometheus-prod
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./infrastructure/alert_rules.yml:/etc/prometheus/alert_rules.yml
      - prometheus_prod_data:/prometheus
    networks:
      - omnify-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=90d'
      - '--web.enable-lifecycle'

  # Grafana - Production
  grafana:
    image: grafana/grafana:latest
    container_name: omnify-grafana-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
      - GF_SERVER_ROOT_URL=https://dashboard.omnify.com
    volumes:
      - grafana_prod_data:/var/lib/grafana
      - ./infrastructure/grafana:/etc/grafana/provisioning
    networks:
      - omnify-network
    depends_on:
      - prometheus

  # Nginx - Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: omnify-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - omnify-network
    depends_on:
      - omnify-api
      - grafana
      - n8n

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: omnify-backup-prod
    restart: "no"
    environment:
      PGPASSWORD: ${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    networks:
      - omnify-network
    depends_on:
      - postgres
    entrypoint: ["/backup.sh"]

  # Log Aggregation
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: omnify-fluentd-prod
    restart: unless-stopped
    volumes:
      - ./logs:/fluentd/log
      - ./infrastructure/fluentd.conf:/fluentd/etc/fluent.conf
    networks:
      - omnify-network
    ports:
      - "24224:24224"
      - "24224:24224/udp"

  # Redis Exporter for Monitoring
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: omnify-redis-exporter-prod
    restart: unless-stopped
    environment:
      REDIS_ADDR: redis://redis:6379
    ports:
      - "9121:9121"
    networks:
      - omnify-network
    depends_on:
      - redis

  # Postgres Exporter for Monitoring
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: omnify-postgres-exporter-prod
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: postgresql://omnify_prod:${DB_PASSWORD}@postgres:5432/omnify_production?sslmode=disable
    ports:
      - "9187:9187"
    networks:
      - omnify-network
    depends_on:
      - postgres

  # Node Exporter for System Metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: omnify-node-exporter-prod
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - omnify-network
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  n8n_prod_data:
    driver: local
  prometheus_prod_data:
    driver: local
  grafana_prod_data:
    driver: local

networks:
  omnify-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
