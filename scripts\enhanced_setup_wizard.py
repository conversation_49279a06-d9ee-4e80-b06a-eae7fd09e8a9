#!/usr/bin/env python3
"""
Enhanced Setup Wizard for Omnify Marketing Cloud
Includes multi-cloud deployment options and AI-powered recommendations
"""
import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
import structlog
from datetime import datetime
import subprocess

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

logger = structlog.get_logger()

class EnhancedSetupWizard:
    """Enhanced setup wizard with multi-cloud support and AI recommendations"""

    def __init__(self):
        self.setup_data = {}
        self.cloud_recommendations = []

    def display_welcome(self):
        """Display welcome message and overview"""
        print("\n" + "="*80)
        print("🚀 OMNIFY MARKETING CLOUD - ENHANCED SETUP WIZARD")
        print("="*80)
        print("\n🌐 World's First Multi-Cloud Marketing Automation Platform")
        print("\n✨ Features:")
        print("   • 5 Cloud Deployment Options (AWS, Azure, GCP, Multi-Cloud, Open Source)")
        print("   • AI-Powered Cloud Recommendations")
        print("   • One-Command Universal Deployment")
        print("   • Universal Cross-Platform Demo")
        print("   • Seamless Cloud Migration")
        print("   • Smart Integration Wizard")
        print("\n🎯 This wizard will help you:")
        print("   1. Run live demo (30 seconds)")
        print("   2. Analyze your business requirements")
        print("   3. Get AI-powered cloud recommendations")
        print("   4. Deploy your preferred cloud variant")
        print("   5. Set up integrations and configurations")
        print("   6. Validate your deployment")
        print("\n" + "="*80 + "\n")

    def offer_demo(self) -> bool:
        """Offer to run the live demo first"""
        print("🎬 LIVE DEMO OPTION")
        print("-" * 30)
        print("Would you like to see Omnify in action first?")
        print("The live demo shows all three AI agents with real-time metrics.")
        print("Takes 30 seconds to start, works on all platforms.")
        print()

        while True:
            choice = input("Run live demo now? (y/n): ").strip().lower()
            if choice in ['y', 'yes']:
                return self.run_demo()
            elif choice in ['n', 'no']:
                print("Proceeding to setup wizard...")
                return True
            else:
                print("Please enter 'y' for yes or 'n' for no")

    def run_demo(self) -> bool:
        """Run the universal demo"""
        print("\n🚀 STARTING OMNIFY LIVE DEMO...")
        print("-" * 40)

        try:
            import subprocess
            import sys
            import platform
            import webbrowser
            import time

            # Check if omnify_demo.py exists
            demo_file = Path("omnify_demo.py")
            if not demo_file.exists():
                print("❌ Demo file not found: omnify_demo.py")
                print("Please ensure you're running from the project root directory.")
                return False

            print(f"Platform: {platform.system()}")
            print("Starting universal demo with automatic port detection...")
            print("Demo will open in your browser automatically.")
            print()
            print("📍 Demo Features:")
            print("   • ROI Engine X™ - Campaign optimization")
            print("   • Retention Reactor Pro™ - Churn prevention")
            print("   • EngageSense Ultra™ - Customer personalization")
            print("   • Real-time metrics with auto-refresh")
            print()

            # Start the demo in background
            process = subprocess.Popen([
                sys.executable, "omnify_demo.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            print("Demo starting... (this may take a few seconds)")
            time.sleep(3)

            # Check if process is still running
            if process.poll() is None:
                print("✅ Demo is running!")
                print()
                print("🌐 The demo should have opened in your browser.")
                print("If not, check the console output above for the URL.")
                print()
                print("💡 Demo Tips:")
                print("   • Watch the metrics update in real-time")
                print("   • Try refreshing to see new data")
                print("   • Check the API docs at /docs endpoint")
                print()

                input("Press Enter when you're ready to continue with setup (demo will keep running)...")
                return True
            else:
                # Process ended, check for errors
                stdout, stderr = process.communicate()
                print("❌ Demo failed to start")
                if stderr:
                    print(f"Error: {stderr.decode()}")
                return False

        except Exception as e:
            print(f"❌ Error starting demo: {str(e)}")
            print("You can try running the demo manually with: python omnify_demo.py")
            return False

    def collect_business_profile(self) -> Dict[str, Any]:
        """Collect business profile for AI recommendations"""
        print("📊 BUSINESS PROFILE ANALYSIS")
        print("-" * 40)

        # Company information
        company_name = input("Company Name: ").strip()
        industry = input("Industry (e.g., ecommerce, saas, fintech): ").strip().lower()

        # Team information
        while True:
            try:
                team_size = int(input("Team Size (number of people): "))
                break
            except ValueError:
                print("Please enter a valid number")

        # Budget information
        print("\nBudget Preference:")
        print("1. Low ($50K infrastructure)")
        print("2. Medium ($150K-$200K cloud credits)")
        print("3. High ($250K+ cloud credits)")

        while True:
            budget_choice = input("Select budget preference (1-3): ").strip()
            if budget_choice in ['1', '2', '3']:
                budget_map = {'1': 'low', '2': 'medium', '3': 'high'}
                budget_preference = budget_map[budget_choice]
                break
            print("Please select 1, 2, or 3")

        # Monthly spend
        while True:
            try:
                monthly_spend = float(input("Current Monthly Marketing Spend ($): "))
                break
            except ValueError:
                print("Please enter a valid number")

        # Technical expertise
        print("\nTechnical Expertise Level:")
        print("1. Low (prefer managed services)")
        print("2. Medium (some technical knowledge)")
        print("3. High (can manage complex infrastructure)")

        while True:
            tech_choice = input("Select technical expertise (1-3): ").strip()
            if tech_choice in ['1', '2', '3']:
                tech_map = {'1': 'low', '2': 'medium', '3': 'high'}
                technical_expertise = tech_map[tech_choice]
                break
            print("Please select 1, 2, or 3")

        # Priorities
        print("\nBusiness Priorities (select all that apply):")
        print("1. Performance optimization")
        print("2. Enterprise integration")
        print("3. Cost control")
        print("4. Vendor independence")
        print("5. Data analytics")
        print("6. Compliance (GDPR, SOC2)")

        priorities = []
        priority_map = {
            '1': 'performance', '2': 'enterprise', '3': 'cost',
            '4': 'vendor_independence', '5': 'analytics', '6': 'compliance'
        }

        priority_input = input("Enter priority numbers (e.g., 1,2,5): ").strip()
        for p in priority_input.split(','):
            p = p.strip()
            if p in priority_map:
                priorities.append(priority_map[p])

        # Existing tools
        print("\nExisting Tools (select all that apply):")
        print("1. Office 365 / Microsoft ecosystem")
        print("2. Google Workspace")
        print("3. Salesforce CRM")
        print("4. HubSpot")
        print("5. Other CRM")

        existing_tools = []
        tools_map = {
            '1': 'office365', '2': 'google_workspace', '3': 'salesforce',
            '4': 'hubspot', '5': 'other_crm'
        }

        tools_input = input("Enter tool numbers (e.g., 1,3): ").strip()
        for t in tools_input.split(','):
            t = t.strip()
            if t in tools_map:
                existing_tools.append(tools_map[t])

        # Compliance requirements
        compliance_requirements = []
        if 'compliance' in priorities:
            print("\nCompliance Requirements:")
            print("1. GDPR")
            print("2. SOC 2")
            print("3. HIPAA")
            print("4. PCI DSS")

            compliance_input = input("Enter compliance numbers (e.g., 1,2): ").strip()
            compliance_map = {'1': 'GDPR', '2': 'SOC2', '3': 'HIPAA', '4': 'PCI_DSS'}
            for c in compliance_input.split(','):
                c = c.strip()
                if c in compliance_map:
                    compliance_requirements.append(compliance_map[c])

        profile = {
            'company_name': company_name,
            'industry': industry,
            'team_size': team_size,
            'budget_preference': budget_preference,
            'monthly_spend': monthly_spend,
            'technical_expertise': technical_expertise,
            'priorities': priorities,
            'existing_tools': existing_tools,
            'compliance_requirements': compliance_requirements
        }

        self.setup_data['business_profile'] = profile
        return profile

    def generate_cloud_recommendations(self, profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate AI-powered cloud recommendations"""
        print("\n🤖 GENERATING AI-POWERED CLOUD RECOMMENDATIONS...")
        print("-" * 50)

        recommendations = []

        # AWS-Manus Hybrid
        aws_score = 70
        if 'performance' in profile['priorities']:
            aws_score += 20
        if profile['monthly_spend'] > 50000:
            aws_score += 10
        if profile['technical_expertise'] in ['medium', 'high']:
            aws_score += 10

        recommendations.append({
            'variant': 'aws',
            'name': 'AWS-Manus Hybrid (Patent-Focused)',
            'score': min(aws_score, 100),
            'setup_time': '8 weeks',
            'cost': '$150K+ cloud credits',
            'enterprise_readiness': 'High (9/10)',
            'benefits': [
                'Proprietary AI algorithms for competitive advantage',
                '20% cost reduction with Graviton3 instances',
                'Enterprise-grade security with AWS PrivateLink',
                'Patent protection for AI decision-making'
            ],
            'best_for': [
                'High-performance marketing optimization',
                'Large-scale enterprise deployments',
                'IP-sensitive competitive environments'
            ]
        })

        # Azure OpenAI Accelerator
        azure_score = 60
        if 'office365' in profile['existing_tools']:
            azure_score += 25
        if 'enterprise' in profile['priorities']:
            azure_score += 15
        if 'compliance' in profile['priorities']:
            azure_score += 15
        if profile['industry'] in ['enterprise', 'finance', 'healthcare']:
            azure_score += 10

        recommendations.append({
            'variant': 'azure',
            'name': 'Azure OpenAI Accelerator',
            'score': min(azure_score, 100),
            'setup_time': '7 weeks',
            'cost': '$200K+ cloud credits',
            'enterprise_readiness': 'High (8/10)',
            'benefits': [
                'Pre-built OpenAI integrations',
                'Native Office 365 and Dynamics 365 connectivity',
                'Built-in GDPR and SOC 2 compliance',
                'Power Platform low-code automation'
            ],
            'best_for': [
                'Microsoft ecosystem organizations',
                'Rapid enterprise deployment',
                'GDPR compliance requirements'
            ]
        })

        # GCP-Vertex Analytics
        gcp_score = 50
        if 'analytics' in profile['priorities']:
            gcp_score += 25
        if 'google_workspace' in profile['existing_tools']:
            gcp_score += 15
        if profile['industry'] in ['tech', 'data', 'analytics']:
            gcp_score += 15
        if profile['budget_preference'] == 'medium':
            gcp_score += 10

        recommendations.append({
            'variant': 'gcp',
            'name': 'GCP-Vertex Analytics Core',
            'score': min(gcp_score, 100),
            'setup_time': '9 weeks',
            'cost': '$180K+ cloud credits',
            'enterprise_readiness': 'Medium (7/10)',
            'benefits': [
                'Advanced BigQuery ML for real-time insights',
                'Cost-efficient per-request pricing',
                'Vertex AI Workbench for model development',
                'Real-time event processing with Pub/Sub'
            ],
            'best_for': [
                'Data-heavy analytics workloads',
                'Advanced machine learning projects',
                'Cost-optimized deployments'
            ]
        })

        # Multi-Cloud Lite
        multi_score = 40
        if 'vendor_independence' in profile['priorities']:
            multi_score += 30
        if profile['technical_expertise'] == 'high':
            multi_score += 15
        if profile['budget_preference'] == 'high':
            multi_score += 10

        recommendations.append({
            'variant': 'multi',
            'name': 'Multi-Cloud Lite',
            'score': min(multi_score, 100),
            'setup_time': '10 weeks',
            'cost': '$250K+ cloud credits',
            'enterprise_readiness': 'Medium (8/10)',
            'benefits': [
                'No single cloud vendor lock-in',
                'Cross-cloud failover and redundancy',
                'Best pricing across all clouds',
                'Data residency flexibility'
            ],
            'best_for': [
                'Risk-averse organizations',
                'Global compliance requirements',
                'Vendor independence strategy'
            ]
        })

        # Open Source Core
        oss_score = 30
        if profile['budget_preference'] == 'low':
            oss_score += 40
        if profile['technical_expertise'] == 'high':
            oss_score += 20
        if 'cost' in profile['priorities']:
            oss_score += 15

        recommendations.append({
            'variant': 'oss',
            'name': 'Open Source Core',
            'score': min(oss_score, 100),
            'setup_time': '12 weeks',
            'cost': '$50K infrastructure',
            'enterprise_readiness': 'Low (6/10)',
            'benefits': [
                'Complete source code access',
                'No cloud vendor fees',
                'On-premises deployment option',
                'Full customization capability'
            ],
            'best_for': [
                'Budget-conscious startups',
                'High security/privacy requirements',
                'Custom development needs'
            ]
        })

        # Sort by score
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        self.cloud_recommendations = recommendations

        return recommendations

    def display_recommendations(self, recommendations: List[Dict[str, Any]]):
        """Display cloud variant recommendations"""
        print("\n🎯 PERSONALIZED CLOUD RECOMMENDATIONS")
        print("=" * 50)

        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. {rec['name']}")
            print(f"   Score: {rec['score']}/100")
            print(f"   Setup Time: {rec['setup_time']}")
            print(f"   Investment: {rec['cost']}")
            print(f"   Enterprise Ready: {rec['enterprise_readiness']}")

            print("   Benefits:")
            for benefit in rec['benefits']:
                print(f"     • {benefit}")

            print("   Best For:")
            for use_case in rec['best_for']:
                print(f"     • {use_case}")

            if i == 1:
                print("   🌟 RECOMMENDED CHOICE")

        print("\n" + "=" * 50)

    def select_cloud_variant(self, recommendations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Allow user to select cloud variant"""
        print("\n🌐 SELECT CLOUD VARIANT")
        print("-" * 30)

        while True:
            choice = input(f"Select cloud variant (1-{len(recommendations)}) or 'info' for details: ").strip().lower()

            if choice == 'info':
                self.display_recommendations(recommendations)
                continue

            try:
                choice_num = int(choice)
                if 1 <= choice_num <= len(recommendations):
                    selected = recommendations[choice_num - 1]
                    print(f"\n✅ Selected: {selected['name']}")
                    self.setup_data['selected_variant'] = selected
                    return selected
                else:
                    print(f"Please enter a number between 1 and {len(recommendations)}")
            except ValueError:
                print("Please enter a valid number or 'info'")

    def configure_deployment(self, variant: Dict[str, Any]) -> Dict[str, Any]:
        """Configure deployment settings"""
        print(f"\n⚙️ CONFIGURE {variant['name'].upper()} DEPLOYMENT")
        print("-" * 50)

        # Environment
        print("Environment:")
        print("1. Development")
        print("2. Staging")
        print("3. Production")

        while True:
            env_choice = input("Select environment (1-3): ").strip()
            if env_choice in ['1', '2', '3']:
                env_map = {'1': 'dev', '2': 'staging', '3': 'production'}
                environment = env_map[env_choice]
                break
            print("Please select 1, 2, or 3")

        # Region
        if variant['variant'] in ['aws', 'azure', 'gcp']:
            region = input("Region (default: us-east-1): ").strip() or "us-east-1"
        else:
            region = "us-east-1"  # Default for multi-cloud and OSS

        config = {
            'variant': variant['variant'],
            'environment': environment,
            'region': region,
            'custom_config': {}
        }

        self.setup_data['deployment_config'] = config
        return config

    async def deploy_variant(self, config: Dict[str, Any]) -> bool:
        """Deploy the selected cloud variant"""
        print(f"\n🚀 DEPLOYING {config['variant'].upper()} VARIANT...")
        print("-" * 40)

        try:
            # Import deployment orchestrator
            from scripts.universal_deploy import UniversalDeploymentOrchestrator

            orchestrator = UniversalDeploymentOrchestrator()

            print(f"Starting deployment to {config['variant']} in {config['environment']} environment...")

            result = await orchestrator.deploy_variant(
                variant=config['variant'],
                environment=config['environment'],
                region=config['region'],
                custom_config=config.get('custom_config')
            )

            if result['status'] == 'success':
                print("✅ Deployment completed successfully!")

                # Display endpoints
                endpoints = result.get('deployment_result', {}).get('infrastructure', {}).get('endpoints', {})
                if endpoints:
                    print("\n📍 Access Points:")
                    for name, url in endpoints.items():
                        print(f"   {name.title()}: {url}")

                # Display next steps
                next_steps = result.get('report', {}).get('next_steps', [])
                if next_steps:
                    print("\n📋 Next Steps:")
                    for i, step in enumerate(next_steps, 1):
                        print(f"   {i}. {step}")

                self.setup_data['deployment_result'] = result
                return True
            else:
                print(f"❌ Deployment failed: {result.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            print(f"❌ Deployment error: {str(e)}")
            return False

    def generate_setup_summary(self):
        """Generate and display setup summary"""
        print("\n📋 SETUP SUMMARY")
        print("=" * 40)

        profile = self.setup_data.get('business_profile', {})
        variant = self.setup_data.get('selected_variant', {})
        config = self.setup_data.get('deployment_config', {})

        print(f"Company: {profile.get('company_name', 'N/A')}")
        print(f"Industry: {profile.get('industry', 'N/A')}")
        print(f"Team Size: {profile.get('team_size', 'N/A')}")
        print(f"Selected Variant: {variant.get('name', 'N/A')}")
        print(f"Environment: {config.get('environment', 'N/A')}")
        print(f"Region: {config.get('region', 'N/A')}")

        # Save setup data
        setup_file = f"setup_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(setup_file, 'w') as f:
            json.dump(self.setup_data, f, indent=2, default=str)

        print(f"\n💾 Setup data saved to: {setup_file}")

        print("\n🎉 OMNIFY MARKETING CLOUD SETUP COMPLETE!")
        print("Ready to revolutionize your marketing automation! 🚀")

async def main():
    """Main setup wizard function"""
    wizard = EnhancedSetupWizard()

    try:
        # Welcome and overview
        wizard.display_welcome()

        # Offer demo first
        demo_success = wizard.offer_demo()
        if not demo_success:
            print("Demo skipped or failed. Continuing with setup...")

        # Collect business profile
        profile = wizard.collect_business_profile()

        # Generate AI recommendations
        recommendations = wizard.generate_cloud_recommendations(profile)

        # Display recommendations
        wizard.display_recommendations(recommendations)

        # Select cloud variant
        selected_variant = wizard.select_cloud_variant(recommendations)

        # Configure deployment
        deployment_config = wizard.configure_deployment(selected_variant)

        # Confirm deployment
        print(f"\n🔍 DEPLOYMENT CONFIRMATION")
        print(f"Variant: {selected_variant['name']}")
        print(f"Environment: {deployment_config['environment']}")
        print(f"Region: {deployment_config['region']}")
        print(f"Estimated Time: {selected_variant['setup_time']}")
        print(f"Investment: {selected_variant['cost']}")

        confirm = input("\nProceed with deployment? (y/N): ").strip().lower()

        if confirm == 'y':
            # Deploy variant
            success = await wizard.deploy_variant(deployment_config)

            if success:
                # Generate summary
                wizard.generate_setup_summary()
            else:
                print("\n❌ Setup failed. Please check the logs and try again.")
        else:
            print("\n⏸️ Deployment cancelled. You can run this wizard again anytime.")
            wizard.generate_setup_summary()

    except KeyboardInterrupt:
        print("\n\n⏸️ Setup wizard interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Setup wizard error: {str(e)}")
        logger.error("Setup wizard failed", error=str(e))

if __name__ == "__main__":
    asyncio.run(main())
