#!/usr/bin/env python3
"""
🚀 Omnify Marketing Cloud - Demo Launcher
One-command demo setup and launch
"""
import asyncio
import os
import sys
import subprocess
import time
from pathlib import Path
import json

def print_banner():
    """Print demo banner"""
    print("\n" + "="*80)
    print("🚀 OMNIFY MARKETING CLOUD - DEMO LAUNCHER")
    print("="*80)
    print("🌐 World's First Multi-Cloud AI-Native Marketing Automation Platform")
    print("🤖 Featuring: ROI Engine X™, Retention Reactor Pro™, EngageSense Ultra™")
    print("⚡ Ready for immediate demonstration with realistic data")
    print("="*80 + "\n")

def check_requirements():
    """Check if required tools are installed"""
    print("🔍 Checking requirements...")
    
    requirements = {
        "python": {"cmd": ["python", "--version"], "min_version": "3.11"},
        "docker": {"cmd": ["docker", "--version"], "required": True},
        "docker-compose": {"cmd": ["docker-compose", "--version"], "required": True}
    }
    
    missing = []
    
    for tool, config in requirements.items():
        try:
            result = subprocess.run(
                config["cmd"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            print(f"  ✅ {tool}: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"  ❌ {tool}: Not found or not working")
            missing.append(tool)
    
    if missing:
        print(f"\n❌ Missing requirements: {', '.join(missing)}")
        print("Please install the missing tools and try again.")
        return False
    
    print("✅ All requirements satisfied!\n")
    return True

def setup_environment():
    """Setup environment file"""
    print("⚙️ Setting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            # Copy example to .env
            with open(env_example, 'r') as f:
                content = f.read()
            
            # Set demo-friendly defaults
            demo_content = content.replace(
                "DEBUG=true", "DEBUG=true"
            ).replace(
                "DEMO_MODE=false", "DEMO_MODE=true"
            ).replace(
                "your-openai-api-key", "demo-key-not-required"
            )
            
            with open(env_file, 'w') as f:
                f.write(demo_content)
            
            print("  ✅ Created .env file with demo settings")
        else:
            # Create minimal .env
            demo_env = """# Demo Environment for Omnify Marketing Cloud
DATABASE_URL=postgresql+asyncpg://omnify_user:omnify_password@localhost:5432/omnify_db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=demo-secret-key-for-testing-only
ENVIRONMENT=demo
DEBUG=true
DEMO_MODE=true
DEMO_DATA_ENABLED=true
CLOUD_VARIANT=local
AI_ENGINE=demo
LOG_LEVEL=INFO
"""
            with open(env_file, 'w') as f:
                f.write(demo_env)
            
            print("  ✅ Created minimal .env file for demo")
    else:
        print("  ✅ .env file already exists")

def start_infrastructure():
    """Start Docker infrastructure"""
    print("🐳 Starting infrastructure services...")
    
    try:
        # Start PostgreSQL and Redis
        result = subprocess.run(
            ["docker-compose", "up", "-d", "postgres", "redis"],
            capture_output=True,
            text=True,
            check=True
        )
        
        print("  ✅ PostgreSQL and Redis started")
        
        # Wait for services to be ready
        print("  ⏳ Waiting for services to be ready...")
        time.sleep(10)
        
        # Check if services are healthy
        postgres_check = subprocess.run(
            ["docker-compose", "exec", "-T", "postgres", "pg_isready", "-U", "omnify_user"],
            capture_output=True
        )
        
        if postgres_check.returncode == 0:
            print("  ✅ PostgreSQL is ready")
        else:
            print("  ⚠️ PostgreSQL might not be ready yet")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"  ❌ Failed to start infrastructure: {e}")
        print(f"  Error output: {e.stderr}")
        return False

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    try:
        # Check if we have a requirements.txt
        if Path("requirements.txt").exists():
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True)
        else:
            # Install minimal dependencies for demo
            minimal_deps = [
                "fastapi>=0.104.1",
                "uvicorn[standard]>=0.24.0",
                "sqlalchemy[asyncio]>=2.0.23",
                "asyncpg>=0.29.0",
                "redis>=5.0.1",
                "pydantic>=2.5.0",
                "pydantic-settings>=2.1.0",
                "python-jose[cryptography]>=3.3.0",
                "passlib[bcrypt]>=1.7.4",
                "structlog>=23.2.0",
                "python-multipart>=0.0.6"
            ]
            
            subprocess.run([
                sys.executable, "-m", "pip", "install"
            ] + minimal_deps, check=True)
        
        print("  ✅ Dependencies installed")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"  ❌ Failed to install dependencies: {e}")
        return False

def setup_database():
    """Setup database and demo data"""
    print("🗄️ Setting up database...")
    
    try:
        # Import and run database setup
        sys.path.append(str(Path.cwd()))
        
        # Create a simple database setup
        setup_script = """
import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path.cwd()))

async def setup_demo_db():
    try:
        from apps.core.database import init_db, engine
        print("  📊 Creating database tables...")
        await init_db()
        print("  ✅ Database tables created")
        
        # Close engine
        await engine.dispose()
        return True
    except Exception as e:
        print(f"  ❌ Database setup failed: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(setup_demo_db())
    sys.exit(0 if result else 1)
"""
        
        # Write and run setup script
        with open("temp_db_setup.py", "w") as f:
            f.write(setup_script)
        
        result = subprocess.run([sys.executable, "temp_db_setup.py"], check=True)
        
        # Clean up
        os.remove("temp_db_setup.py")
        
        print("  ✅ Database setup complete")
        return True
        
    except Exception as e:
        print(f"  ❌ Database setup failed: {e}")
        return False

def start_api_server():
    """Start the API server"""
    print("🚀 Starting Omnify API server...")
    
    try:
        # Start the server in background
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "apps.core.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ])
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if server is running
        try:
            import httpx
            response = httpx.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("  ✅ API server is running at http://localhost:8000")
                return process
            else:
                print(f"  ⚠️ API server responded with status {response.status_code}")
                return process
        except:
            print("  ⚠️ API server starting... (may take a moment)")
            return process
            
    except Exception as e:
        print(f"  ❌ Failed to start API server: {e}")
        return None

def display_demo_info():
    """Display demo information"""
    print("\n" + "🎉 DEMO IS READY!" + "\n")
    print("📍 Access Points:")
    print("  • API Documentation: http://localhost:8000/docs")
    print("  • Health Check: http://localhost:8000/health")
    print("  • API Root: http://localhost:8000/")
    
    print("\n🔑 Demo Features Available:")
    print("  • ✅ Multi-Cloud Architecture Simulation")
    print("  • ✅ AI-Powered Setup Wizard")
    print("  • ✅ Campaign Management APIs")
    print("  • ✅ Real-time Analytics")
    print("  • ✅ Integration Wizard")
    print("  • ✅ Health Monitoring")
    
    print("\n🧪 Try These Demo Commands:")
    print("  # Test API health")
    print("  curl http://localhost:8000/health")
    print("")
    print("  # Run enhanced setup wizard")
    print("  python scripts/enhanced_setup_wizard.py")
    print("")
    print("  # Test multi-cloud deployment (simulation)")
    print("  python scripts/universal_deploy.py deploy --variant aws --environment demo")
    print("")
    print("  # Run health checks")
    print("  python scripts/health_check.py --variant local --environment demo")
    
    print("\n📚 Documentation:")
    print("  • Complete Guide: docs/README.md")
    print("  • Quick Start: docs/quick-start.md")
    print("  • API Reference: http://localhost:8000/docs")
    
    print("\n⚠️ Note: This is a demo environment with simulated data.")
    print("For production deployment, use the multi-cloud setup wizard.")
    print("\n" + "="*80)

def main():
    """Main demo launcher"""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Start infrastructure
    if not start_infrastructure():
        print("❌ Failed to start infrastructure. Please check Docker and try again.")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies. Please check your Python environment.")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("❌ Failed to setup database. Please check PostgreSQL connection.")
        sys.exit(1)
    
    # Start API server
    server_process = start_api_server()
    if not server_process:
        print("❌ Failed to start API server.")
        sys.exit(1)
    
    # Display demo info
    display_demo_info()
    
    try:
        print("\n🔄 Demo is running... Press Ctrl+C to stop")
        server_process.wait()
    except KeyboardInterrupt:
        print("\n\n⏹️ Stopping demo...")
        server_process.terminate()
        
        # Stop Docker services
        try:
            subprocess.run(["docker-compose", "down"], check=True)
            print("✅ Infrastructure stopped")
        except:
            print("⚠️ Could not stop Docker services")
        
        print("👋 Demo stopped. Thank you for trying Omnify Marketing Cloud!")

if __name__ == "__main__":
    main()
