# 🧪 Omnify Testing Commands Reference

## **🚀 Quick Start Testing**

### **1. Quick Smoke Test (2-3 minutes)**
```bash
# Run essential tests only
python scripts/run_all_tests.py --quick

# Or run specific smoke tests
pytest -m smoke -v
```

### **2. Full Test Suite (10-15 minutes)**
```bash
# Run comprehensive test suite
python scripts/run_all_tests.py

# With specific cloud variant
python scripts/run_all_tests.py --cloud-variant aws --environment test
```

### **3. Production Readiness Check (5-10 minutes)**
```bash
# Check if ready for production deployment
python scripts/run_all_tests.py --production-check --cloud-variant aws

# Or run production validator directly
python scripts/production_readiness_validator.py --cloud-variant aws
```

## **🎯 Specific Test Categories**

### **Unit Tests**
```bash
# All unit tests
pytest tests/test_main.py tests/test_ai_agents.py -v

# With coverage
pytest tests/test_main.py tests/test_ai_agents.py --cov=lib --cov=apps --cov-report=html
```

### **Cloud Integration Tests**
```bash
# All cloud integrations
pytest tests/test_cloud_integrations.py -v

# AWS specific
pytest tests/test_cloud_integrations.py -m aws -v

# Azure specific
pytest tests/test_cloud_integrations.py -m azure -v

# GCP specific
pytest tests/test_cloud_integrations.py -m gcp -v
```

### **Infrastructure Tests**
```bash
# Test Terraform configurations
pytest tests/test_infrastructure.py -v

# Test specific cloud provider infrastructure
pytest tests/test_infrastructure.py::TestTerraformConfigurations::test_terraform_syntax_validation -v
```

### **Security Tests**
```bash
# Security test suite
pytest tests/test_cloud_integrations.py::TestDataProtectionAndEncryption -v

# Security scanning
bandit -r lib/ apps/ scripts/ -f txt
safety check
```

### **End-to-End Tests**
```bash
# Full E2E test suite
pytest tests/test_e2e.py -v

# Specific E2E scenarios
pytest tests/test_e2e.py::TestUserJourney -v
```

## **⚡ Performance Testing**

### **Performance Tests**
```bash
# Performance test suite
pytest -m performance -v

# Load testing (if available)
pytest tests/test_e2e.py::TestPerformance -v
```

### **Memory and Resource Tests**
```bash
# Memory profiling
python -m memory_profiler scripts/start.py

# Resource monitoring during tests
pytest tests/ --profile
```

## **🔍 Debugging and Diagnostics**

### **Verbose Testing**
```bash
# Maximum verbosity
pytest tests/ -vvv --tb=long

# Show local variables in tracebacks
pytest tests/ -vvv --tb=long --showlocals

# Stop on first failure
pytest tests/ -x
```

### **Test Discovery**
```bash
# List all tests without running
pytest --collect-only

# List tests by marker
pytest --collect-only -m "unit or integration"

# Show test durations
pytest tests/ --durations=10
```

## **📊 Coverage and Reporting**

### **Coverage Reports**
```bash
# HTML coverage report
pytest tests/ --cov=lib --cov=apps --cov-report=html

# Terminal coverage report
pytest tests/ --cov=lib --cov=apps --cov-report=term-missing

# XML coverage for CI
pytest tests/ --cov=lib --cov=apps --cov-report=xml
```

### **Test Reports**
```bash
# JSON test report
pytest tests/ --json-report --json-report-file=test_results.json

# JUnit XML report
pytest tests/ --junitxml=test_results.xml
```

## **🌐 Cloud-Specific Testing**

### **AWS Testing**
```bash
# AWS integration tests
python scripts/run_all_tests.py --cloud-variant aws

# AWS infrastructure validation
pytest tests/test_infrastructure.py::TestCloudResourceValidation::test_aws_resource_naming_conventions -v
```

### **Azure Testing**
```bash
# Azure integration tests
python scripts/run_all_tests.py --cloud-variant azure

# Azure infrastructure validation
pytest tests/test_infrastructure.py::TestCloudResourceValidation::test_azure_resource_naming_conventions -v
```

### **GCP Testing**
```bash
# GCP integration tests
python scripts/run_all_tests.py --cloud-variant gcp

# GCP infrastructure validation
pytest tests/test_infrastructure.py::TestCloudResourceValidation::test_gcp_resource_naming_conventions -v
```

### **Multi-Cloud Testing**
```bash
# Multi-cloud integration tests
python scripts/run_all_tests.py --cloud-variant multi

# Cross-cloud failover tests
pytest tests/test_cloud_integrations.py::TestIntegrationScenarios::test_cross_cloud_failover_scenario -v
```

## **🚨 CI/CD Testing**

### **Pre-Commit Tests**
```bash
# Quick pre-commit validation
python scripts/run_all_tests.py --quick

# Security scan before commit
bandit -r lib/ apps/ scripts/ -f txt
safety check
```

### **Pre-Deployment Tests**
```bash
# Full pre-deployment validation
python scripts/run_all_tests.py --production-check --cloud-variant aws --environment prod

# Infrastructure validation
pytest tests/test_infrastructure.py -v
```

### **Post-Deployment Tests**
```bash
# Health check after deployment
python scripts/health_check.py --comprehensive

# End-to-end validation
pytest tests/test_e2e.py -v
```

## **🔧 Troubleshooting**

### **Common Issues**

#### **Import Errors**
```bash
# Check Python path
python -c "import sys; print('\n'.join(sys.path))"

# Install dependencies
pip install -r requirements.txt
pip install -e .
```

#### **Database Connection Issues**
```bash
# Test database connection
python -c "from apps.core.database import get_db; print('DB OK')"

# Run with test database
pytest tests/ --tb=short
```

#### **Cloud Provider Issues**
```bash
# Test cloud credentials
python -c "import boto3; print(boto3.Session().get_credentials())"

# Mock cloud services for testing
pytest tests/test_cloud_integrations.py -v
```

### **Test Environment Setup**
```bash
# Create test environment
python -m venv test_env
source test_env/bin/activate  # Linux/Mac
# or
test_env\Scripts\activate  # Windows

# Install test dependencies
pip install -r requirements.txt
pip install pytest pytest-asyncio pytest-cov pytest-mock
```

## **📈 Continuous Testing**

### **Watch Mode**
```bash
# Auto-run tests on file changes (requires pytest-watch)
ptw tests/ -- --tb=short

# Watch specific files
ptw tests/test_main.py -- -v
```

### **Parallel Testing**
```bash
# Run tests in parallel (requires pytest-xdist)
pytest tests/ -n auto

# Specific number of workers
pytest tests/ -n 4
```

## **🎯 Test Results Interpretation**

### **Success Criteria**
- ✅ **Unit Tests**: 100% pass rate
- ✅ **Integration Tests**: 95%+ pass rate
- ✅ **Security Tests**: No critical vulnerabilities
- ✅ **Infrastructure Tests**: All configurations valid
- ✅ **E2E Tests**: Core user journeys working
- ✅ **Production Check**: Ready status

### **Exit Codes**
- `0`: All tests passed / System ready
- `1`: Some tests failed / System not ready
- `2`: Test execution error

### **Status Levels**
- **FULLY_FUNCTIONAL**: Ready for production
- **MOSTLY_FUNCTIONAL**: Minor issues, mostly ready
- **PARTIALLY_FUNCTIONAL**: Significant issues
- **NOT_FUNCTIONAL**: Major problems, not ready

## **📝 Example Test Workflows**

### **Developer Workflow**
```bash
# 1. Quick check before coding
python scripts/run_all_tests.py --quick

# 2. Run relevant tests during development
pytest tests/test_ai_agents.py -v

# 3. Full test before commit
python scripts/run_all_tests.py

# 4. Production check before deployment
python scripts/run_all_tests.py --production-check
```

### **CI/CD Pipeline**
```bash
# 1. Security scan
bandit -r lib/ apps/ scripts/ -f json -o security_report.json
safety check --json --output safety_report.json

# 2. Unit and integration tests
pytest tests/ --cov=lib --cov=apps --cov-report=xml --junitxml=test_results.xml

# 3. Infrastructure validation
pytest tests/test_infrastructure.py -v

# 4. Production readiness
python scripts/production_readiness_validator.py --cloud-variant aws --output readiness_report.json
```
