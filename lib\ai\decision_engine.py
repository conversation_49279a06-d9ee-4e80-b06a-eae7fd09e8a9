"""
Hybrid AI Decision Engine - ROI Engine X™
Uses Manus RL as primary predictor with OpenAI GPT-4 fallback
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog
from pydantic import BaseModel, Field
import openai
import httpx

from apps.core.config import settings
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class AIDecisionRequest(BaseModel):
    """Input data for AI decision making"""
    client_id: str
    campaign_data: Dict[str, Any]
    historical_metrics: List[Dict[str, Any]]
    current_cac: float
    target_cac: float
    current_roas: float
    target_roas: float = Field(default=2.5)
    budget_remaining: float
    days_remaining_in_period: int

class AIDecisionResponse(BaseModel):
    """Standardized AI decision output"""
    action: str  # "increase_bid", "decrease_bid", "pause_campaign", "no_action"
    confidence: float  # 0.0 to 1.0
    params: Dict[str, Any]  # Action-specific parameters
    reasoning: str
    model_used: str  # "manus_rl" or "openai_gpt4"
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class HybridAIDecisionEngine:
    """
    Hybrid AI decision maker that uses Manus RL as primary with OpenAI fallback
    """
    
    def __init__(self):
        self.confidence_threshold = settings.AI_CONFIDENCE_THRESHOLD
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.manus_rl_endpoint = settings.MANUS_RL_ENDPOINT
        self.manus_rl_api_key = settings.MANUS_RL_API_KEY
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def make_decision(self, request: AIDecisionRequest) -> AIDecisionResponse:
        """
        Make AI-driven decision with hybrid approach
        """
        logger.info(
            "Making AI decision",
            client_id=request.client_id,
            current_cac=request.current_cac,
            target_cac=request.target_cac
        )
        
        # Try Manus RL first
        if self.manus_rl_endpoint and self.manus_rl_api_key:
            try:
                decision = await self._manus_rl_decision(request)
                if decision.confidence >= self.confidence_threshold:
                    logger.info(
                        "Using Manus RL decision",
                        confidence=decision.confidence,
                        action=decision.action
                    )
                    return decision
                else:
                    logger.info(
                        "Manus RL confidence too low, falling back to OpenAI",
                        confidence=decision.confidence
                    )
            except Exception as e:
                logger.warning(
                    "Manus RL failed, falling back to OpenAI",
                    error=str(e)
                )
        
        # Fallback to OpenAI GPT-4
        decision = await self._openai_decision(request)
        logger.info(
            "Using OpenAI decision",
            confidence=decision.confidence,
            action=decision.action
        )
        return decision
    
    async def _manus_rl_decision(self, request: AIDecisionRequest) -> AIDecisionResponse:
        """
        Get decision from Manus RL model
        """
        # Prepare data for Manus RL
        rl_input = {
            "state": {
                "current_cac": request.current_cac,
                "target_cac": request.target_cac,
                "current_roas": request.current_roas,
                "target_roas": request.target_roas,
                "budget_remaining": request.budget_remaining,
                "days_remaining": request.days_remaining_in_period,
                "historical_performance": request.historical_metrics[-7:],  # Last 7 days
                "campaign_features": request.campaign_data
            }
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.manus_rl_endpoint}/predict",
                headers={
                    "Authorization": f"Bearer {self.manus_rl_api_key}",
                    "Content-Type": "application/json"
                },
                json=rl_input,
                timeout=30.0
            )
            response.raise_for_status()
            
            result = response.json()
            
            return AIDecisionResponse(
                action=result["action"],
                confidence=result["confidence"],
                params=result.get("params", {}),
                reasoning=result.get("reasoning", "Manus RL recommendation"),
                model_used="manus_rl"
            )
    
    async def _openai_decision(self, request: AIDecisionRequest) -> AIDecisionResponse:
        """
        Get decision from OpenAI GPT-4
        """
        # Create prompt for GPT-4
        prompt = self._create_openai_prompt(request)
        
        response = await self.openai_client.chat.completions.create(
            model=settings.OPENAI_MODEL,
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert digital marketing AI that optimizes ad campaigns for ROI. Always respond with valid JSON."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0.1,
            max_tokens=500
        )
        
        # Parse GPT-4 response
        try:
            result = json.loads(response.choices[0].message.content)
            
            return AIDecisionResponse(
                action=result["action"],
                confidence=result["confidence"],
                params=result.get("params", {}),
                reasoning=result.get("reasoning", "OpenAI GPT-4 recommendation"),
                model_used="openai_gpt4"
            )
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            return AIDecisionResponse(
                action="no_action",
                confidence=0.5,
                params={},
                reasoning="Failed to parse AI response, taking no action",
                model_used="openai_gpt4"
            )
    
    def _create_openai_prompt(self, request: AIDecisionRequest) -> str:
        """
        Create structured prompt for OpenAI GPT-4
        """
        return f"""
Analyze this campaign performance and recommend an action:

CURRENT METRICS:
- Current CAC: ${request.current_cac:.2f}
- Target CAC: ${request.target_cac:.2f}
- Current ROAS: {request.current_roas:.2f}
- Target ROAS: {request.target_roas:.2f}
- Budget Remaining: ${request.budget_remaining:.2f}
- Days Remaining: {request.days_remaining_in_period}

HISTORICAL PERFORMANCE (last 7 days):
{json.dumps(request.historical_metrics[-7:], indent=2)}

CAMPAIGN DATA:
{json.dumps(request.campaign_data, indent=2)}

DECISION RULES:
1. If CAC > target by >20%, consider decreasing bids/budget
2. If CAC < target by >15% and ROAS > target, consider increasing bids/budget
3. If ROAS < 1.5, consider pausing campaign
4. If budget is low (<10% remaining), be conservative

Respond with JSON in this exact format:
{{
    "action": "increase_bid|decrease_bid|pause_campaign|no_action",
    "confidence": 0.85,
    "params": {{
        "bid_adjustment": 0.15,
        "budget_adjustment": 0.10
    }},
    "reasoning": "Explanation of the decision"
}}
"""

class ROIEngineX:
    """
    ROI Engine X™ - Specialized AI agent for CAC optimization
    """
    
    def __init__(self):
        self.decision_engine = HybridAIDecisionEngine()
    
    async def optimize_campaign(
        self,
        client_id: str,
        campaign_data: Dict[str, Any],
        historical_metrics: List[Dict[str, Any]]
    ) -> AIDecisionResponse:
        """
        Main optimization function for ROI Engine X™
        """
        # Calculate current metrics
        current_cac = self._calculate_current_cac(historical_metrics)
        current_roas = self._calculate_current_roas(historical_metrics)
        
        # Create decision request
        request = AIDecisionRequest(
            client_id=client_id,
            campaign_data=campaign_data,
            historical_metrics=historical_metrics,
            current_cac=current_cac,
            target_cac=campaign_data.get("target_cac", 50.0),
            current_roas=current_roas,
            target_roas=campaign_data.get("target_roas", 2.5),
            budget_remaining=campaign_data.get("budget_remaining", 1000.0),
            days_remaining_in_period=campaign_data.get("days_remaining", 30)
        )
        
        # Get AI decision
        decision = await self.decision_engine.make_decision(request)
        
        logger.info(
            "ROI Engine X decision made",
            client_id=client_id,
            action=decision.action,
            confidence=decision.confidence,
            model_used=decision.model_used
        )
        
        return decision
    
    def _calculate_current_cac(self, metrics: List[Dict[str, Any]]) -> float:
        """Calculate current CAC from recent metrics"""
        if not metrics:
            return 0.0
        
        recent_metrics = metrics[-7:]  # Last 7 days
        total_spend = sum(m.get("spend", 0) for m in recent_metrics)
        total_conversions = sum(m.get("conversions", 0) for m in recent_metrics)
        
        return total_spend / total_conversions if total_conversions > 0 else 0.0
    
    def _calculate_current_roas(self, metrics: List[Dict[str, Any]]) -> float:
        """Calculate current ROAS from recent metrics"""
        if not metrics:
            return 0.0
        
        recent_metrics = metrics[-7:]  # Last 7 days
        total_spend = sum(m.get("spend", 0) for m in recent_metrics)
        total_revenue = sum(m.get("revenue", 0) for m in recent_metrics)
        
        return total_revenue / total_spend if total_spend > 0 else 0.0
