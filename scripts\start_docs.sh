#!/bin/bash

# Omnify Marketing Cloud - Documentation Server Startup Script
# Starts the documentation server with all dependencies

set -e

echo "🚀 Starting Omnify Marketing Cloud Documentation Server..."
echo "=" * 60

# Check if we're in the right directory
if [ ! -f "docs/README.md" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected to find docs/README.md"
    exit 1
fi

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python 3.8+ required, found Python $python_version"
    exit 1
fi

echo "✅ Python version: $python_version"

# Install documentation dependencies if needed
echo "📦 Installing documentation dependencies..."

# Check if markdown is installed
if ! python3 -c "import markdown" 2>/dev/null; then
    echo "Installing markdown..."
    pip install markdown
fi

# Check if fastapi is installed
if ! python3 -c "import fastapi" 2>/dev/null; then
    echo "Installing FastAPI and dependencies..."
    pip install fastapi uvicorn jinja2 python-multipart
fi

# Check if pygments is installed (for syntax highlighting)
if ! python3 -c "import pygments" 2>/dev/null; then
    echo "Installing Pygments for syntax highlighting..."
    pip install pygments
fi

echo "✅ Dependencies installed"

# Create docs directory structure if it doesn't exist
mkdir -p docs/static
mkdir -p docs/templates

# Check if documentation files exist
doc_files=(
    "docs/README.md"
    "docs/quick-start.md"
    "docs/mvp-status.md"
    "docs/api-reference.md"
    "docs/architecture.md"
    "docs/index.html"
)

missing_files=()
for file in "${doc_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "⚠️  Warning: Some documentation files are missing:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    echo ""
fi

echo "📚 Found documentation files:"
for file in "${doc_files[@]}"; do
    if [ -f "$file" ]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file (missing)"
    fi
done

echo ""
echo "🌐 Starting documentation server..."
echo "   URL: http://localhost:8080"
echo "   API: http://localhost:8080/api/docs"
echo "   Search: http://localhost:8080/api/search?q=query"
echo ""
echo "📖 Available documentation:"
echo "   • Quick Start Guide: http://localhost:8080/docs/quick-start"
echo "   • MVP Status: http://localhost:8080/docs/mvp-status"
echo "   • API Reference: http://localhost:8080/docs/api-reference"
echo "   • Architecture: http://localhost:8080/docs/architecture"
echo ""
echo "🔍 Features:"
echo "   • Real-time markdown rendering"
echo "   • Full-text search across all docs"
echo "   • Table of contents generation"
echo "   • Syntax highlighting for code blocks"
echo "   • Responsive design"
echo ""
echo "Press Ctrl+C to stop the server"
echo "=" * 60

# Start the documentation server
python3 scripts/serve_docs.py
