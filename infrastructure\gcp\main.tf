# Terraform configuration for GCP-Vertex Analytics variant
# Complete Infrastructure as Code for production deployment

terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Variables
variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "region" {
  description = "GCP region"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

# Enable required APIs
resource "google_project_service" "required_apis" {
  for_each = toset([
    "run.googleapis.com",
    "sql-component.googleapis.com",
    "sqladmin.googleapis.com",
    "redis.googleapis.com",
    "aiplatform.googleapis.com",
    "bigquery.googleapis.com",
    "pubsub.googleapis.com",
    "containerregistry.googleapis.com",
    "cloudbuild.googleapis.com"
  ])

  project = var.project_id
  service = each.value

  disable_dependent_services = true
}

# VPC Network
resource "google_compute_network" "omnify_vpc" {
  name                    = "omnify-vpc-${var.environment}"
  auto_create_subnetworks = false
  project                 = var.project_id

  depends_on = [google_project_service.required_apis]
}

# Subnet
resource "google_compute_subnetwork" "omnify_subnet" {
  name          = "omnify-subnet-${var.environment}"
  ip_cidr_range = "10.0.0.0/24"
  region        = var.region
  network       = google_compute_network.omnify_vpc.id
  project       = var.project_id

  secondary_ip_range {
    range_name    = "services-range"
    ip_cidr_range = "***********/24"
  }

  secondary_ip_range {
    range_name    = "pod-ranges"
    ip_cidr_range = "************/22"
  }
}

# Firewall Rules
resource "google_compute_firewall" "omnify_firewall" {
  name    = "omnify-firewall-${var.environment}"
  network = google_compute_network.omnify_vpc.name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["80", "443", "8000"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["omnify-${var.environment}"]
}

# Cloud SQL Instance
resource "google_sql_database_instance" "omnify_postgres" {
  name             = "omnify-postgres-${var.environment}-${random_string.suffix.result}"
  database_version = "POSTGRES_15"
  region           = var.region
  project          = var.project_id

  settings {
    tier                        = "db-custom-2-8192"
    availability_type           = "REGIONAL"
    disk_type                   = "PD_SSD"
    disk_size                   = 100
    disk_autoresize             = true
    disk_autoresize_limit       = 500

    backup_configuration {
      enabled                        = true
      start_time                     = "03:00"
      location                       = var.region
      point_in_time_recovery_enabled = true
      transaction_log_retention_days = 7
      backup_retention_settings {
        retained_backups = 30
        retention_unit   = "COUNT"
      }
    }

    ip_configuration {
      ipv4_enabled    = true
      private_network = google_compute_network.omnify_vpc.id
      require_ssl     = true

      authorized_networks {
        name  = "all"
        value = "0.0.0.0/0"
      }
    }

    database_flags {
      name  = "log_checkpoints"
      value = "on"
    }

    database_flags {
      name  = "log_connections"
      value = "on"
    }

    database_flags {
      name  = "log_disconnections"
      value = "on"
    }
  }

  deletion_protection = false

  depends_on = [google_project_service.required_apis]
}

# Cloud SQL Database
resource "google_sql_database" "omnify_database" {
  name     = "omnify"
  instance = google_sql_database_instance.omnify_postgres.name
  project  = var.project_id
}

# Cloud SQL User
resource "google_sql_user" "omnify_user" {
  name     = "omnify_admin"
  instance = google_sql_database_instance.omnify_postgres.name
  password = random_password.postgres_password.result
  project  = var.project_id
}

# Redis Instance
resource "google_redis_instance" "omnify_redis" {
  name           = "omnify-redis-${var.environment}-${random_string.suffix.result}"
  tier           = "STANDARD_HA"
  memory_size_gb = 4
  region         = var.region
  project        = var.project_id

  location_id             = "${var.region}-a"
  alternative_location_id = "${var.region}-b"

  authorized_network = google_compute_network.omnify_vpc.id

  redis_version     = "REDIS_7_0"
  display_name      = "Omnify Redis ${var.environment}"
  reserved_ip_range = "***********/29"

  depends_on = [google_project_service.required_apis]
}

# BigQuery Dataset
resource "google_bigquery_dataset" "omnify_dataset" {
  dataset_id                  = "omnify_analytics_${var.environment}"
  friendly_name               = "Omnify Analytics ${var.environment}"
  description                 = "Analytics dataset for Omnify Marketing Cloud"
  location                    = "US"
  default_table_expiration_ms = 3600000
  project                     = var.project_id

  labels = {
    environment = var.environment
    project     = "omnify-marketing-cloud"
  }

  depends_on = [google_project_service.required_apis]
}

# Pub/Sub Topic
resource "google_pubsub_topic" "omnify_events" {
  name    = "omnify-events-${var.environment}"
  project = var.project_id

  labels = {
    environment = var.environment
    project     = "omnify-marketing-cloud"
  }

  depends_on = [google_project_service.required_apis]
}

# Pub/Sub Subscription
resource "google_pubsub_subscription" "omnify_events_subscription" {
  name    = "omnify-events-subscription-${var.environment}"
  topic   = google_pubsub_topic.omnify_events.name
  project = var.project_id

  ack_deadline_seconds = 20

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  depends_on = [google_project_service.required_apis]
}

# Cloud Run Service
resource "google_cloud_run_service" "omnify_api" {
  name     = "omnify-api-${var.environment}"
  location = var.region
  project  = var.project_id

  template {
    spec {
      containers {
        image = "gcr.io/${var.project_id}/omnify:latest"

        ports {
          container_port = 8000
        }

        env {
          name  = "CLOUD_VARIANT"
          value = "gcp"
        }

        env {
          name  = "AI_ENGINE"
          value = "vertex_ai"
        }

        env {
          name  = "DATABASE_TYPE"
          value = "cloud_sql_postgresql"
        }

        env {
          name  = "CACHE_TYPE"
          value = "memorystore_redis"
        }

        env {
          name  = "ENVIRONMENT"
          value = var.environment
        }

        env {
          name  = "DATABASE_URL"
          value = "postgresql://${google_sql_user.omnify_user.name}:${random_password.postgres_password.result}@${google_sql_database_instance.omnify_postgres.connection_name}/${google_sql_database.omnify_database.name}"
        }

        env {
          name  = "REDIS_URL"
          value = "redis://${google_redis_instance.omnify_redis.host}:${google_redis_instance.omnify_redis.port}"
        }

        env {
          name  = "GCP_PROJECT_ID"
          value = var.project_id
        }

        env {
          name  = "PUBSUB_TOPIC"
          value = google_pubsub_topic.omnify_events.name
        }

        resources {
          limits = {
            cpu    = "2000m"
            memory = "4Gi"
          }
        }
      }

      container_concurrency = 80
      timeout_seconds       = 300
    }

    metadata {
      annotations = {
        "autoscaling.knative.dev/minScale"      = "1"
        "autoscaling.knative.dev/maxScale"      = "100"
        "run.googleapis.com/cloudsql-instances" = google_sql_database_instance.omnify_postgres.connection_name
        "run.googleapis.com/vpc-access-connector" = google_vpc_access_connector.omnify_connector.id
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  depends_on = [google_project_service.required_apis]
}

# VPC Access Connector
resource "google_vpc_access_connector" "omnify_connector" {
  name          = "omnify-connector-${var.environment}"
  ip_cidr_range = "********/28"
  network       = google_compute_network.omnify_vpc.name
  region        = var.region
  project       = var.project_id

  depends_on = [google_project_service.required_apis]
}

# Cloud Run IAM
resource "google_cloud_run_service_iam_binding" "omnify_api_public" {
  location = google_cloud_run_service.omnify_api.location
  project  = google_cloud_run_service.omnify_api.project
  service  = google_cloud_run_service.omnify_api.name
  role     = "roles/run.invoker"

  members = [
    "allUsers",
  ]
}

# Random resources
resource "random_string" "suffix" {
  length  = 8
  special = false
  upper   = false
}

resource "random_password" "postgres_password" {
  length  = 16
  special = true
}

# Outputs
output "cloud_run_url" {
  description = "Cloud Run service URL"
  value       = google_cloud_run_service.omnify_api.status[0].url
}

output "sql_connection_name" {
  description = "Cloud SQL connection name"
  value       = google_sql_database_instance.omnify_postgres.connection_name
}

output "redis_host" {
  description = "Redis host"
  value       = google_redis_instance.omnify_redis.host
}

output "bigquery_dataset" {
  description = "BigQuery dataset ID"
  value       = google_bigquery_dataset.omnify_dataset.dataset_id
}

output "pubsub_topic" {
  description = "Pub/Sub topic name"
  value       = google_pubsub_topic.omnify_events.name
}
