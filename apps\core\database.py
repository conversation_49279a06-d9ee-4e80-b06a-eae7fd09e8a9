"""
Database configuration and models for Omnify Marketing Cloud
"""
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import AsyncGenerator
import uuid

from apps.core.config import settings

# Create async engine
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300,
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Base class for all models
Base = declarative_base()

class TimestampMixin:
    """Mixin for created_at and updated_at timestamps"""
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Client(Base, TimestampMixin):
    """Client/Company model"""
    __tablename__ = "clients"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    email = Column(String(255), unique=True, index=True)
    industry = Column(String(100))
    revenue_range = Column(String(50))  # e.g., "50M-100M"
    status = Column(String(20), default="active")  # active, inactive, trial

    # API credentials (encrypted)
    google_ads_customer_id = Column(String(20))
    meta_ads_account_id = Column(String(50))

    # Relationships
    campaigns = relationship("Campaign", back_populates="client")
    ai_decisions = relationship("AIDecision", back_populates="client")
    customer_profiles = relationship("CustomerProfile", back_populates="client")

class Campaign(Base, TimestampMixin):
    """Marketing campaign model"""
    __tablename__ = "campaigns"

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)

    name = Column(String(255), nullable=False)
    platform = Column(String(50), nullable=False)  # google_ads, meta_ads
    campaign_id = Column(String(100))  # External platform campaign ID

    budget = Column(Float)
    target_cac = Column(Float)
    current_cac = Column(Float)
    roas = Column(Float)

    status = Column(String(20), default="active")

    # Relationships
    client = relationship("Client", back_populates="campaigns")
    metrics = relationship("CampaignMetric", back_populates="campaign")

class CampaignMetric(Base, TimestampMixin):
    """Daily campaign performance metrics"""
    __tablename__ = "campaign_metrics"

    id = Column(Integer, primary_key=True, index=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=False)

    date = Column(DateTime(timezone=True), nullable=False)
    impressions = Column(Integer, default=0)
    clicks = Column(Integer, default=0)
    conversions = Column(Integer, default=0)
    spend = Column(Float, default=0.0)
    revenue = Column(Float, default=0.0)

    # Calculated metrics
    ctr = Column(Float)  # Click-through rate
    cpc = Column(Float)  # Cost per click
    cac = Column(Float)  # Customer acquisition cost
    roas = Column(Float)  # Return on ad spend

    # Relationships
    campaign = relationship("Campaign", back_populates="metrics")

class AIDecision(Base, TimestampMixin):
    """AI agent decision log"""
    __tablename__ = "ai_decisions"

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)

    agent_type = Column(String(50), nullable=False)  # roi_engine, retention_reactor, engage_sense
    decision_id = Column(String(100), default=lambda: str(uuid.uuid4()))

    input_data = Column(JSON)
    decision = Column(JSON)
    confidence = Column(Float)

    action_taken = Column(String(100))
    action_params = Column(JSON)

    result = Column(String(20))  # success, failed, pending
    error_message = Column(Text)

    # Relationships
    client = relationship("Client", back_populates="ai_decisions")

class User(Base, TimestampMixin):
    """User authentication model"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)

    first_name = Column(String(100))
    last_name = Column(String(100))
    role = Column(String(50), default="user")  # admin, user, viewer

    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)

    # Client association
    client_id = Column(Integer, ForeignKey("clients.id"))

class CustomerProfile(Base, TimestampMixin):
    """Customer profile for retention and engagement analysis"""
    __tablename__ = "customer_profiles"

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)
    customer_id = Column(String(100), nullable=False)  # External customer ID

    # Demographics
    email = Column(String(255))
    first_name = Column(String(100))
    last_name = Column(String(100))
    age_group = Column(String(20))  # "18-24", "25-34", etc.
    location = Column(String(100))

    # Purchase behavior
    total_purchases = Column(Integer, default=0)
    total_spent = Column(Float, default=0.0)
    average_order_value = Column(Float, default=0.0)
    last_purchase_date = Column(DateTime(timezone=True))
    purchase_frequency = Column(Float, default=0.0)  # purchases per month

    # Engagement metrics
    email_engagement_rate = Column(Float, default=0.0)
    website_sessions = Column(Integer, default=0)
    page_views = Column(Integer, default=0)
    session_duration_avg = Column(Float, default=0.0)
    bounce_rate = Column(Float, default=0.0)

    # Support and satisfaction
    support_tickets = Column(Integer, default=0)
    satisfaction_score = Column(Float)  # 1-10 scale
    nps_score = Column(Integer)  # Net Promoter Score

    # Calculated scores
    churn_probability = Column(Float, default=0.0)
    engagement_score = Column(Float, default=0.0)
    lifetime_value = Column(Float, default=0.0)

    # Segmentation
    segment = Column(String(50))  # "champion", "loyal", "at_risk", etc.
    risk_level = Column(String(20))  # "low", "medium", "high", "critical"

    # Relationships
    client = relationship("Client", back_populates="customer_profiles")

class RetentionAction(Base, TimestampMixin):
    """Retention actions taken for customers"""
    __tablename__ = "retention_actions"

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)
    customer_id = Column(String(100), nullable=False)

    action_type = Column(String(50), nullable=False)  # "email_campaign", "discount_offer", etc.
    action_details = Column(JSON)

    trigger_reason = Column(String(100))  # "high_churn_risk", "low_engagement", etc.
    priority = Column(Integer, default=3)  # 1-5 scale

    status = Column(String(20), default="pending")  # pending, executed, failed
    execution_date = Column(DateTime(timezone=True))

    # Results
    cost = Column(Float, default=0.0)
    estimated_impact = Column(Float, default=0.0)
    actual_impact = Column(Float)
    success = Column(Boolean)

    # Relationships
    client = relationship("Client")

class PersonalizationRule(Base, TimestampMixin):
    """Personalization rules for EngageSense Ultra™"""
    __tablename__ = "personalization_rules"

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)

    rule_name = Column(String(255), nullable=False)
    segment = Column(String(50))  # Target segment
    conditions = Column(JSON)  # Rule conditions

    content_type = Column(String(50))  # "email", "web_content", "product_recommendation"
    content_template = Column(Text)
    parameters = Column(JSON)

    priority = Column(Integer, default=3)
    is_active = Column(Boolean, default=True)

    # Performance metrics
    times_triggered = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)
    avg_engagement_lift = Column(Float, default=0.0)

    # Relationships
    client = relationship("Client")

# Database dependency
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

async def init_db():
    """Initialize database tables"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
