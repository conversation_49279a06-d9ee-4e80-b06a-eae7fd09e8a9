"""
Smart Integration Wizard - User-Friendly Integration Helper
Provides step-by-step guidance and intelligent recommendations
"""
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field
import structlog

from apps.core.database import get_db, Client, Integration
from apps.auth.dependencies import get_current_active_user
from lib.integrations.integration_manager import integration_manager
from apps.core.websocket_manager import notification_service
from lib.cloud.cloud_abstraction import CloudProvider, CloudAbstractionLayer

logger = structlog.get_logger()

router = APIRouter()

class IntegrationStep(BaseModel):
    """Integration step model"""
    step_id: str
    title: str
    description: str
    status: str = "pending"  # pending, in_progress, completed, failed
    instructions: List[str]
    estimated_time: str
    required_data: List[Dict[str, Any]]
    help_links: List[Dict[str, str]]
    troubleshooting: List[Dict[str, str]]

class IntegrationRecommendation(BaseModel):
    """Integration recommendation model"""
    integration_type: str
    priority: str = Field(..., description="high, medium, low")
    reason: str
    benefits: List[str]
    estimated_setup_time: str
    difficulty: str = Field(..., description="easy, medium, advanced")
    prerequisites: List[str]

class IntegrationProgress(BaseModel):
    """Integration progress tracking"""
    client_id: str
    integration_type: str
    current_step: int
    total_steps: int
    progress_percentage: float
    status: str
    steps: List[IntegrationStep]
    next_recommended_action: str
    estimated_completion: str

class SmartSuggestion(BaseModel):
    """Smart suggestion for next actions"""
    suggestion_id: str
    type: str = Field(..., description="integration, optimization, feature")
    title: str
    description: str
    impact: str = Field(..., description="high, medium, low")
    effort: str = Field(..., description="low, medium, high")
    category: str
    action_url: str
    estimated_benefit: str

class CloudVariantRecommendation(BaseModel):
    """Cloud variant recommendation for optimal deployment"""
    variant: str = Field(..., description="aws, azure, gcp, multi, oss")
    variant_name: str
    priority: str = Field(..., description="high, medium, low")
    reason: str
    benefits: List[str]
    estimated_setup_time: str
    cost_estimate: str
    enterprise_readiness: str
    use_cases: List[str]

class IntegrationWizard:
    """Smart integration wizard with AI-powered recommendations"""

    def __init__(self):
        self.integration_templates = {
            "google_ads": {
                "name": "Google Ads",
                "difficulty": "easy",
                "estimated_time": "10-15 minutes",
                "steps": [
                    {
                        "step_id": "google_ads_1",
                        "title": "Enable Google Ads API",
                        "description": "Enable the Google Ads API in your Google Cloud Console",
                        "instructions": [
                            "Go to Google Cloud Console (console.cloud.google.com)",
                            "Select or create a project",
                            "Navigate to APIs & Services > Library",
                            "Search for 'Google Ads API' and enable it",
                            "Note your project ID for the next step"
                        ],
                        "estimated_time": "3-5 minutes",
                        "required_data": [
                            {"field": "project_id", "type": "string", "description": "Google Cloud Project ID"}
                        ],
                        "help_links": [
                            {"title": "Google Ads API Setup Guide", "url": "https://developers.google.com/google-ads/api/docs/first-call/overview"},
                            {"title": "Google Cloud Console", "url": "https://console.cloud.google.com"}
                        ],
                        "troubleshooting": [
                            {"issue": "Can't find Google Ads API", "solution": "Make sure you're in the correct Google Cloud project and have proper permissions"},
                            {"issue": "API enable button is grayed out", "solution": "Check if billing is enabled for your Google Cloud project"}
                        ]
                    },
                    {
                        "step_id": "google_ads_2",
                        "title": "Get Developer Token",
                        "description": "Obtain your Google Ads Developer Token",
                        "instructions": [
                            "Go to Google Ads (ads.google.com)",
                            "Click Tools & Settings > Setup > API Center",
                            "Apply for a developer token if you don't have one",
                            "Copy your developer token (starts with 'your-token-here')",
                            "Note: Basic access level is sufficient for most use cases"
                        ],
                        "estimated_time": "2-3 minutes",
                        "required_data": [
                            {"field": "developer_token", "type": "string", "description": "Google Ads Developer Token"}
                        ],
                        "help_links": [
                            {"title": "Developer Token Guide", "url": "https://developers.google.com/google-ads/api/docs/first-call/dev-token"}
                        ],
                        "troubleshooting": [
                            {"issue": "Don't see API Center", "solution": "Make sure you have admin access to the Google Ads account"},
                            {"issue": "Developer token pending", "solution": "Basic access tokens are approved instantly, Standard access may take time"}
                        ]
                    },
                    {
                        "step_id": "google_ads_3",
                        "title": "Create OAuth2 Credentials",
                        "description": "Set up OAuth2 credentials for secure access",
                        "instructions": [
                            "Return to Google Cloud Console",
                            "Go to APIs & Services > Credentials",
                            "Click 'Create Credentials' > 'OAuth 2.0 Client IDs'",
                            "Choose 'Web application' as application type",
                            "Add authorized redirect URI: https://your-domain.com/auth/google/callback",
                            "Download the JSON credentials file"
                        ],
                        "estimated_time": "3-5 minutes",
                        "required_data": [
                            {"field": "client_id", "type": "string", "description": "OAuth2 Client ID"},
                            {"field": "client_secret", "type": "string", "description": "OAuth2 Client Secret"}
                        ],
                        "help_links": [
                            {"title": "OAuth2 Setup Guide", "url": "https://developers.google.com/identity/protocols/oauth2"}
                        ],
                        "troubleshooting": [
                            {"issue": "Redirect URI error", "solution": "Make sure the redirect URI exactly matches your domain"},
                            {"issue": "Can't download credentials", "solution": "Check if you have proper permissions in Google Cloud Console"}
                        ]
                    },
                    {
                        "step_id": "google_ads_4",
                        "title": "Connect Your Account",
                        "description": "Link your Google Ads account to Omnify",
                        "instructions": [
                            "Enter your credentials in the form below",
                            "Click 'Test Connection' to verify setup",
                            "Authorize Omnify to access your Google Ads data",
                            "Select which accounts to sync",
                            "Choose sync frequency (recommended: every 4 hours)"
                        ],
                        "estimated_time": "2-3 minutes",
                        "required_data": [
                            {"field": "customer_id", "type": "string", "description": "Google Ads Customer ID (format: 123-456-7890)"}
                        ],
                        "help_links": [
                            {"title": "Find Customer ID", "url": "https://support.google.com/google-ads/answer/1704344"}
                        ],
                        "troubleshooting": [
                            {"issue": "Connection test fails", "solution": "Double-check all credentials and ensure the Google Ads account is active"},
                            {"issue": "No accounts found", "solution": "Make sure the OAuth account has access to the Google Ads accounts you want to sync"}
                        ]
                    }
                ]
            },
            "meta_ads": {
                "name": "Meta Ads (Facebook/Instagram)",
                "difficulty": "easy",
                "estimated_time": "8-12 minutes",
                "steps": [
                    {
                        "step_id": "meta_ads_1",
                        "title": "Create Facebook App",
                        "description": "Create a Facebook app for API access",
                        "instructions": [
                            "Go to Facebook Developers (developers.facebook.com)",
                            "Click 'Create App' and choose 'Business' type",
                            "Enter app name and contact email",
                            "Add 'Marketing API' product to your app",
                            "Note your App ID and App Secret"
                        ],
                        "estimated_time": "3-4 minutes",
                        "required_data": [
                            {"field": "app_id", "type": "string", "description": "Facebook App ID"},
                            {"field": "app_secret", "type": "string", "description": "Facebook App Secret"}
                        ],
                        "help_links": [
                            {"title": "Facebook App Setup", "url": "https://developers.facebook.com/docs/development/create-an-app/"}
                        ],
                        "troubleshooting": [
                            {"issue": "Can't create app", "solution": "Make sure your Facebook account is verified and has developer access"},
                            {"issue": "Marketing API not available", "solution": "Ensure you selected 'Business' app type during creation"}
                        ]
                    },
                    {
                        "step_id": "meta_ads_2",
                        "title": "Get Access Token",
                        "description": "Generate a long-lived access token",
                        "instructions": [
                            "In your Facebook app, go to Tools > Graph API Explorer",
                            "Select your app from the dropdown",
                            "Add permissions: ads_read, ads_management, business_management",
                            "Generate access token",
                            "Use Access Token Debugger to extend token lifetime",
                            "Copy the long-lived access token"
                        ],
                        "estimated_time": "3-4 minutes",
                        "required_data": [
                            {"field": "access_token", "type": "string", "description": "Long-lived Access Token"}
                        ],
                        "help_links": [
                            {"title": "Access Token Guide", "url": "https://developers.facebook.com/docs/facebook-login/access-tokens/"}
                        ],
                        "troubleshooting": [
                            {"issue": "Token expires quickly", "solution": "Make sure to extend the token using the Access Token Debugger"},
                            {"issue": "Permission denied", "solution": "Ensure you have admin access to the Facebook ad accounts you want to connect"}
                        ]
                    },
                    {
                        "step_id": "meta_ads_3",
                        "title": "Connect Ad Accounts",
                        "description": "Link your Meta ad accounts to Omnify",
                        "instructions": [
                            "Enter your app credentials and access token",
                            "Click 'Test Connection' to verify setup",
                            "Select which ad accounts to sync",
                            "Choose data sync preferences",
                            "Set up webhook notifications (optional but recommended)"
                        ],
                        "estimated_time": "2-4 minutes",
                        "required_data": [
                            {"field": "ad_account_id", "type": "string", "description": "Meta Ad Account ID (format: act_123456789)"}
                        ],
                        "help_links": [
                            {"title": "Find Ad Account ID", "url": "https://www.facebook.com/business/help/****************"}
                        ],
                        "troubleshooting": [
                            {"issue": "No ad accounts found", "solution": "Make sure the access token has proper permissions and account access"},
                            {"issue": "Sync fails", "solution": "Check if the ad account is active and has recent activity"}
                        ]
                    }
                ]
            },
            "email_system": {
                "name": "Email System (SendGrid/SMTP)",
                "difficulty": "easy",
                "estimated_time": "5-8 minutes",
                "steps": [
                    {
                        "step_id": "email_1",
                        "title": "Choose Email Provider",
                        "description": "Select your preferred email service",
                        "instructions": [
                            "Choose between SendGrid (recommended) or custom SMTP",
                            "SendGrid: Sign up at sendgrid.com if you don't have an account",
                            "SMTP: Gather your email provider's SMTP settings",
                            "Consider deliverability and volume requirements"
                        ],
                        "estimated_time": "2-3 minutes",
                        "required_data": [
                            {"field": "provider_type", "type": "select", "options": ["sendgrid", "smtp"], "description": "Email Provider Type"}
                        ],
                        "help_links": [
                            {"title": "SendGrid Setup", "url": "https://sendgrid.com/docs/for-developers/sending-email/quickstart-python/"},
                            {"title": "SMTP Settings Guide", "url": "https://support.google.com/mail/answer/7126229"}
                        ],
                        "troubleshooting": [
                            {"issue": "Unsure which to choose", "solution": "SendGrid is recommended for better deliverability and analytics"}
                        ]
                    },
                    {
                        "step_id": "email_2",
                        "title": "Configure Email Service",
                        "description": "Set up your email service credentials",
                        "instructions": [
                            "For SendGrid: Create an API key with 'Mail Send' permissions",
                            "For SMTP: Gather host, port, username, and password",
                            "Test email sending with a sample message",
                            "Configure sender domain and authentication"
                        ],
                        "estimated_time": "3-5 minutes",
                        "required_data": [
                            {"field": "api_key_or_password", "type": "string", "description": "SendGrid API Key or SMTP Password"},
                            {"field": "from_email", "type": "email", "description": "Default sender email address"}
                        ],
                        "help_links": [
                            {"title": "SendGrid API Keys", "url": "https://sendgrid.com/docs/ui/account-and-settings/api-keys/"}
                        ],
                        "troubleshooting": [
                            {"issue": "API key doesn't work", "solution": "Make sure the API key has 'Mail Send' permissions enabled"},
                            {"issue": "SMTP authentication fails", "solution": "Check if 2FA is enabled and use an app-specific password"}
                        ]
                    }
                ]
            }
        }

    def get_integration_recommendations(self, client_data: Dict[str, Any]) -> List[IntegrationRecommendation]:
        """Generate smart integration recommendations based on client data"""
        recommendations = []

        # Analyze current integrations
        current_integrations = client_data.get("integrations", [])
        has_google_ads = any(i.get("type") == "google_ads" for i in current_integrations)
        has_meta_ads = any(i.get("type") == "meta_ads" for i in current_integrations)
        has_email = any(i.get("type") == "email_system" for i in current_integrations)

        # Business size and industry analysis
        monthly_spend = client_data.get("monthly_spend", 0)
        industry = client_data.get("industry", "").lower()
        team_size = client_data.get("team_size", 1)

        # Google Ads recommendation
        if not has_google_ads:
            priority = "high" if monthly_spend > 5000 else "medium"
            recommendations.append(IntegrationRecommendation(
                integration_type="google_ads",
                priority=priority,
                reason="Google Ads is the largest digital advertising platform with advanced targeting options",
                benefits=[
                    "Access to 90%+ of search traffic",
                    "Advanced audience targeting",
                    "Real-time bid optimization",
                    "Detailed performance analytics"
                ],
                estimated_setup_time="10-15 minutes",
                difficulty="easy",
                prerequisites=["Google Ads account", "Google Cloud project"]
            ))

        # Meta Ads recommendation
        if not has_meta_ads:
            priority = "high" if "ecommerce" in industry or "retail" in industry else "medium"
            recommendations.append(IntegrationRecommendation(
                integration_type="meta_ads",
                priority=priority,
                reason="Meta platforms offer excellent visual advertising and social commerce opportunities",
                benefits=[
                    "Access to 3+ billion users across Facebook and Instagram",
                    "Advanced visual advertising formats",
                    "Social commerce integration",
                    "Detailed audience insights"
                ],
                estimated_setup_time="8-12 minutes",
                difficulty="easy",
                prerequisites=["Facebook Business account", "Facebook app"]
            ))

        # Email system recommendation
        if not has_email:
            recommendations.append(IntegrationRecommendation(
                integration_type="email_system",
                priority="high",
                reason="Email marketing has the highest ROI of all digital marketing channels",
                benefits=[
                    "Highest ROI marketing channel (4200% average ROI)",
                    "Direct customer communication",
                    "Automated nurture sequences",
                    "Detailed engagement tracking"
                ],
                estimated_setup_time="5-8 minutes",
                difficulty="easy",
                prerequisites=["Email service account (SendGrid recommended)"]
            ))

        # Advanced integrations for larger clients
        if monthly_spend > 20000 and team_size > 5:
            recommendations.append(IntegrationRecommendation(
                integration_type="crm_system",
                priority="medium",
                reason="CRM integration enables advanced customer lifecycle management",
                benefits=[
                    "Unified customer data",
                    "Advanced lead scoring",
                    "Sales and marketing alignment",
                    "Customer lifetime value tracking"
                ],
                estimated_setup_time="15-25 minutes",
                difficulty="medium",
                prerequisites=["CRM system (Salesforce, HubSpot, etc.)"]
            ))

        # Sort by priority
        priority_order = {"high": 3, "medium": 2, "low": 1}
        recommendations.sort(key=lambda x: priority_order[x.priority], reverse=True)

        return recommendations

    def get_smart_suggestions(self, client_id: str, client_data: Dict[str, Any]) -> List[SmartSuggestion]:
        """Generate smart suggestions for next actions"""
        suggestions = []

        # Integration suggestions
        integration_recs = self.get_integration_recommendations(client_data)
        for rec in integration_recs[:2]:  # Top 2 recommendations
            suggestions.append(SmartSuggestion(
                suggestion_id=f"integrate_{rec.integration_type}",
                type="integration",
                title=f"Connect {rec.integration_type.replace('_', ' ').title()}",
                description=rec.reason,
                impact=rec.priority,
                effort="low" if rec.difficulty == "easy" else "medium",
                category="integrations",
                action_url=f"/integrations/wizard/{rec.integration_type}",
                estimated_benefit=f"Setup time: {rec.estimated_setup_time}"
            ))

        # Optimization suggestions based on current data
        current_roas = client_data.get("current_roas", 0)
        if current_roas < 3.0:
            suggestions.append(SmartSuggestion(
                suggestion_id="optimize_roas",
                type="optimization",
                title="Improve ROAS with AI Optimization",
                description="Your current ROAS could be improved with automated bid optimization",
                impact="high",
                effort="low",
                category="ai_optimization",
                action_url="/campaigns/optimize",
                estimated_benefit="15-30% ROAS improvement"
            ))

        # Feature suggestions
        if len(client_data.get("integrations", [])) >= 2:
            suggestions.append(SmartSuggestion(
                suggestion_id="enable_advanced_analytics",
                type="feature",
                title="Enable Advanced Analytics Dashboard",
                description="Unlock cross-platform insights with your connected integrations",
                impact="medium",
                effort="low",
                category="analytics",
                action_url="/dashboard/analytics/advanced",
                estimated_benefit="Better decision making"
            ))

        return suggestions

    def get_cloud_variant_recommendations(self, client_data: Dict[str, Any]) -> List[CloudVariantRecommendation]:
        """Generate cloud variant recommendations based on client requirements"""
        recommendations = []

        # Analyze client requirements
        monthly_spend = client_data.get("monthly_spend", 0)
        team_size = client_data.get("team_size", 1)
        industry = client_data.get("industry", "").lower()
        compliance_requirements = client_data.get("compliance_requirements", [])
        technical_expertise = client_data.get("technical_expertise", "medium")
        budget_preference = client_data.get("budget_preference", "medium")

        # AWS-Manus Hybrid recommendation
        aws_priority = "high" if monthly_spend > 50000 and "performance" in client_data.get("priorities", []) else "medium"
        recommendations.append(CloudVariantRecommendation(
            variant="aws",
            variant_name="AWS-Manus Hybrid (Patent-Focused)",
            priority=aws_priority,
            reason="Best performance with proprietary Manus RL algorithms and AWS Bedrock fallback",
            benefits=[
                "Proprietary AI algorithms for competitive advantage",
                "20% cost reduction with Graviton3 instances",
                "Enterprise-grade security with AWS PrivateLink",
                "Auto-scaling with predictive scaling",
                "Patent protection for AI decision-making"
            ],
            estimated_setup_time="8 weeks",
            cost_estimate="$150K+ cloud credits",
            enterprise_readiness="High (9/10)",
            use_cases=[
                "High-performance marketing optimization",
                "Large-scale enterprise deployments",
                "IP-sensitive competitive environments",
                "Advanced AI-driven decision making"
            ]
        ))

        # Azure OpenAI Accelerator recommendation
        azure_priority = "high" if "enterprise" in industry or "office365" in client_data.get("existing_tools", []) else "medium"
        recommendations.append(CloudVariantRecommendation(
            variant="azure",
            variant_name="Azure OpenAI Accelerator",
            priority=azure_priority,
            reason="Fastest enterprise deployment with native Office 365 and Dynamics 365 integration",
            benefits=[
                "Pre-built OpenAI integrations",
                "Native Office 365 and Dynamics 365 connectivity",
                "Built-in GDPR and SOC 2 compliance",
                "Power Platform low-code automation",
                "Confidential Computing for sensitive data"
            ],
            estimated_setup_time="7 weeks",
            cost_estimate="$200K+ cloud credits",
            enterprise_readiness="High (8/10)",
            use_cases=[
                "Microsoft ecosystem organizations",
                "Rapid enterprise deployment",
                "GDPR compliance requirements",
                "Low-code workflow automation"
            ]
        ))

        # GCP-Vertex Analytics recommendation
        gcp_priority = "high" if "analytics" in client_data.get("priorities", []) or "data_science" in industry else "medium"
        recommendations.append(CloudVariantRecommendation(
            variant="gcp",
            variant_name="GCP-Vertex Analytics Core",
            priority=gcp_priority,
            reason="Advanced analytics and machine learning with BigQuery ML and Vertex AI",
            benefits=[
                "Advanced BigQuery ML for real-time insights",
                "Cost-efficient per-request pricing",
                "Vertex AI Workbench for model development",
                "Real-time event processing with Pub/Sub",
                "Superior data analytics capabilities"
            ],
            estimated_setup_time="9 weeks",
            cost_estimate="$180K+ cloud credits",
            enterprise_readiness="Medium (7/10)",
            use_cases=[
                "Data-heavy analytics workloads",
                "Advanced machine learning projects",
                "Real-time event processing",
                "Cost-optimized deployments"
            ]
        ))

        # Multi-Cloud recommendation
        multi_priority = "high" if "vendor_independence" in client_data.get("priorities", []) else "low"
        recommendations.append(CloudVariantRecommendation(
            variant="multi",
            variant_name="Multi-Cloud Lite",
            priority=multi_priority,
            reason="Vendor independence with cross-cloud failover and best-of-breed services",
            benefits=[
                "No single cloud vendor lock-in",
                "Cross-cloud failover and redundancy",
                "Best pricing across all clouds",
                "Data residency flexibility",
                "Risk mitigation through diversification"
            ],
            estimated_setup_time="10 weeks",
            cost_estimate="$250K+ cloud credits",
            enterprise_readiness="Medium (8/10)",
            use_cases=[
                "Risk-averse organizations",
                "Global compliance requirements",
                "Vendor independence strategy",
                "High availability requirements"
            ]
        ))

        # Open Source recommendation
        oss_priority = "high" if budget_preference == "low" or "control" in client_data.get("priorities", []) else "low"
        recommendations.append(CloudVariantRecommendation(
            variant="oss",
            variant_name="Open Source Core",
            priority=oss_priority,
            reason="Full control and cost efficiency with on-premises deployment option",
            benefits=[
                "Complete source code access",
                "No cloud vendor fees",
                "On-premises deployment option",
                "Full customization capability",
                "Maximum data privacy and control"
            ],
            estimated_setup_time="12 weeks",
            cost_estimate="$50K infrastructure",
            enterprise_readiness="Low (6/10)",
            use_cases=[
                "Budget-conscious startups",
                "High security/privacy requirements",
                "Custom development needs",
                "On-premises deployment requirements"
            ]
        ))

        # Sort by priority and client fit
        priority_order = {"high": 3, "medium": 2, "low": 1}
        recommendations.sort(key=lambda x: priority_order[x.priority], reverse=True)

        return recommendations

# Initialize wizard
integration_wizard = IntegrationWizard()

@router.get("/wizard/recommendations")
async def get_integration_recommendations(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get personalized integration recommendations"""

    # Get client data for recommendations
    client_data = {
        "integrations": [],  # Would fetch from database
        "monthly_spend": 15000,  # Would fetch from metrics
        "industry": "ecommerce",  # Would fetch from client profile
        "team_size": 3  # Would fetch from user count
    }

    recommendations = integration_wizard.get_integration_recommendations(client_data)
    smart_suggestions = integration_wizard.get_smart_suggestions(str(current_user.client_id), client_data)

    return {
        "recommendations": recommendations,
        "smart_suggestions": smart_suggestions,
        "total_recommendations": len(recommendations),
        "priority_actions": [r for r in recommendations if r.priority == "high"]
    }

@router.get("/wizard/{integration_type}")
async def get_integration_wizard(
    integration_type: str,
    current_user = Depends(get_current_active_user)
):
    """Get step-by-step integration wizard for specific integration"""

    if integration_type not in integration_wizard.integration_templates:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Integration type '{integration_type}' not supported"
        )

    template = integration_wizard.integration_templates[integration_type]

    # Convert template to IntegrationStep objects
    steps = []
    for i, step_data in enumerate(template["steps"]):
        steps.append(IntegrationStep(**step_data))

    progress = IntegrationProgress(
        client_id=str(current_user.client_id),
        integration_type=integration_type,
        current_step=0,
        total_steps=len(steps),
        progress_percentage=0.0,
        status="not_started",
        steps=steps,
        next_recommended_action=f"Start with: {steps[0].title}",
        estimated_completion=template["estimated_time"]
    )

    return {
        "integration_name": template["name"],
        "difficulty": template["difficulty"],
        "estimated_time": template["estimated_time"],
        "progress": progress,
        "helpful_tips": [
            "Take your time with each step - accuracy is more important than speed",
            "Test connections at each step to catch issues early",
            "Keep your credentials secure and don't share them",
            "Contact support if you get stuck - we're here to help!"
        ]
    }

@router.post("/wizard/{integration_type}/step/{step_id}")
async def complete_integration_step(
    integration_type: str,
    step_id: str,
    step_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Complete a specific integration step"""

    try:
        # Validate step data
        if integration_type not in integration_wizard.integration_templates:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Integration type '{integration_type}' not supported"
            )

        # Find the step
        template = integration_wizard.integration_templates[integration_type]
        step_found = None
        step_index = 0

        for i, step in enumerate(template["steps"]):
            if step["step_id"] == step_id:
                step_found = step
                step_index = i
                break

        if not step_found:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Step '{step_id}' not found"
            )

        # Validate required data
        required_fields = [field["field"] for field in step_found["required_data"]]
        missing_fields = [field for field in required_fields if field not in step_data]

        if missing_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing required fields: {', '.join(missing_fields)}"
            )

        # Test connection if this is a connection step
        if step_id.endswith("_4") or "connect" in step_id.lower():
            # Background task to test connection
            background_tasks.add_task(
                test_integration_connection,
                integration_type,
                step_data,
                current_user.client_id
            )

        # Calculate progress
        progress_percentage = ((step_index + 1) / len(template["steps"])) * 100

        # Determine next action
        if step_index + 1 < len(template["steps"]):
            next_step = template["steps"][step_index + 1]
            next_action = f"Next: {next_step['title']}"
            status = "in_progress"
        else:
            next_action = "Integration complete! Start using your connected data."
            status = "completed"

        # Send real-time update
        await notification_service.notify_ai_optimization(
            client_id=str(current_user.client_id),
            agent_name="Integration Wizard",
            campaign_id="integration_setup",
            optimization_type="integration_progress",
            expected_impact={
                "step_completed": step_found["title"],
                "progress": f"{progress_percentage:.1f}%",
                "next_action": next_action
            }
        )

        logger.info(
            "Integration step completed",
            client_id=current_user.client_id,
            integration_type=integration_type,
            step_id=step_id,
            progress=progress_percentage
        )

        return {
            "status": "success",
            "step_completed": step_found["title"],
            "progress_percentage": progress_percentage,
            "next_action": next_action,
            "integration_status": status,
            "message": f"Step '{step_found['title']}' completed successfully!"
        }

    except Exception as e:
        logger.error("Integration step failed", error=str(e), step_id=step_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to complete step: {str(e)}"
        )

@router.get("/suggestions")
async def get_smart_suggestions(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get smart suggestions for improving integrations and performance"""

    # Get client data
    client_data = {
        "integrations": [],  # Would fetch from database
        "monthly_spend": 15000,
        "current_roas": 2.8,
        "industry": "ecommerce",
        "team_size": 3
    }

    suggestions = integration_wizard.get_smart_suggestions(str(current_user.client_id), client_data)

    return {
        "suggestions": suggestions,
        "total_suggestions": len(suggestions),
        "high_impact_suggestions": [s for s in suggestions if s.impact == "high"],
        "quick_wins": [s for s in suggestions if s.effort == "low"]
    }

async def test_integration_connection(
    integration_type: str,
    credentials: Dict[str, Any],
    client_id: str
):
    """Background task to test integration connection"""
    try:
        # This would use the actual integration manager to test connection
        success = await integration_manager.test_connection(integration_type, credentials)

        if success:
            await notification_service.notify_ai_optimization(
                client_id=str(client_id),
                agent_name="Integration Wizard",
                campaign_id="integration_test",
                optimization_type="connection_test",
                expected_impact={
                    "status": "success",
                    "message": f"{integration_type} connection successful!",
                    "next_steps": "You can now start syncing data"
                }
            )
        else:
            await notification_service.notify_ai_optimization(
                client_id=str(client_id),
                agent_name="Integration Wizard",
                campaign_id="integration_test",
                optimization_type="connection_test",
                expected_impact={
                    "status": "failed",
                    "message": f"{integration_type} connection failed",
                    "next_steps": "Please check your credentials and try again"
                }
            )

    except Exception as e:
        logger.error("Connection test failed", error=str(e), integration_type=integration_type)

@router.get("/cloud-variants")
async def get_cloud_variant_recommendations(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get cloud variant recommendations based on client profile"""

    # Get client data for recommendations
    client_data = {
        "monthly_spend": 75000,  # Would fetch from metrics
        "team_size": 8,  # Would fetch from user count
        "industry": "enterprise",  # Would fetch from client profile
        "compliance_requirements": ["SOC2", "GDPR"],  # Would fetch from requirements
        "technical_expertise": "high",  # Would assess from team profile
        "budget_preference": "medium",  # Would fetch from preferences
        "priorities": ["performance", "enterprise"],  # Would fetch from client goals
        "existing_tools": ["office365", "salesforce"]  # Would fetch from integrations
    }

    cloud_recommendations = integration_wizard.get_cloud_variant_recommendations(client_data)

    return {
        "cloud_variants": cloud_recommendations,
        "total_variants": len(cloud_recommendations),
        "recommended_variant": cloud_recommendations[0] if cloud_recommendations else None,
        "client_profile": {
            "monthly_spend": client_data["monthly_spend"],
            "team_size": client_data["team_size"],
            "industry": client_data["industry"],
            "technical_expertise": client_data["technical_expertise"]
        }
    }

@router.post("/deploy-variant")
async def deploy_cloud_variant(
    variant_config: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Deploy a specific cloud variant"""

    variant = variant_config.get("variant")
    environment = variant_config.get("environment", "dev")
    region = variant_config.get("region", "us-east-1")

    if variant not in ["aws", "azure", "gcp", "multi", "oss"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid cloud variant: {variant}"
        )

    # Start deployment in background
    background_tasks.add_task(
        _deploy_variant_background,
        variant,
        environment,
        region,
        str(current_user.client_id)
    )

    return {
        "status": "deployment_started",
        "variant": variant,
        "environment": environment,
        "region": region,
        "estimated_completion": "30-60 minutes",
        "tracking_id": f"deploy_{variant}_{environment}_{current_user.client_id}"
    }

async def _deploy_variant_background(
    variant: str,
    environment: str,
    region: str,
    client_id: str
):
    """Background task for cloud variant deployment"""
    try:
        logger.info(
            "Starting cloud variant deployment",
            variant=variant,
            environment=environment,
            client_id=client_id
        )

        # Import here to avoid circular imports
        from scripts.universal_deploy import UniversalDeploymentOrchestrator

        orchestrator = UniversalDeploymentOrchestrator()
        result = await orchestrator.deploy_variant(
            variant=variant,
            environment=environment,
            region=region
        )

        # Send notification to client
        await notification_service.send_notification(
            client_id=client_id,
            notification_type="deployment_complete",
            data={
                "variant": variant,
                "status": result["status"],
                "endpoints": result.get("deployment_result", {}).get("infrastructure", {}).get("endpoints", {}),
                "next_steps": result.get("report", {}).get("next_steps", [])
            }
        )

        logger.info("Cloud variant deployment completed", variant=variant, status=result["status"])

    except Exception as e:
        logger.error("Cloud variant deployment failed", variant=variant, error=str(e))

        # Send error notification
        await notification_service.send_notification(
            client_id=client_id,
            notification_type="deployment_failed",
            data={
                "variant": variant,
                "error": str(e),
                "support_contact": "<EMAIL>"
            }
        )
