"""
Tests for AI Agents - ROI Engine X™, Retention Reactor Pro™, EngageSense Ultra™
"""
import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import numpy as np

from lib.ai.decision_engine import ROIEngineX, AIDecisionRequest
from lib.ai.retention_reactor import RetentionReactorPro, CustomerData
from lib.ai.engage_sense import EngageSenseUltra, CustomerBehavior

class TestROIEngineX:
    """Test ROI Engine X™ functionality"""
    
    @pytest.fixture
    def roi_engine(self):
        return ROIEngineX()
    
    @pytest.fixture
    def sample_campaign_data(self):
        return {
            "campaign_id": "test_campaign_123",
            "platform": "google_ads",
            "budget": 10000.0,
            "target_cac": 50.0,
            "budget_remaining": 7000.0,
            "days_remaining": 15
        }
    
    @pytest.fixture
    def sample_historical_metrics(self):
        return [
            {
                "date": "2024-01-01",
                "spend": 500.0,
                "revenue": 1250.0,
                "conversions": 10,
                "clicks": 200,
                "impressions": 5000
            },
            {
                "date": "2024-01-02", 
                "spend": 600.0,
                "revenue": 1200.0,
                "conversions": 8,
                "clicks": 180,
                "impressions": 4800
            }
        ]
    
    @pytest.mark.asyncio
    async def test_calculate_current_cac(self, roi_engine, sample_historical_metrics):
        """Test CAC calculation"""
        cac = roi_engine._calculate_current_cac(sample_historical_metrics)
        expected_cac = (500.0 + 600.0) / (10 + 8)  # Total spend / Total conversions
        assert abs(cac - expected_cac) < 0.01
    
    @pytest.mark.asyncio
    async def test_calculate_current_roas(self, roi_engine, sample_historical_metrics):
        """Test ROAS calculation"""
        roas = roi_engine._calculate_current_roas(sample_historical_metrics)
        expected_roas = (1250.0 + 1200.0) / (500.0 + 600.0)  # Total revenue / Total spend
        assert abs(roas - expected_roas) < 0.01
    
    @pytest.mark.asyncio
    @patch('lib.ai.decision_engine.HybridAIDecisionEngine.make_decision')
    async def test_optimize_campaign(self, mock_decision, roi_engine, sample_campaign_data, sample_historical_metrics):
        """Test campaign optimization"""
        # Mock AI decision response
        mock_decision.return_value = Mock(
            action="increase_bid",
            confidence=0.9,
            params={"bid_adjustment": 0.15},
            reasoning="CAC below target, increase bids",
            model_used="manus_rl"
        )
        
        result = await roi_engine.optimize_campaign(
            client_id="test_client",
            campaign_data=sample_campaign_data,
            historical_metrics=sample_historical_metrics
        )
        
        assert result.action == "increase_bid"
        assert result.confidence == 0.9
        assert result.params["bid_adjustment"] == 0.15
        mock_decision.assert_called_once()

class TestRetentionReactorPro:
    """Test Retention Reactor Pro™ functionality"""
    
    @pytest.fixture
    def retention_reactor(self):
        return RetentionReactorPro()
    
    @pytest.fixture
    def sample_customer_data(self):
        return CustomerData(
            customer_id="test_customer_123",
            days_since_last_purchase=45,
            total_purchases=15,
            average_order_value=125.50,
            total_spent=1882.50,
            email_engagement_rate=65.0,
            support_tickets=2,
            last_login_days=7,
            subscription_tier="premium",
            geographic_region="US"
        )
    
    @pytest.mark.asyncio
    async def test_initialize_model(self, retention_reactor):
        """Test model initialization"""
        await retention_reactor.initialize_model()
        assert retention_reactor.model is not None
        assert retention_reactor.scaler is not None
    
    @pytest.mark.asyncio
    async def test_predict_churn_low_risk(self, retention_reactor):
        """Test churn prediction for low-risk customer"""
        # Customer with good engagement and recent purchases
        customer = CustomerData(
            customer_id="low_risk_customer",
            days_since_last_purchase=5,
            total_purchases=25,
            average_order_value=200.0,
            total_spent=5000.0,
            email_engagement_rate=85.0,
            support_tickets=0,
            last_login_days=1,
            subscription_tier="premium",
            geographic_region="US"
        )
        
        await retention_reactor.initialize_model()
        prediction = await retention_reactor.predict_churn(customer)
        
        assert prediction.customer_id == "low_risk_customer"
        assert prediction.risk_level in ["low", "medium"]
        assert 0.0 <= prediction.churn_probability <= 1.0
        assert prediction.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_predict_churn_high_risk(self, retention_reactor):
        """Test churn prediction for high-risk customer"""
        # Customer with poor engagement and old purchases
        customer = CustomerData(
            customer_id="high_risk_customer",
            days_since_last_purchase=180,
            total_purchases=2,
            average_order_value=50.0,
            total_spent=100.0,
            email_engagement_rate=10.0,
            support_tickets=5,
            last_login_days=90,
            subscription_tier="basic",
            geographic_region="US"
        )
        
        await retention_reactor.initialize_model()
        prediction = await retention_reactor.predict_churn(customer)
        
        assert prediction.customer_id == "high_risk_customer"
        assert prediction.risk_level in ["high", "critical"]
        assert len(prediction.recommended_actions) > 0
    
    @pytest.mark.asyncio
    async def test_fallback_retention_actions(self, retention_reactor, sample_customer_data):
        """Test fallback retention action generation"""
        actions = retention_reactor._fallback_retention_actions(sample_customer_data, 0.85)
        
        assert len(actions) > 0
        assert all("action_type" in action for action in actions)
        assert all("priority" in action for action in actions)
        assert all("estimated_impact" in action for action in actions)

class TestEngageSenseUltra:
    """Test EngageSense Ultra™ functionality"""
    
    @pytest.fixture
    def engage_sense(self):
        return EngageSenseUltra()
    
    @pytest.fixture
    def sample_behavior_data(self):
        return CustomerBehavior(
            customer_id="test_customer_456",
            page_views=45,
            session_duration_avg=8.5,
            bounce_rate=35.0,
            email_opens=12,
            email_clicks=4,
            social_shares=2,
            product_views=25,
            cart_additions=5,
            purchase_frequency=2.5,
            support_interactions=1,
            feature_usage_score=75.0,
            mobile_vs_desktop_ratio=0.6
        )
    
    @pytest.mark.asyncio
    async def test_calculate_category_scores(self, engage_sense, sample_behavior_data):
        """Test category score calculation"""
        scores = engage_sense._calculate_category_scores(sample_behavior_data)
        
        assert "engagement" in scores
        assert "purchase_intent" in scores
        assert "loyalty" in scores
        assert "digital_adoption" in scores
        
        # All scores should be between 0 and 100
        for score in scores.values():
            assert 0 <= score <= 100
    
    @pytest.mark.asyncio
    async def test_calculate_overall_score(self, engage_sense):
        """Test overall score calculation"""
        category_scores = {
            "engagement": 75.0,
            "purchase_intent": 80.0,
            "loyalty": 70.0,
            "digital_adoption": 65.0
        }
        
        overall_score = engage_sense._calculate_overall_score(category_scores)
        
        # Should be weighted average
        expected = (75.0 * 0.3) + (80.0 * 0.35) + (70.0 * 0.25) + (65.0 * 0.1)
        assert abs(overall_score - expected) < 0.01
    
    @pytest.mark.asyncio
    async def test_determine_segment_champion(self, engage_sense):
        """Test segment determination for champion customer"""
        behavior = CustomerBehavior(
            customer_id="champion_customer",
            page_views=100,
            session_duration_avg=15.0,
            bounce_rate=20.0,
            email_opens=25,
            email_clicks=15,
            social_shares=10,
            product_views=50,
            cart_additions=20,
            purchase_frequency=5.0,
            support_interactions=0,
            feature_usage_score=95.0,
            mobile_vs_desktop_ratio=0.7
        )
        
        category_scores = engage_sense._calculate_category_scores(behavior)
        segment = await engage_sense._determine_segment(behavior, category_scores)
        
        assert segment == "champion"
    
    @pytest.mark.asyncio
    async def test_determine_segment_hibernating(self, engage_sense):
        """Test segment determination for hibernating customer"""
        behavior = CustomerBehavior(
            customer_id="hibernating_customer",
            page_views=5,
            session_duration_avg=1.0,
            bounce_rate=80.0,
            email_opens=1,
            email_clicks=0,
            social_shares=0,
            product_views=2,
            cart_additions=0,
            purchase_frequency=0.1,
            support_interactions=0,
            feature_usage_score=10.0,
            mobile_vs_desktop_ratio=0.3
        )
        
        category_scores = engage_sense._calculate_category_scores(behavior)
        segment = await engage_sense._determine_segment(behavior, category_scores)
        
        assert segment == "hibernating"
    
    @pytest.mark.asyncio
    async def test_analyze_trend_improving(self, engage_sense, sample_behavior_data):
        """Test trend analysis for improving customer"""
        # Create historical data showing improvement
        historical_data = [
            CustomerBehavior(
                customer_id="test_customer_456",
                page_views=20,
                session_duration_avg=4.0,
                bounce_rate=60.0,
                email_opens=5,
                email_clicks=1,
                social_shares=0,
                product_views=10,
                cart_additions=1,
                purchase_frequency=1.0,
                support_interactions=2,
                feature_usage_score=40.0,
                mobile_vs_desktop_ratio=0.4
            ),
            CustomerBehavior(
                customer_id="test_customer_456",
                page_views=30,
                session_duration_avg=6.0,
                bounce_rate=45.0,
                email_opens=8,
                email_clicks=2,
                social_shares=1,
                product_views=18,
                cart_additions=3,
                purchase_frequency=1.8,
                support_interactions=1,
                feature_usage_score=60.0,
                mobile_vs_desktop_ratio=0.5
            )
        ]
        
        trend = engage_sense._analyze_trend(sample_behavior_data, historical_data)
        assert trend == "improving"
    
    @pytest.mark.asyncio
    async def test_fallback_personalization_profile(self, engage_sense):
        """Test fallback personalization profile generation"""
        profile = engage_sense._fallback_personalization_profile("champion", {
            "engagement": 90.0,
            "purchase_intent": 85.0,
            "loyalty": 95.0,
            "digital_adoption": 80.0
        })
        
        assert "communication_channels" in profile
        assert "content_preferences" in profile
        assert "optimal_timing" in profile
        assert "messaging_tone" in profile
        assert "product_interests" in profile
        assert "personalization_tags" in profile
        
        # Champion should have premium preferences
        assert "vip" in profile["personalization_tags"]
        assert "email" in profile["communication_channels"]

class TestIntegration:
    """Integration tests for AI agents working together"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_customer_analysis(self):
        """Test complete customer analysis pipeline"""
        # Initialize all agents
        roi_engine = ROIEngineX()
        retention_reactor = RetentionReactorPro()
        engage_sense = EngageSenseUltra()
        
        await retention_reactor.initialize_model()
        await engage_sense.initialize_models()
        
        # Sample customer data
        customer_data = CustomerData(
            customer_id="integration_test_customer",
            days_since_last_purchase=30,
            total_purchases=10,
            average_order_value=150.0,
            total_spent=1500.0,
            email_engagement_rate=70.0,
            support_tickets=1,
            last_login_days=5,
            subscription_tier="premium",
            geographic_region="US"
        )
        
        behavior_data = CustomerBehavior(
            customer_id="integration_test_customer",
            page_views=40,
            session_duration_avg=7.0,
            bounce_rate=40.0,
            email_opens=10,
            email_clicks=3,
            social_shares=1,
            product_views=20,
            cart_additions=4,
            purchase_frequency=2.0,
            support_interactions=1,
            feature_usage_score=70.0,
            mobile_vs_desktop_ratio=0.6
        )
        
        # Run analyses
        churn_prediction = await retention_reactor.predict_churn(customer_data)
        engagement_score = await engage_sense.score_customer_engagement(behavior_data)
        
        # Verify results are consistent
        assert churn_prediction.customer_id == engagement_score.customer_id
        
        # High engagement should correlate with low churn risk
        if engagement_score.overall_score > 70:
            assert churn_prediction.risk_level in ["low", "medium"]
        
        # Both should provide actionable recommendations
        assert len(churn_prediction.recommended_actions) > 0
        assert len(engagement_score.recommended_content) > 0
