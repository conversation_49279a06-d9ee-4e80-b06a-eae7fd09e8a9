"""
Meta/Facebook Ads API Connector with OAuth2 authentication, rate limiting, and data validation
"""
import asyncio
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import structlog
from pydantic import BaseModel, Field, validator
from facebook_business.api import FacebookAdsApi
from facebook_business.adobjects.adaccount import AdAccount
from facebook_business.adobjects.campaign import Campaign
from facebook_business.adobjects.adsinsights import AdsInsights
from facebook_business.exceptions import FacebookRequestError
import httpx

from apps.core.config import settings
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class MetaAdsMetrics(BaseModel):
    """Unified output format for Meta Ads data"""
    campaign_id: str
    campaign_name: str
    date: datetime
    impressions: int = 0
    clicks: int = 0
    conversions: float = 0.0
    spend: float = 0.0
    
    # Calculated fields
    ctr: float = Field(default=0.0)  # Click-through rate
    cpc: float = Field(default=0.0)  # Cost per click
    conversion_rate: float = Field(default=0.0)
    
    @validator('ctr', always=True)
    def calculate_ctr(cls, v, values):
        """Calculate click-through rate"""
        impressions = values.get('impressions', 0)
        clicks = values.get('clicks', 0)
        return (clicks / impressions * 100) if impressions > 0 else 0.0
    
    @validator('cpc', always=True)
    def calculate_cpc(cls, v, values):
        """Calculate cost per click"""
        clicks = values.get('clicks', 0)
        spend = values.get('spend', 0)
        return spend / clicks if clicks > 0 else 0.0
    
    @validator('conversion_rate', always=True)
    def calculate_conversion_rate(cls, v, values):
        """Calculate conversion rate"""
        clicks = values.get('clicks', 0)
        conversions = values.get('conversions', 0)
        return (conversions / clicks * 100) if clicks > 0 else 0.0

class MetaAdsConnector:
    """Meta/Facebook Ads API connector with OAuth2 and rate limiting"""
    
    def __init__(self, ad_account_id: str):
        self.ad_account_id = ad_account_id
        if not self.ad_account_id.startswith('act_'):
            self.ad_account_id = f"act_{ad_account_id}"
        
        self.api = None
        self.ad_account = None
        self._rate_limit_delay = 1.0  # Start with 1 second delay
        self._last_request_time = 0
        
    async def authenticate(self) -> bool:
        """Authenticate with Meta Ads API using access token"""
        try:
            # Initialize Facebook Ads API
            FacebookAdsApi.init(
                app_id=settings.META_APP_ID,
                app_secret=settings.META_APP_SECRET,
                access_token=settings.META_ACCESS_TOKEN
            )
            
            self.api = FacebookAdsApi.get_default_api()
            self.ad_account = AdAccount(self.ad_account_id)
            
            # Test the connection
            account_info = self.ad_account.api_get(fields=['name', 'account_status'])
            
            logger.info(
                "Meta Ads authentication successful",
                ad_account_id=self.ad_account_id,
                account_name=account_info.get('name')
            )
            return True
            
        except Exception as e:
            logger.error("Meta Ads authentication failed", error=str(e))
            return False
    
    async def _rate_limit(self):
        """Implement rate limiting with exponential backoff"""
        current_time = time.time()
        time_since_last_request = current_time - self._last_request_time
        
        if time_since_last_request < self._rate_limit_delay:
            sleep_time = self._rate_limit_delay - time_since_last_request
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def get_campaign_metrics(
        self, 
        start_date: datetime, 
        end_date: datetime,
        campaign_ids: Optional[List[str]] = None
    ) -> List[MetaAdsMetrics]:
        """
        Fetch campaign metrics with rate limiting and retry logic
        """
        if not self.api:
            if not await self.authenticate():
                raise Exception("Failed to authenticate with Meta Ads API")
        
        await self._rate_limit()
        
        try:
            # Define fields to fetch
            fields = [
                'campaign_id',
                'campaign_name',
                'date_start',
                'impressions',
                'clicks',
                'actions',  # Contains conversions
                'spend'
            ]
            
            # Define parameters
            params = {
                'time_range': {
                    'since': start_date.strftime('%Y-%m-%d'),
                    'until': end_date.strftime('%Y-%m-%d')
                },
                'level': 'campaign',
                'breakdowns': ['date_start']
            }
            
            # Add campaign filter if specified
            if campaign_ids:
                params['filtering'] = [
                    {
                        'field': 'campaign.id',
                        'operator': 'IN',
                        'value': campaign_ids
                    }
                ]
            
            # Fetch insights
            insights = self.ad_account.get_insights(
                fields=fields,
                params=params
            )
            
            # Process results
            metrics_list = []
            for insight in insights:
                # Extract conversions from actions
                conversions = 0.0
                if 'actions' in insight:
                    for action in insight['actions']:
                        if action['action_type'] in ['purchase', 'complete_registration', 'lead']:
                            conversions += float(action['value'])
                
                metrics = MetaAdsMetrics(
                    campaign_id=insight['campaign_id'],
                    campaign_name=insight['campaign_name'],
                    date=datetime.strptime(insight['date_start'], '%Y-%m-%d'),
                    impressions=int(insight.get('impressions', 0)),
                    clicks=int(insight.get('clicks', 0)),
                    conversions=conversions,
                    spend=float(insight.get('spend', 0))
                )
                metrics_list.append(metrics)
            
            logger.info(
                "Successfully fetched Meta Ads metrics",
                ad_account_id=self.ad_account_id,
                metrics_count=len(metrics_list)
            )
            
            # Reset rate limit delay on success
            self._rate_limit_delay = 1.0
            
            return metrics_list
            
        except FacebookRequestError as ex:
            logger.error(
                "Meta Ads API error",
                ad_account_id=self.ad_account_id,
                error=str(ex)
            )
            
            # Increase rate limit delay on error
            self._rate_limit_delay = min(self._rate_limit_delay * 2, 60.0)
            raise
        
        except Exception as e:
            logger.error(
                "Unexpected error fetching Meta Ads metrics",
                ad_account_id=self.ad_account_id,
                error=str(e)
            )
            raise
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def update_campaign_budget(
        self, 
        campaign_id: str, 
        budget_adjustment: float
    ) -> bool:
        """
        Update campaign budget with the specified adjustment
        """
        if not self.api:
            if not await self.authenticate():
                raise Exception("Failed to authenticate with Meta Ads API")
        
        await self._rate_limit()
        
        try:
            campaign = Campaign(campaign_id)
            
            # Get current budget
            current_campaign = campaign.api_get(fields=['daily_budget'])
            current_budget = float(current_campaign.get('daily_budget', 0))
            
            # Calculate new budget
            new_budget = current_budget * (1 + budget_adjustment)
            
            # Update campaign
            campaign.api_update(fields={
                'daily_budget': int(new_budget * 100)  # Meta expects budget in cents
            })
            
            logger.info(
                "Successfully updated campaign budget",
                ad_account_id=self.ad_account_id,
                campaign_id=campaign_id,
                old_budget=current_budget,
                new_budget=new_budget,
                adjustment=budget_adjustment
            )
            
            return True
            
        except FacebookRequestError as ex:
            logger.error(
                "Failed to update campaign budget",
                ad_account_id=self.ad_account_id,
                campaign_id=campaign_id,
                error=str(ex)
            )
            raise
        
        except Exception as e:
            logger.error(
                "Unexpected error updating campaign budget",
                ad_account_id=self.ad_account_id,
                campaign_id=campaign_id,
                error=str(e)
            )
            raise
