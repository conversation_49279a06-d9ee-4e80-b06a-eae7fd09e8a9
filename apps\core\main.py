"""
Omnify Marketing Cloud - Main FastAPI Application
"""
from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON>earer
from contextlib import asynccontextmanager
import structlog
from prometheus_client import make_asgi_app
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

from apps.core.config import settings
from apps.core.database import init_db
from apps.core.routers import (
    auth_router,
    campaigns_router,
    analytics_router,
    ai_agents_router,
    webhooks_router
)
from apps.core.routers.dashboard import router as dashboard_router
from apps.core.routers.onboarding import router as onboarding_router
from apps.core.routers.system import router as system_router
from apps.core.routers.campaigns_advanced import router as campaigns_router
from apps.core.routers.billing import router as billing_router
from apps.core.routers.integration_wizard import router as integration_wizard_router
from apps.auth.enhanced_router import router as enhanced_auth_router
from apps.core.demo_dashboard import router as demo_router
from apps.core.websocket_manager import connection_manager, start_background_tasks
from lib.utils.middleware import RateLimitMiddleware, LoggingMiddleware

# Configure structured logging
logger = structlog.get_logger()

# Initialize Sentry for error tracking
if settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        integrations=[FastApiIntegration(auto_enabling=True)],
        traces_sample_rate=0.1,
        environment=settings.ENVIRONMENT,
    )

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Omnify Marketing Cloud API")
    await init_db()
    yield
    # Shutdown
    logger.info("Shutting down Omnify Marketing Cloud API")

# Create FastAPI application
app = FastAPI(
    title="Omnify Marketing Cloud API",
    description="AI-native SaaS platform for mid-market marketing automation",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Security
security = HTTPBearer()

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.DEBUG else ["https://app.omnify.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["api.omnify.com", "localhost"]
)

app.add_middleware(RateLimitMiddleware)
app.add_middleware(LoggingMiddleware)

# Include routers
app.include_router(enhanced_auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(campaigns_router, prefix="/api/v1/campaigns", tags=["Campaigns"])
app.include_router(analytics_router, prefix="/api/v1/analytics", tags=["Analytics"])
app.include_router(ai_agents_router, prefix="/api/v1/agents", tags=["AI Agents"])
app.include_router(webhooks_router, prefix="/api/v1/webhooks", tags=["Webhooks"])
app.include_router(dashboard_router, prefix="/api/v1/dashboard", tags=["Dashboard"])
app.include_router(onboarding_router, prefix="/api/v1/onboarding", tags=["Onboarding"])
app.include_router(system_router, prefix="/api/v1/system", tags=["System"])
app.include_router(billing_router, prefix="/api/v1/billing", tags=["Billing"])
app.include_router(integration_wizard_router, prefix="/api/v1/integrations", tags=["Integration Wizard"])
app.include_router(demo_router, tags=["Demo"])

# WebSocket endpoint for real-time updates
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time updates"""
    try:
        # In production, would validate user authentication
        user_id = "demo_user"  # Would get from JWT token

        await connection_manager.connect(websocket, client_id, user_id)

        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()

            # Handle ping/pong
            if data == "ping":
                await websocket.send_text("pong")

    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)
    except Exception as e:
        logger.error("WebSocket error", error=str(e))
        connection_manager.disconnect(websocket)

# Prometheus metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Omnify Marketing Cloud API",
        "version": "1.0.0",
        "status": "healthy",
        "environment": settings.ENVIRONMENT
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "database": "connected",
        "redis": "connected",
        "ai_services": "operational"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "apps.core.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
