[tool:pytest]
# Pytest configuration for Omnify Marketing Cloud
minversion = 7.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=lib
    --cov=apps
    --cov=scripts
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --tb=short
    --maxfail=5
    --durations=10
    -p no:warnings

testpaths = tests

python_files = test_*.py *_test.py

python_classes = Test*

python_functions = test_*

markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    cloud: Cloud provider tests
    security: Security tests
    compliance: Compliance tests
    performance: Performance tests
    slow: Slow running tests
    aws: AWS specific tests
    azure: Azure specific tests
    gcp: GCP specific tests
    local: Local environment tests
    smoke: Smoke tests for quick validation

filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

asyncio_mode = auto

# Test discovery patterns
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    node_modules
    .venv
    venv
    __pycache__

# Logging configuration for tests
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment for parallel execution with pytest-xdist
