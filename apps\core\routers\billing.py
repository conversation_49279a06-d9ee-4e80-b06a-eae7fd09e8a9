"""
Complete Billing and Subscription Management Router
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel
import structlog
import stripe

from apps.core.database import get_db, Client
from apps.auth.dependencies import get_current_active_user
from lib.billing.subscription_manager import billing_manager, SubscriptionPlan, SubscriptionInfo
from apps.core.config import settings

logger = structlog.get_logger()

router = APIRouter()

# Configure Stripe
stripe.api_key = getattr(settings, 'STRIPE_SECRET_KEY', 'sk_test_...')

class CreateSubscriptionRequest(BaseModel):
    """Create subscription request"""
    plan: SubscriptionPlan
    billing_cycle: str = "monthly"  # monthly or yearly
    payment_method_id: str

class UpdateSubscriptionRequest(BaseModel):
    """Update subscription request"""
    plan: SubscriptionPlan
    billing_cycle: str = "monthly"

class PaymentMethodRequest(BaseModel):
    """Payment method request"""
    payment_method_id: str
    set_as_default: bool = True

class InvoiceResponse(BaseModel):
    """Invoice response"""
    invoice_id: str
    amount: float
    currency: str
    status: str
    due_date: datetime
    paid_date: Optional[datetime]
    download_url: str

class UsageResponse(BaseModel):
    """Usage response"""
    current_usage: Dict[str, Any]
    plan_limits: Dict[str, Any]
    usage_warnings: List[str]
    billing_period: Dict[str, str]

@router.get("/subscription", response_model=SubscriptionInfo)
async def get_subscription_info(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get current subscription information"""
    
    subscription_info = await billing_manager.get_subscription_info(
        str(current_user.client_id), 
        db
    )
    
    return subscription_info

@router.get("/plans")
async def get_subscription_plans():
    """Get all available subscription plans"""
    
    plans = billing_manager.get_all_plans()
    
    return {
        "plans": [plan.dict() for plan in plans],
        "current_promotions": [
            {
                "code": "LAUNCH50",
                "description": "50% off first 3 months",
                "valid_until": "2024-12-31",
                "applicable_plans": ["starter", "professional"]
            }
        ]
    }

@router.post("/subscription/create")
async def create_subscription(
    subscription_request: CreateSubscriptionRequest,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new subscription"""
    
    try:
        # Get client
        client_result = await db.execute(
            select(Client).where(Client.id == current_user.client_id)
        )
        client = client_result.scalar_one_or_none()
        
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client not found"
            )
        
        # Create Stripe customer if doesn't exist
        if not client.stripe_customer_id:
            stripe_customer_id = await billing_manager.create_stripe_customer(
                str(client.id),
                client.email,
                client.name,
                db
            )
        else:
            stripe_customer_id = client.stripe_customer_id
        
        # Attach payment method to customer
        stripe.PaymentMethod.attach(
            subscription_request.payment_method_id,
            customer=stripe_customer_id
        )
        
        # Set as default payment method
        stripe.Customer.modify(
            stripe_customer_id,
            invoice_settings={
                'default_payment_method': subscription_request.payment_method_id
            }
        )
        
        # Create subscription
        subscription_id = await billing_manager.create_subscription(
            str(client.id),
            subscription_request.plan,
            subscription_request.billing_cycle,
            db
        )
        
        logger.info(
            "Subscription created successfully",
            client_id=client.id,
            plan=subscription_request.plan.value,
            subscription_id=subscription_id
        )
        
        return {
            "status": "success",
            "subscription_id": subscription_id,
            "message": "Subscription created successfully"
        }
        
    except stripe.error.StripeError as e:
        logger.error("Stripe error during subscription creation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment error: {str(e)}"
        )
    except Exception as e:
        logger.error("Subscription creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create subscription"
        )

@router.put("/subscription/update")
async def update_subscription(
    update_request: UpdateSubscriptionRequest,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update existing subscription"""
    
    try:
        # Get client
        client_result = await db.execute(
            select(Client).where(Client.id == current_user.client_id)
        )
        client = client_result.scalar_one_or_none()
        
        if not client or not client.stripe_subscription_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active subscription found"
            )
        
        # Get new plan configuration
        plan_config = billing_manager.plan_configs[update_request.plan]
        
        # Select price ID based on billing cycle
        price_id = (
            plan_config.stripe_price_id_yearly 
            if update_request.billing_cycle == "yearly" 
            else plan_config.stripe_price_id_monthly
        )
        
        # Update subscription in Stripe
        stripe.Subscription.modify(
            client.stripe_subscription_id,
            items=[{
                'id': stripe.Subscription.retrieve(client.stripe_subscription_id)['items']['data'][0]['id'],
                'price': price_id,
            }],
            proration_behavior='create_prorations'
        )
        
        # Update in database
        client.subscription_plan = update_request.plan.value
        await db.commit()
        
        logger.info(
            "Subscription updated successfully",
            client_id=client.id,
            new_plan=update_request.plan.value
        )
        
        return {
            "status": "success",
            "message": "Subscription updated successfully",
            "new_plan": update_request.plan.value
        }
        
    except stripe.error.StripeError as e:
        logger.error("Stripe error during subscription update", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment error: {str(e)}"
        )

@router.post("/subscription/cancel")
async def cancel_subscription(
    immediate: bool = False,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Cancel subscription"""
    
    success = await billing_manager.cancel_subscription(
        str(current_user.client_id),
        immediate,
        db
    )
    
    if success:
        return {
            "status": "success",
            "message": "Subscription cancelled" if immediate else "Subscription will cancel at period end"
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to cancel subscription"
        )

@router.get("/usage", response_model=UsageResponse)
async def get_usage_information(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get current usage information"""
    
    subscription_info = await billing_manager.get_subscription_info(
        str(current_user.client_id),
        db
    )
    
    return UsageResponse(
        current_usage={
            "campaigns": subscription_info.usage.campaigns_count,
            "monthly_spend": subscription_info.usage.monthly_spend,
            "customers": subscription_info.usage.customers_count,
            "ai_decisions": subscription_info.usage.ai_decisions_this_month,
            "email_sends": subscription_info.usage.email_sends_this_month
        },
        plan_limits={
            "max_campaigns": subscription_info.plan_config.features.max_campaigns,
            "max_monthly_spend": subscription_info.plan_config.features.max_monthly_spend,
            "max_customers": subscription_info.plan_config.features.max_customers,
            "max_ai_decisions": subscription_info.plan_config.features.ai_decisions_per_month,
            "max_email_sends": subscription_info.plan_config.features.email_sends_per_month
        },
        usage_warnings=subscription_info.usage_warnings,
        billing_period={
            "start": subscription_info.current_period_start.isoformat(),
            "end": subscription_info.current_period_end.isoformat()
        }
    )

@router.get("/invoices", response_model=List[InvoiceResponse])
async def get_invoices(
    limit: int = 10,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get billing invoices"""
    
    # Get client
    client_result = await db.execute(
        select(Client).where(Client.id == current_user.client_id)
    )
    client = client_result.scalar_one_or_none()
    
    if not client or not client.stripe_customer_id:
        return []
    
    try:
        # Get invoices from Stripe
        invoices = stripe.Invoice.list(
            customer=client.stripe_customer_id,
            limit=limit
        )
        
        invoice_responses = []
        for invoice in invoices.data:
            invoice_responses.append(InvoiceResponse(
                invoice_id=invoice.id,
                amount=invoice.amount_paid / 100,  # Convert from cents
                currency=invoice.currency.upper(),
                status=invoice.status,
                due_date=datetime.fromtimestamp(invoice.due_date) if invoice.due_date else datetime.utcnow(),
                paid_date=datetime.fromtimestamp(invoice.status_transitions.paid_at) if invoice.status_transitions.paid_at else None,
                download_url=invoice.invoice_pdf or ""
            ))
        
        return invoice_responses
        
    except stripe.error.StripeError as e:
        logger.error("Failed to retrieve invoices", error=str(e))
        return []

@router.post("/payment-methods")
async def add_payment_method(
    payment_method_request: PaymentMethodRequest,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Add payment method"""
    
    # Get client
    client_result = await db.execute(
        select(Client).where(Client.id == current_user.client_id)
    )
    client = client_result.scalar_one_or_none()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    try:
        # Create Stripe customer if doesn't exist
        if not client.stripe_customer_id:
            stripe_customer_id = await billing_manager.create_stripe_customer(
                str(client.id),
                client.email,
                client.name,
                db
            )
        else:
            stripe_customer_id = client.stripe_customer_id
        
        # Attach payment method
        stripe.PaymentMethod.attach(
            payment_method_request.payment_method_id,
            customer=stripe_customer_id
        )
        
        # Set as default if requested
        if payment_method_request.set_as_default:
            stripe.Customer.modify(
                stripe_customer_id,
                invoice_settings={
                    'default_payment_method': payment_method_request.payment_method_id
                }
            )
        
        return {
            "status": "success",
            "message": "Payment method added successfully"
        }
        
    except stripe.error.StripeError as e:
        logger.error("Failed to add payment method", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment error: {str(e)}"
        )

@router.post("/webhooks/stripe")
async def stripe_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Handle Stripe webhooks"""
    
    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid payload")
    except stripe.error.SignatureVerificationError:
        raise HTTPException(status_code=400, detail="Invalid signature")
    
    # Handle the event
    await billing_manager.handle_stripe_webhook(event, db)
    
    return {"status": "success"}

@router.get("/billing-portal")
async def create_billing_portal_session(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create Stripe billing portal session"""
    
    # Get client
    client_result = await db.execute(
        select(Client).where(Client.id == current_user.client_id)
    )
    client = client_result.scalar_one_or_none()
    
    if not client or not client.stripe_customer_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No billing account found"
        )
    
    try:
        session = stripe.billing_portal.Session.create(
            customer=client.stripe_customer_id,
            return_url=f"{settings.FRONTEND_URL}/billing"
        )
        
        return {"url": session.url}
        
    except stripe.error.StripeError as e:
        logger.error("Failed to create billing portal session", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create billing portal session"
        )
