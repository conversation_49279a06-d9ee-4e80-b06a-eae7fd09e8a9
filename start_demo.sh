#!/bin/bash
# Omnify Marketing Cloud Demo Launcher for macOS/Linux
# Universal cross-platform demo with automatic dependency installation

echo "🎯 OMNIFY MARKETING CLOUD - UNIVERSAL DEMO"
echo "============================================================"
echo "🖥️  Platform: $(uname -s)"
echo "🐍 Python: $(python3 --version 2>/dev/null || python --version)"
echo ""

# Check if Python is available
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ Python is not installed or not in PATH"
    echo "Please install Python 3.8+ and try again"
    exit 1
fi

echo "✅ Python detected"
echo ""

# Check if pip is available
if ! $PYTHON_CMD -m pip --version &> /dev/null; then
    echo "❌ pip is not available"
    echo "Please install pip and try again"
    exit 1
fi

echo "📦 Installing/updating dependencies..."
$PYTHON_CMD -m pip install --upgrade pip --quiet
$PYTHON_CMD -m pip install fastapi uvicorn[standard] --quiet

echo ""
echo "🚀 Starting Omnify Demo..."
echo ""
echo "📍 Demo will auto-detect available port and open in browser"
echo "💡 Press Ctrl+C to stop the demo"
echo ""

# Start the demo
$PYTHON_CMD omnify_demo.py

echo ""
echo "👋 Demo stopped. Thank you for trying Omnify Marketing Cloud!"
