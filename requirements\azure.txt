# Azure-specific requirements for Omnify Marketing Cloud
# Optimized for Azure OpenAI Accelerator variant

# Core application dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database (Azure SQL)
pyodbc==5.0.1
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1
aioodbc==0.4.0

# Cache and messaging
redis[hiredis]==5.0.1
celery[redis]==5.3.4

# Azure SDK and services
azure-identity==1.15.0
azure-mgmt-resource==23.0.1
azure-mgmt-containerinstance==10.1.0
azure-mgmt-sql==3.0.1
azure-mgmt-redis==14.0.0
azure-mgmt-cognitiveservices==13.5.0
azure-mgmt-containerregistry==10.2.0
azure-storage-blob==12.19.0
azure-keyvault-secrets==4.7.0

# Azure AI services
openai==1.3.8  # For Azure OpenAI Service
azure-cognitiveservices-language-textanalytics==5.2.0
azure-ai-textanalytics==5.3.0
azure-cognitiveservices-vision-computervision==0.9.0

# Machine Learning
numpy==1.24.4
scipy==1.11.4
scikit-learn==1.3.2
pandas==2.1.4

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
msal==1.25.0  # Microsoft Authentication Library

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0
azure-monitor-opentelemetry==1.1.1

# Data validation and serialization
marshmallow==3.20.1
marshmallow-dataclass==8.6.0

# Date and time
python-dateutil==2.8.2
pytz==2023.3

# Environment and configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Testing (for development)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Performance optimization
orjson==3.9.10
ujson==5.8.0

# Image processing (for marketing assets)
Pillow==10.1.0

# Email
emails==0.6.0

# Stripe for billing
stripe==7.8.0

# WebSocket support
websockets==12.0

# Azure-specific integrations
azure-functions==1.18.0
azure-servicebus==7.11.4
azure-eventhub==5.11.4

# Office 365 integration
msgraph-core==0.2.2
msgraph-sdk==1.0.0

# Power Platform integration
powerapps-client==1.0.0

# Additional Azure services
azure-cosmos==4.5.1
azure-search-documents==11.4.0
azure-communication-email==1.0.0
