@echo off
echo.
echo 🎯 OMNIFY MARKETING CLOUD - DOCKER DEMO STARTER
echo ============================================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running or not installed
    echo Please start Docker Desktop and try again
    pause
    exit /b 1
)

echo ✅ Docker detected
echo.

echo 🚀 Starting Omnify Demo with Docker...
echo.

REM Start essential services only
echo 📦 Starting PostgreSQL and Redis...
docker-compose -f docker-compose.demo.yml up -d postgres redis

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check if services are healthy
echo 🔍 Checking service health...
docker-compose -f docker-compose.demo.yml ps

echo.
echo 🌐 Starting Omnify API server...
echo.

REM Set environment variables for Docker connection
set DATABASE_URL=postgresql+asyncpg://omnify_user:omnify_password@localhost:5432/omnify_db
set REDIS_URL=redis://localhost:6379/0
set ENVIRONMENT=demo
set DEBUG=true
set DEMO_MODE=true
set CLOUD_VARIANT=local

echo 📍 Demo will be available at:
echo    • Demo Dashboard: http://localhost:8000/demo
echo    • API Documentation: http://localhost:8000/docs
echo    • Health Check: http://localhost:8000/health
echo.
echo 💡 Press Ctrl+C to stop the demo
echo.

REM Start the API server
python -m uvicorn apps.core.main:app --host 0.0.0.0 --port 8000 --reload

echo.
echo 🛑 Stopping Docker services...
docker-compose -f docker-compose.demo.yml down

echo.
echo 👋 Demo stopped. Press any key to exit...
pause >nul
