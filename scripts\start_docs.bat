@echo off
REM Omnify Marketing Cloud - Documentation Server Startup Script (Windows)
REM Starts the documentation server with all dependencies

echo 🚀 Starting Omnify Marketing Cloud Documentation Server...
echo ============================================================

REM Check if we're in the right directory
if not exist "docs\README.md" (
    echo ❌ Error: Please run this script from the project root directory
    echo    Expected to find docs\README.md
    pause
    exit /b 1
)

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python is installed

REM Install documentation dependencies if needed
echo 📦 Installing documentation dependencies...

REM Check and install markdown
python -c "import markdown" >nul 2>&1
if errorlevel 1 (
    echo Installing markdown...
    pip install markdown
)

REM Check and install fastapi
python -c "import fastapi" >nul 2>&1
if errorlevel 1 (
    echo Installing FastAPI and dependencies...
    pip install fastapi uvicorn jinja2 python-multipart
)

REM Check and install pygments
python -c "import pygments" >nul 2>&1
if errorlevel 1 (
    echo Installing Pygments for syntax highlighting...
    pip install pygments
)

echo ✅ Dependencies installed

REM Create docs directory structure if it doesn't exist
if not exist "docs\static" mkdir docs\static
if not exist "docs\templates" mkdir docs\templates

echo 📚 Checking documentation files...

if exist "docs\README.md" (
    echo    ✅ docs\README.md
) else (
    echo    ❌ docs\README.md (missing)
)

if exist "docs\quick-start.md" (
    echo    ✅ docs\quick-start.md
) else (
    echo    ❌ docs\quick-start.md (missing)
)

if exist "docs\mvp-status.md" (
    echo    ✅ docs\mvp-status.md
) else (
    echo    ❌ docs\mvp-status.md (missing)
)

if exist "docs\api-reference.md" (
    echo    ✅ docs\api-reference.md
) else (
    echo    ❌ docs\api-reference.md (missing)
)

if exist "docs\architecture.md" (
    echo    ✅ docs\architecture.md
) else (
    echo    ❌ docs\architecture.md (missing)
)

if exist "docs\index.html" (
    echo    ✅ docs\index.html
) else (
    echo    ❌ docs\index.html (missing)
)

echo.
echo 🌐 Starting documentation server...
echo    URL: http://localhost:8080
echo    API: http://localhost:8080/api/docs
echo    Search: http://localhost:8080/api/search?q=query
echo.
echo 📖 Available documentation:
echo    • Quick Start Guide: http://localhost:8080/docs/quick-start
echo    • MVP Status: http://localhost:8080/docs/mvp-status
echo    • API Reference: http://localhost:8080/docs/api-reference
echo    • Architecture: http://localhost:8080/docs/architecture
echo.
echo 🔍 Features:
echo    • Real-time markdown rendering
echo    • Full-text search across all docs
echo    • Table of contents generation
echo    • Syntax highlighting for code blocks
echo    • Responsive design
echo.
echo Press Ctrl+C to stop the server
echo ============================================================

REM Start the documentation server
python scripts\serve_docs.py
