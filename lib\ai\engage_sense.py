"""
EngageSense Ultra™ - Customer Behavior Scoring and Personalized Outreach at Scale
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import structlog
from pydantic import BaseModel, Field
import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import openai

from apps.core.config import settings
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class CustomerBehavior(BaseModel):
    """Customer behavior data for scoring"""
    customer_id: str
    page_views: int
    session_duration_avg: float  # minutes
    bounce_rate: float
    email_opens: int
    email_clicks: int
    social_shares: int
    product_views: int
    cart_additions: int
    purchase_frequency: float  # purchases per month
    support_interactions: int
    feature_usage_score: float  # 0-100
    mobile_vs_desktop_ratio: float

class EngagementScore(BaseModel):
    """Customer engagement score result"""
    customer_id: str
    overall_score: float  # 0-100
    category_scores: Dict[str, float]  # engagement, purchase_intent, loyalty, etc.
    segment: str  # "champion", "loyal", "potential_loyalist", "at_risk", "hibernating"
    trend: str  # "improving", "stable", "declining"
    personalization_profile: Dict[str, Any]
    recommended_content: List[Dict[str, Any]]
    next_best_action: Dict[str, Any]

class PersonalizationRule(BaseModel):
    """Personalization rule for content/offers"""
    rule_id: str
    segment: str
    condition: Dict[str, Any]
    content_type: str
    parameters: Dict[str, Any]
    priority: int
    success_rate: float

class EngageSenseUltra:
    """
    EngageSense Ultra™ - AI agent for customer behavior scoring and personalization
    """
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.kmeans_model = None
        self.segment_labels = {
            0: "hibernating",
            1: "at_risk", 
            2: "potential_loyalist",
            3: "loyal",
            4: "champion"
        }
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
    async def initialize_models(self):
        """Initialize customer segmentation models"""
        # In production, load pre-trained models
        # For MVP, we'll use a simple clustering approach
        self.kmeans_model = KMeans(n_clusters=5, random_state=42)
        logger.info("EngageSense Ultra models initialized")
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def score_customer_engagement(
        self, 
        behavior_data: CustomerBehavior,
        historical_data: Optional[List[CustomerBehavior]] = None
    ) -> EngagementScore:
        """
        Score customer engagement and generate personalization insights
        """
        if not self.kmeans_model:
            await self.initialize_models()
        
        # Calculate category scores
        category_scores = self._calculate_category_scores(behavior_data)
        
        # Calculate overall engagement score
        overall_score = self._calculate_overall_score(category_scores)
        
        # Determine customer segment
        segment = await self._determine_segment(behavior_data, category_scores)
        
        # Analyze trend
        trend = self._analyze_trend(behavior_data, historical_data)
        
        # Generate personalization profile
        personalization_profile = await self._generate_personalization_profile(
            behavior_data, segment, category_scores
        )
        
        # Get recommended content
        recommended_content = await self._get_recommended_content(
            behavior_data, segment, personalization_profile
        )
        
        # Determine next best action
        next_best_action = await self._determine_next_best_action(
            behavior_data, segment, category_scores
        )
        
        logger.info(
            "Customer engagement scored",
            customer_id=behavior_data.customer_id,
            overall_score=overall_score,
            segment=segment
        )
        
        return EngagementScore(
            customer_id=behavior_data.customer_id,
            overall_score=overall_score,
            category_scores=category_scores,
            segment=segment,
            trend=trend,
            personalization_profile=personalization_profile,
            recommended_content=recommended_content,
            next_best_action=next_best_action
        )
    
    def _calculate_category_scores(self, behavior_data: CustomerBehavior) -> Dict[str, float]:
        """Calculate scores for different engagement categories"""
        
        # Engagement Score (0-100)
        engagement_score = min(100, (
            behavior_data.page_views * 2 +
            behavior_data.session_duration_avg * 5 +
            (100 - behavior_data.bounce_rate) +
            behavior_data.email_opens * 3 +
            behavior_data.email_clicks * 5 +
            behavior_data.social_shares * 10
        ) / 6)
        
        # Purchase Intent Score (0-100)
        purchase_intent_score = min(100, (
            behavior_data.product_views * 3 +
            behavior_data.cart_additions * 15 +
            behavior_data.purchase_frequency * 20
        ) / 3)
        
        # Loyalty Score (0-100)
        loyalty_score = min(100, (
            behavior_data.feature_usage_score +
            (behavior_data.purchase_frequency * 10) +
            max(0, 50 - behavior_data.support_interactions * 5)  # Fewer support tickets = higher loyalty
        ) / 3)
        
        # Digital Adoption Score (0-100)
        digital_adoption_score = min(100, (
            behavior_data.feature_usage_score +
            (behavior_data.mobile_vs_desktop_ratio * 50) +  # Mobile usage indicates higher adoption
            (behavior_data.session_duration_avg * 2)
        ) / 3)
        
        return {
            "engagement": round(engagement_score, 2),
            "purchase_intent": round(purchase_intent_score, 2),
            "loyalty": round(loyalty_score, 2),
            "digital_adoption": round(digital_adoption_score, 2)
        }
    
    def _calculate_overall_score(self, category_scores: Dict[str, float]) -> float:
        """Calculate weighted overall engagement score"""
        weights = {
            "engagement": 0.3,
            "purchase_intent": 0.35,
            "loyalty": 0.25,
            "digital_adoption": 0.1
        }
        
        overall_score = sum(
            category_scores[category] * weight 
            for category, weight in weights.items()
        )
        
        return round(overall_score, 2)
    
    async def _determine_segment(
        self, 
        behavior_data: CustomerBehavior, 
        category_scores: Dict[str, float]
    ) -> str:
        """Determine customer segment based on behavior and scores"""
        
        engagement = category_scores["engagement"]
        purchase_intent = category_scores["purchase_intent"]
        loyalty = category_scores["loyalty"]
        
        # Rule-based segmentation (can be replaced with ML model)
        if engagement >= 80 and purchase_intent >= 80 and loyalty >= 80:
            return "champion"
        elif engagement >= 60 and loyalty >= 70:
            return "loyal"
        elif engagement >= 50 and purchase_intent >= 60:
            return "potential_loyalist"
        elif engagement < 40 and loyalty < 50:
            return "hibernating"
        else:
            return "at_risk"
    
    def _analyze_trend(
        self, 
        current_data: CustomerBehavior,
        historical_data: Optional[List[CustomerBehavior]]
    ) -> str:
        """Analyze engagement trend over time"""
        if not historical_data or len(historical_data) < 2:
            return "stable"
        
        # Calculate trend based on recent behavior changes
        recent_scores = []
        for data in historical_data[-3:]:  # Last 3 data points
            scores = self._calculate_category_scores(data)
            recent_scores.append(self._calculate_overall_score(scores))
        
        current_score = self._calculate_overall_score(
            self._calculate_category_scores(current_data)
        )
        recent_scores.append(current_score)
        
        # Simple trend analysis
        if len(recent_scores) >= 3:
            trend_slope = (recent_scores[-1] - recent_scores[0]) / len(recent_scores)
            if trend_slope > 5:
                return "improving"
            elif trend_slope < -5:
                return "declining"
        
        return "stable"
    
    async def _generate_personalization_profile(
        self,
        behavior_data: CustomerBehavior,
        segment: str,
        category_scores: Dict[str, float]
    ) -> Dict[str, Any]:
        """Generate AI-powered personalization profile"""
        
        prompt = f"""
        Create a personalization profile for this customer:
        
        Behavior Data:
        - Page Views: {behavior_data.page_views}
        - Avg Session Duration: {behavior_data.session_duration_avg} minutes
        - Bounce Rate: {behavior_data.bounce_rate}%
        - Email Engagement: {behavior_data.email_opens} opens, {behavior_data.email_clicks} clicks
        - Product Interest: {behavior_data.product_views} views, {behavior_data.cart_additions} cart adds
        - Purchase Frequency: {behavior_data.purchase_frequency} per month
        - Mobile vs Desktop: {behavior_data.mobile_vs_desktop_ratio}
        
        Segment: {segment}
        Category Scores: {category_scores}
        
        Generate a personalization profile with:
        1. Preferred communication channels
        2. Content preferences
        3. Optimal timing for outreach
        4. Messaging tone and style
        5. Product/service interests
        
        Respond in JSON format:
        {{
            "communication_channels": ["email", "sms", "push"],
            "content_preferences": ["educational", "promotional", "social_proof"],
            "optimal_timing": {{"day": "weekday", "time": "evening"}},
            "messaging_tone": "friendly",
            "product_interests": ["category1", "category2"],
            "personalization_tags": ["tag1", "tag2"]
        }}
        """
        
        try:
            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert in customer personalization and behavioral analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=600
            )
            
            import json
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            logger.error("Failed to generate personalization profile", error=str(e))
            return self._fallback_personalization_profile(segment, category_scores)
    
    def _fallback_personalization_profile(
        self, 
        segment: str, 
        category_scores: Dict[str, float]
    ) -> Dict[str, Any]:
        """Fallback rule-based personalization profile"""
        
        profiles = {
            "champion": {
                "communication_channels": ["email", "sms", "push"],
                "content_preferences": ["exclusive_offers", "new_features", "community"],
                "optimal_timing": {"day": "any", "time": "morning"},
                "messaging_tone": "appreciative",
                "product_interests": ["premium", "new_releases"],
                "personalization_tags": ["vip", "early_adopter"]
            },
            "loyal": {
                "communication_channels": ["email", "push"],
                "content_preferences": ["educational", "tips", "loyalty_rewards"],
                "optimal_timing": {"day": "weekday", "time": "evening"},
                "messaging_tone": "friendly",
                "product_interests": ["complementary", "upgrades"],
                "personalization_tags": ["loyal", "engaged"]
            },
            "potential_loyalist": {
                "communication_channels": ["email"],
                "content_preferences": ["educational", "social_proof", "tutorials"],
                "optimal_timing": {"day": "weekend", "time": "afternoon"},
                "messaging_tone": "helpful",
                "product_interests": ["popular", "recommended"],
                "personalization_tags": ["growing", "potential"]
            },
            "at_risk": {
                "communication_channels": ["email", "retargeting"],
                "content_preferences": ["win_back", "special_offers", "testimonials"],
                "optimal_timing": {"day": "weekday", "time": "morning"},
                "messaging_tone": "urgent_but_caring",
                "product_interests": ["discounted", "popular"],
                "personalization_tags": ["at_risk", "needs_attention"]
            },
            "hibernating": {
                "communication_channels": ["email"],
                "content_preferences": ["reactivation", "major_discounts", "whats_new"],
                "optimal_timing": {"day": "weekend", "time": "morning"},
                "messaging_tone": "welcoming_back",
                "product_interests": ["bestsellers", "heavily_discounted"],
                "personalization_tags": ["hibernating", "reactivation"]
            }
        }
        
        return profiles.get(segment, profiles["potential_loyalist"])
    
    async def _get_recommended_content(
        self,
        behavior_data: CustomerBehavior,
        segment: str,
        personalization_profile: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get AI-recommended content for the customer"""
        
        content_preferences = personalization_profile.get("content_preferences", [])
        
        # Rule-based content recommendations (can be enhanced with ML)
        recommendations = []
        
        if "educational" in content_preferences:
            recommendations.append({
                "type": "blog_post",
                "title": "How to maximize your ROI with our platform",
                "priority": 3,
                "estimated_engagement": 0.7
            })
        
        if "promotional" in content_preferences:
            recommendations.append({
                "type": "offer",
                "title": "Exclusive 20% discount for loyal customers",
                "priority": 4,
                "estimated_engagement": 0.8
            })
        
        if "social_proof" in content_preferences:
            recommendations.append({
                "type": "case_study",
                "title": "How Company X increased revenue by 150%",
                "priority": 3,
                "estimated_engagement": 0.6
            })
        
        return recommendations[:3]  # Return top 3 recommendations
    
    async def _determine_next_best_action(
        self,
        behavior_data: CustomerBehavior,
        segment: str,
        category_scores: Dict[str, float]
    ) -> Dict[str, Any]:
        """Determine the next best action for this customer"""
        
        actions = {
            "champion": {
                "action": "vip_program_invite",
                "priority": 5,
                "expected_impact": 0.9,
                "description": "Invite to VIP program with exclusive benefits"
            },
            "loyal": {
                "action": "loyalty_reward",
                "priority": 4,
                "expected_impact": 0.8,
                "description": "Send loyalty points or special discount"
            },
            "potential_loyalist": {
                "action": "educational_content",
                "priority": 3,
                "expected_impact": 0.6,
                "description": "Share educational content to increase engagement"
            },
            "at_risk": {
                "action": "retention_campaign",
                "priority": 5,
                "expected_impact": 0.7,
                "description": "Launch targeted retention campaign"
            },
            "hibernating": {
                "action": "reactivation_offer",
                "priority": 4,
                "expected_impact": 0.5,
                "description": "Send compelling reactivation offer"
            }
        }
        
        return actions.get(segment, actions["potential_loyalist"])
    
    async def execute_personalized_outreach(
        self,
        customer_id: str,
        engagement_score: EngagementScore,
        action_type: str
    ) -> Dict[str, Any]:
        """Execute personalized outreach based on engagement insights"""
        
        try:
            if action_type == "email_campaign":
                return await self._execute_personalized_email(customer_id, engagement_score)
            elif action_type == "content_recommendation":
                return await self._execute_content_recommendation(customer_id, engagement_score)
            elif action_type == "product_suggestion":
                return await self._execute_product_suggestion(customer_id, engagement_score)
            else:
                raise ValueError(f"Unknown action type: {action_type}")
                
        except Exception as e:
            logger.error(
                "Failed to execute personalized outreach",
                error=str(e),
                customer_id=customer_id,
                action_type=action_type
            )
            return {"status": "failed", "error": str(e)}
    
    async def _execute_personalized_email(
        self, 
        customer_id: str, 
        engagement_score: EngagementScore
    ) -> Dict[str, Any]:
        """Execute personalized email campaign"""
        
        profile = engagement_score.personalization_profile
        
        # Generate personalized email content
        email_content = {
            "subject": f"Personalized recommendations for you",
            "tone": profile.get("messaging_tone", "friendly"),
            "content_type": profile.get("content_preferences", ["educational"])[0],
            "timing": profile.get("optimal_timing", {"day": "weekday", "time": "morning"})
        }
        
        logger.info(
            "Personalized email executed",
            customer_id=customer_id,
            segment=engagement_score.segment,
            content_type=email_content["content_type"]
        )
        
        return {
            "status": "success",
            "channel": "email",
            "personalization_applied": True,
            "content": email_content,
            "sent_at": datetime.utcnow()
        }
    
    async def _execute_content_recommendation(
        self, 
        customer_id: str, 
        engagement_score: EngagementScore
    ) -> Dict[str, Any]:
        """Execute personalized content recommendation"""
        
        recommended_content = engagement_score.recommended_content
        
        logger.info(
            "Content recommendations executed",
            customer_id=customer_id,
            content_count=len(recommended_content)
        )
        
        return {
            "status": "success",
            "recommendations": recommended_content,
            "personalized": True,
            "delivered_at": datetime.utcnow()
        }
    
    async def _execute_product_suggestion(
        self, 
        customer_id: str, 
        engagement_score: EngagementScore
    ) -> Dict[str, Any]:
        """Execute personalized product suggestions"""
        
        profile = engagement_score.personalization_profile
        product_interests = profile.get("product_interests", [])
        
        logger.info(
            "Product suggestions executed",
            customer_id=customer_id,
            interests=product_interests
        )
        
        return {
            "status": "success",
            "suggestions": product_interests,
            "segment_based": True,
            "delivered_at": datetime.utcnow()
        }
