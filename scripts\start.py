#!/usr/bin/env python3
"""
Omnify Marketing Cloud - Startup Script
"""
import asyncio
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from apps.core.config import settings
from apps.core.database import init_db
import structlog

logger = structlog.get_logger()

async def check_dependencies():
    """Check if all required dependencies are available"""
    checks = []
    
    # Check database connection
    try:
        await init_db()
        checks.append(("Database", "✓ Connected"))
    except Exception as e:
        checks.append(("Database", f"✗ Failed: {e}"))
    
    # Check OpenAI API key
    if settings.OPENAI_API_KEY:
        checks.append(("OpenAI API", "✓ Key configured"))
    else:
        checks.append(("OpenAI API", "⚠ Key not configured"))
    
    # Check Google Ads credentials
    if all([
        settings.GOOGLE_ADS_DEVELOPER_TOKEN,
        settings.GOOGLE_ADS_CLIENT_ID,
        settings.GOOGLE_ADS_CLIENT_SECRET
    ]):
        checks.append(("Google Ads API", "✓ Credentials configured"))
    else:
        checks.append(("Google Ads API", "⚠ Credentials incomplete"))
    
    # Check Meta Ads credentials
    if all([
        settings.META_APP_ID,
        settings.META_APP_SECRET,
        settings.META_ACCESS_TOKEN
    ]):
        checks.append(("Meta Ads API", "✓ Credentials configured"))
    else:
        checks.append(("Meta Ads API", "⚠ Credentials incomplete"))
    
    return checks

def print_banner():
    """Print startup banner"""
    banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║                 🚀 OMNIFY MARKETING CLOUD 🚀                 ║
    ║                                                               ║
    ║              AI-Native SaaS for Marketing Automation          ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    
    🤖 AI Agents:
       • ROI Engine X™ - CAC Optimization
       • Retention Reactor Pro™ - Churn Prevention  
       • EngageSense Ultra™ - Personalization
    
    🔧 Tech Stack:
       • FastAPI + PostgreSQL + Redis
       • OpenAI GPT-4 + Manus RL
       • n8n Workflows + Prometheus Monitoring
    
    """
    print(banner)

async def main():
    """Main startup function"""
    print_banner()
    
    print("🔍 Checking system dependencies...\n")
    
    checks = await check_dependencies()
    
    for service, status in checks:
        print(f"   {service:<20} {status}")
    
    print("\n" + "="*60)
    
    # Check if critical services are ready
    failed_checks = [check for check in checks if "✗" in check[1]]
    
    if failed_checks:
        print("❌ Critical dependencies failed. Please fix the issues above.")
        return False
    
    warning_checks = [check for check in checks if "⚠" in check[1]]
    
    if warning_checks:
        print("⚠️  Some optional services are not configured.")
        print("   The system will start but some features may be limited.")
    
    print("\n✅ System ready to start!")
    print("\n📚 Quick Start:")
    print("   1. Copy .env.example to .env and configure your API keys")
    print("   2. Run: docker-compose up -d")
    print("   3. Run: uvicorn apps.core.main:app --reload")
    print("   4. Visit: http://localhost:8000/docs")
    
    print("\n🔗 Service URLs:")
    print("   • API Documentation: http://localhost:8000/docs")
    print("   • n8n Workflows: http://localhost:5678")
    print("   • Grafana Dashboard: http://localhost:3000")
    print("   • Prometheus Metrics: http://localhost:9090")
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Startup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        sys.exit(1)
