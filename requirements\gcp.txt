# GCP-specific requirements for Omnify Marketing Cloud
# Optimized for GCP-Vertex Analytics variant

# Core application dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database (Cloud SQL PostgreSQL)
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
cloud-sql-python-connector[asyncpg]==1.4.3

# Cache and messaging
redis[hiredis]==5.0.1
celery[redis]==5.3.4

# Google Cloud SDK and services
google-cloud-core==2.4.1
google-cloud-storage==2.10.0
google-cloud-sql==3.4.4
google-cloud-redis==2.13.1
google-cloud-run==0.10.3
google-cloud-bigquery==3.13.0
google-cloud-pubsub==2.18.4
google-cloud-aiplatform==1.38.1
google-cloud-container==2.33.0
google-cloud-monitoring==2.16.0
google-cloud-logging==3.8.0

# Vertex AI and ML
google-cloud-aiplatform==1.38.1
google-cloud-ml==0.1.0
vertexai==1.38.1
tensorflow==2.15.0
tensorflow-serving-api==2.15.0

# Machine Learning
numpy==1.24.4
scipy==1.11.4
scikit-learn==1.3.2
pandas==2.1.4
torch==2.1.2

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
google-auth==2.23.4
google-auth-oauthlib==1.1.0

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0
google-cloud-error-reporting==1.9.3

# Data validation and serialization
marshmallow==3.20.1
marshmallow-dataclass==8.6.0

# Date and time
python-dateutil==2.8.2
pytz==2023.3

# Environment and configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Testing (for development)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Performance optimization
orjson==3.9.10
ujson==5.8.0

# Image processing (for marketing assets)
Pillow==10.1.0

# Email
emails==0.6.0

# Stripe for billing
stripe==7.8.0

# WebSocket support
websockets==12.0

# BigQuery and analytics
google-cloud-bigquery-storage==2.22.0
google-cloud-datacatalog==3.16.1
google-cloud-dataflow-client==0.8.4

# Pub/Sub and messaging
google-cloud-pubsublite==1.8.3
google-cloud-tasks==2.14.2

# Additional GCP services
google-cloud-secret-manager==2.17.0
google-cloud-kms==2.19.2
google-cloud-firestore==2.13.1
google-cloud-functions==1.13.3

# Kubernetes integration
kubernetes==28.1.0
google-cloud-container==2.33.0

# Data processing
apache-beam[gcp]==2.52.0
google-cloud-dataproc==5.6.0

# Workspace integration
google-api-python-client==2.108.0
google-auth-httplib2==0.1.1
google-auth-oauthlib==1.1.0
