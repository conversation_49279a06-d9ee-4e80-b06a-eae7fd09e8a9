#!/usr/bin/env python3
"""
Simple Omnify Demo - Minimal Dependencies
Runs a basic demo server with just Python standard library + FastAPI
"""
import json
import random
import webbrowser
import time
from datetime import datetime
from typing import Dict, Any

try:
    from fastapi import FastAPI
    from fastapi.responses import HTMLResponse
    import uvicorn
except ImportError:
    print("❌ FastAPI not installed. Installing now...")
    import subprocess
    import sys
    subprocess.run([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn[standard]"])
    from fastapi import FastAPI
    from fastapi.responses import HTMLResponse
    import uvicorn

app = FastAPI(title="Omnify Marketing Cloud Demo", version="1.0.0")

def generate_demo_data() -> Dict[str, Any]:
    """Generate realistic demo data"""
    return {
        "roi_engine": {
            "active_campaigns": 12,
            "optimizations_today": random.randint(45, 78),
            "roi_improvement": round(random.uniform(156, 312), 1),
            "revenue_impact": random.randint(8400, 15600),
            "confidence_score": random.randint(85, 95)
        },
        "retention_reactor": {
            "customers_monitored": random.randint(12800, 15200),
            "at_risk_customers": random.randint(67, 89),
            "retention_rate": round(random.uniform(76, 82), 1),
            "revenue_at_risk": random.randint(89000, 125000)
        },
        "engagesense": {
            "total_customers": random.randint(14500, 16800),
            "personalizations_today": random.randint(1200, 1800),
            "engagement_improvement": round(random.uniform(320, 380), 1),
            "conversion_lift": round(random.uniform(145, 175), 1)
        },
        "platform": {
            "total_revenue_impact": random.randint(47000, 68000),
            "ai_decisions_today": random.randint(1247, 1856),
            "uptime": "99.97%",
            "last_updated": datetime.now().strftime("%H:%M:%S")
        }
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Omnify Marketing Cloud Demo",
        "version": "1.0.0",
        "status": "healthy",
        "demo_dashboard": "/demo",
        "api_docs": "/docs"
    }

@app.get("/health")
async def health():
    """Health check"""
    return {"status": "healthy", "ai_agents": "active", "demo_mode": True}

@app.get("/demo", response_class=HTMLResponse)
async def demo_dashboard():
    """Simple demo dashboard"""
    data = generate_demo_data()

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Omnify Marketing Cloud - Live Demo</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            * {{ margin: 0; padding: 0; box-sizing: border-box; }}
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh; color: #333; padding: 2rem;
            }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{
                background: rgba(255,255,255,0.95); padding: 2rem; border-radius: 12px;
                margin-bottom: 2rem; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }}
            .header h1 {{ font-size: 2.5rem; color: #2d3748; margin-bottom: 0.5rem; }}
            .header p {{ color: #718096; font-size: 1.1rem; }}
            .live-indicator {{
                display: inline-flex; align-items: center; gap: 0.5rem;
                background: #48bb78; color: white; padding: 0.5rem 1rem;
                border-radius: 20px; font-weight: 600; margin-bottom: 2rem;
            }}
            .pulse {{
                width: 8px; height: 8px; background: white; border-radius: 50%;
                animation: pulse 2s infinite;
            }}
            @keyframes pulse {{ 0%, 100% {{ opacity: 1; }} 50% {{ opacity: 0.5; }} }}
            .metrics {{
                display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem; margin-bottom: 3rem;
            }}
            .metric-card {{
                background: rgba(255,255,255,0.95); padding: 2rem; border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1); text-align: center;
            }}
            .metric-value {{
                font-size: 2.5rem; font-weight: 700; color: #4299e1; margin-bottom: 0.5rem;
            }}
            .metric-label {{ color: #718096; font-weight: 600; }}
            .agents {{
                display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 2rem;
            }}
            .agent-card {{
                background: rgba(255,255,255,0.95); padding: 2rem; border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }}
            .agent-header {{
                display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;
            }}
            .agent-icon {{
                width: 50px; height: 50px; border-radius: 50%; display: flex;
                align-items: center; justify-content: center; font-size: 1.5rem; color: white;
            }}
            .roi-icon {{ background: linear-gradient(135deg, #4299e1, #3182ce); }}
            .retention-icon {{ background: linear-gradient(135deg, #48bb78, #38a169); }}
            .engagement-icon {{ background: linear-gradient(135deg, #ed8936, #dd6b20); }}
            .agent-title {{ font-size: 1.5rem; font-weight: 700; color: #2d3748; }}
            .agent-status {{
                background: #48bb78; color: white; padding: 0.25rem 0.75rem;
                border-radius: 20px; font-size: 0.8rem; font-weight: 600;
            }}
            .agent-metrics {{
                display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-top: 1rem;
            }}
            .refresh-btn {{
                background: #4299e1; color: white; border: none; padding: 1rem 2rem;
                border-radius: 8px; font-weight: 600; cursor: pointer; font-size: 1rem;
                margin: 2rem auto; display: block; transition: background 0.2s;
            }}
            .refresh-btn:hover {{ background: #3182ce; }}
            .footer {{
                text-align: center; margin-top: 3rem; color: rgba(255,255,255,0.8);
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Omnify Marketing Cloud</h1>
                <p>AI-Native Marketing Automation Platform</p>
                <div class="live-indicator">
                    <div class="pulse"></div>
                    Live Demo - AI Agents Active
                </div>
            </div>

            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value">${data['platform']['total_revenue_impact']:,}</div>
                    <div class="metric-label">Revenue Impact Today</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data['platform']['ai_decisions_today']:,}</div>
                    <div class="metric-label">AI Decisions Made</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data['platform']['uptime']}</div>
                    <div class="metric-label">Platform Uptime</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data['platform']['last_updated']}</div>
                    <div class="metric-label">Last Updated</div>
                </div>
            </div>

            <div class="agents">
                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon roi-icon">💰</div>
                        <div>
                            <div class="agent-title">ROI Engine X™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="agent-metrics">
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['roi_engine']['roi_improvement']}%</div>
                            <div class="metric-label">ROI Improvement</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['roi_engine']['optimizations_today']}</div>
                            <div class="metric-label">Optimizations Today</div>
                        </div>
                    </div>
                    <p style="margin-top: 1rem; color: #718096;">
                        AI-powered campaign optimization with {data['roi_engine']['confidence_score']}% confidence.
                        Generated ${data['roi_engine']['revenue_impact']:,} in additional revenue today.
                    </p>
                </div>

                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon retention-icon">🔄</div>
                        <div>
                            <div class="agent-title">Retention Reactor Pro™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="agent-metrics">
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['retention_reactor']['at_risk_customers']}</div>
                            <div class="metric-label">At-Risk Customers</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['retention_reactor']['retention_rate']}%</div>
                            <div class="metric-label">Retention Rate</div>
                        </div>
                    </div>
                    <p style="margin-top: 1rem; color: #718096;">
                        Monitoring {data['retention_reactor']['customers_monitored']:,} customers.
                        ${data['retention_reactor']['revenue_at_risk']:,} in revenue at risk identified.
                    </p>
                </div>

                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon engagement-icon">👥</div>
                        <div>
                            <div class="agent-title">EngageSense Ultra™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="agent-metrics">
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['engagesense']['engagement_improvement']}%</div>
                            <div class="metric-label">Engagement Boost</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['engagesense']['personalizations_today']:,}</div>
                            <div class="metric-label">Personalizations</div>
                        </div>
                    </div>
                    <p style="margin-top: 1rem; color: #718096;">
                        Segmenting {data['engagesense']['total_customers']:,} customers in real-time.
                        {data['engagesense']['conversion_lift']}% conversion rate improvement achieved.
                    </p>
                </div>
            </div>

            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Live Data</button>

            <div class="footer">
                <p>Omnify Marketing Cloud Demo | Last Updated: {data['platform']['last_updated']}</p>
                <p>🌐 <a href="/docs" style="color: rgba(255,255,255,0.9);">API Documentation</a> |
                   ❤️ <a href="/" style="color: rgba(255,255,255,0.9);">API Status</a></p>
            </div>
        </div>

        <script>
            // Auto-refresh every 30 seconds
            setTimeout(() => location.reload(), 30000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html)

@app.get("/api/demo/data")
async def demo_data():
    """API endpoint for demo data"""
    return generate_demo_data()

def main():
    """Start the demo server"""
    # Set UTF-8 encoding for Windows console
    import sys
    if sys.platform.startswith('win'):
        import os
        os.system('chcp 65001 >nul 2>&1')  # Set console to UTF-8

    try:
        print("🎯 OMNIFY MARKETING CLOUD - SIMPLE DEMO")
        print("=" * 50)
        print("🚀 Starting demo server...")
        print("📍 Demo will be available at:")
        print("   • Demo Dashboard: http://localhost:8000/demo")
        print("   • API Docs: http://localhost:8000/docs")
        print("   • Health Check: http://localhost:8000/health")
        print()
        print("💡 Press Ctrl+C to stop")
        print("=" * 50)
    except UnicodeEncodeError:
        # Fallback for Windows console issues
        print("OMNIFY MARKETING CLOUD - SIMPLE DEMO")
        print("=" * 50)
        print("Starting demo server...")
        print("Demo will be available at:")
        print("   - Demo Dashboard: http://localhost:8000/demo")
        print("   - API Docs: http://localhost:8000/docs")
        print("   - Health Check: http://localhost:8000/health")
        print()
        print("Press Ctrl+C to stop")
        print("=" * 50)

    # Open browser after a short delay
    def open_browser():
        time.sleep(2)
        webbrowser.open("http://localhost:8000/demo")

    import threading
    threading.Thread(target=open_browser, daemon=True).start()

    # Start server
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

if __name__ == "__main__":
    main()
