#!/bin/bash

# Omnify Marketing Cloud - Frontend Setup Script
# Sets up Next.js dashboard with all required dependencies

set -e

echo "🚀 Setting up Omnify Marketing Cloud Frontend..."

# Create frontend directory
mkdir -p frontend
cd frontend

# Initialize Next.js project with TypeScript
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install additional dependencies
echo "📦 Installing dependencies..."
npm install \
  @tanstack/react-query \
  @tanstack/react-query-devtools \
  recharts \
  lucide-react \
  @radix-ui/react-alert-dialog \
  @radix-ui/react-avatar \
  @radix-ui/react-badge \
  @radix-ui/react-button \
  @radix-ui/react-card \
  @radix-ui/react-dialog \
  @radix-ui/react-dropdown-menu \
  @radix-ui/react-input \
  @radix-ui/react-label \
  @radix-ui/react-select \
  @radix-ui/react-table \
  @radix-ui/react-tabs \
  @radix-ui/react-toast \
  class-variance-authority \
  clsx \
  tailwind-merge \
  date-fns \
  axios \
  js-cookie \
  @types/js-cookie

# Install dev dependencies
npm install -D \
  @types/node \
  autoprefixer \
  postcss \
  tailwindcss

# Create environment file
cat > .env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws
NEXT_PUBLIC_APP_NAME=Omnify Marketing Cloud
EOF

# Create lib directory structure
mkdir -p src/lib src/components src/hooks src/types

# Create API client
cat > src/lib/api.ts << 'EOF'
import axios from 'axios';
import Cookies from 'js-cookie';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = Cookies.get('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      Cookies.remove('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
EOF

# Create types
cat > src/types/index.ts << 'EOF'
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  client_id?: number;
  client_name?: string;
}

export interface DashboardOverview {
  client_id: string;
  total_spend: number;
  total_revenue: number;
  current_roas: number;
  current_cac: number;
  ai_decisions_today: number;
  estimated_savings: number;
  roas_trend: 'improving' | 'stable' | 'declining';
}

export interface Campaign {
  campaign_id: string;
  name: string;
  platform: string;
  status: string;
  spend: number;
  revenue: number;
  roas: number;
  cac: number;
  target_cac: number;
  budget_utilization: number;
  last_ai_optimization: string;
  alert_level: string;
}

export interface Alert {
  alert_id: string;
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'critical' | 'emergency';
  category: string;
  created_at: string;
  status: string;
}
EOF

# Create auth hook
cat > src/hooks/useAuth.ts << 'EOF'
import { useState, useEffect } from 'react';
import { User } from '@/types';
import api from '@/lib/api';
import Cookies from 'js-cookie';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = Cookies.get('access_token');
    if (token) {
      // Verify token and get user info
      api.get('/auth/me')
        .then(response => {
          setUser(response.data);
        })
        .catch(() => {
          Cookies.remove('access_token');
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    const { access_token } = response.data;

    Cookies.set('access_token', access_token, { expires: 1 }); // 1 day

    // Get user info
    const userResponse = await api.get('/auth/me');
    setUser(userResponse.data);

    return response.data;
  };

  const logout = () => {
    Cookies.remove('access_token');
    setUser(null);
  };

  return { user, loading, login, logout };
};
EOF

# Create basic layout
mkdir -p src/components/layout
cat > src/components/layout/DashboardLayout.tsx << 'EOF'
'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, logout } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Omnify Marketing Cloud
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                {user?.first_name} {user?.last_name}
              </span>
              <button
                onClick={logout}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  );
}
EOF

# Create login page
mkdir -p src/app/login
cat > src/app/login/page.tsx << 'EOF'
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(email, password);
      router.push('/dashboard');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to Omnify
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="sr-only">Email</label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="relative block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Email address"
            />
          </div>
          <div>
            <label htmlFor="password" className="sr-only">Password</label>
            <input
              id="password"
              name="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="relative block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Password"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? 'Signing in...' : 'Sign in'}
          </button>
        </form>
      </div>
    </div>
  );
}
EOF

# Create comprehensive dashboard components
mkdir -p src/components/dashboard src/components/charts src/components/ui

# Create metric card component
cat > src/components/dashboard/MetricCard.tsx << 'EOF'
'use client';

import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  format?: 'currency' | 'percentage' | 'number';
  subtitle?: string;
}

export default function MetricCard({
  title,
  value,
  change,
  trend,
  format = 'number',
  subtitle
}: MetricCardProps) {
  const formatValue = (val: string | number) => {
    if (format === 'currency') {
      return `$${Number(val).toLocaleString()}`;
    }
    if (format === 'percentage') {
      return `${Number(val).toFixed(2)}%`;
    }
    return val.toString();
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        {trend && getTrendIcon()}
      </div>
      <div className="mt-2">
        <p className="text-3xl font-bold text-gray-900">
          {formatValue(value)}
        </p>
        {change !== undefined && (
          <p className={`text-sm ${getTrendColor()} flex items-center mt-1`}>
            {change > 0 ? '+' : ''}{change.toFixed(1)}%
            <span className="text-gray-500 ml-1">vs last period</span>
          </p>
        )}
        {subtitle && (
          <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
        )}
      </div>
    </div>
  );
}
EOF

# Create chart components
cat > src/components/charts/LineChart.tsx << 'EOF'
'use client';

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface LineChartProps {
  data: Array<{ date: string; value: number; [key: string]: any }>;
  dataKey?: string;
  color?: string;
  height?: number;
}

export default function CustomLineChart({
  data,
  dataKey = 'value',
  color = '#3b82f6',
  height = 300
}: LineChartProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="date"
          tick={{ fontSize: 12 }}
          tickFormatter={(value) => new Date(value).toLocaleDateString()}
        />
        <YAxis tick={{ fontSize: 12 }} />
        <Tooltip
          labelFormatter={(value) => new Date(value).toLocaleDateString()}
          formatter={(value: number) => [value.toLocaleString(), dataKey]}
        />
        <Line
          type="monotone"
          dataKey={dataKey}
          stroke={color}
          strokeWidth={2}
          dot={{ fill: color, strokeWidth: 2, r: 4 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
EOF

# Create AI agent status component
cat > src/components/dashboard/AIAgentCard.tsx << 'EOF'
'use client';

import { Bot, CheckCircle, AlertCircle, Clock } from 'lucide-react';

interface AIAgentCardProps {
  name: string;
  status: 'active' | 'inactive' | 'processing';
  lastRun: string;
  decisionsToday: number;
  successRate: number;
  description: string;
  onTrigger?: () => void;
}

export default function AIAgentCard({
  name,
  status,
  lastRun,
  decisionsToday,
  successRate,
  description,
  onTrigger
}: AIAgentCardProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-red-100 text-red-800';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <div className="flex items-start justify-between">
        <div className="flex items-center">
          <Bot className="h-8 w-8 text-indigo-600" />
          <div className="ml-3">
            <h3 className="text-lg font-medium text-gray-900">{name}</h3>
            <p className="text-sm text-gray-500">{description}</p>
          </div>
        </div>
        <div className="flex items-center">
          {getStatusIcon()}
          <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}>
            {status}
          </span>
        </div>
      </div>

      <div className="mt-4 grid grid-cols-3 gap-4">
        <div>
          <p className="text-sm text-gray-500">Last Run</p>
          <p className="text-sm font-medium text-gray-900">
            {new Date(lastRun).toLocaleString()}
          </p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Decisions Today</p>
          <p className="text-sm font-medium text-gray-900">{decisionsToday}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Success Rate</p>
          <p className="text-sm font-medium text-gray-900">{successRate}%</p>
        </div>
      </div>

      {onTrigger && (
        <button
          onClick={onTrigger}
          className="mt-4 w-full bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors"
        >
          Trigger Analysis
        </button>
      )}
    </div>
  );
}
EOF

# Create campaign table component
cat > src/components/dashboard/CampaignTable.tsx << 'EOF'
'use client';

import { useState } from 'react';
import { ChevronDown, ExternalLink, AlertTriangle } from 'lucide-react';

interface Campaign {
  campaign_id: string;
  name: string;
  platform: string;
  status: string;
  spend: number;
  revenue: number;
  roas: number;
  cac: number;
  target_cac: number;
  budget_utilization: number;
  alert_level: string;
}

interface CampaignTableProps {
  campaigns: Campaign[];
  onCampaignClick?: (campaign: Campaign) => void;
}

export default function CampaignTable({ campaigns, onCampaignClick }: CampaignTableProps) {
  const [sortField, setSortField] = useState<keyof Campaign>('spend');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const sortedCampaigns = [...campaigns].sort((a, b) => {
    const aVal = a[sortField];
    const bVal = b[sortField];

    if (typeof aVal === 'number' && typeof bVal === 'number') {
      return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
    }

    return sortDirection === 'asc'
      ? String(aVal).localeCompare(String(bVal))
      : String(bVal).localeCompare(String(aVal));
  });

  const handleSort = (field: keyof Campaign) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const getPlatformBadge = (platform: string) => {
    const colors = {
      google_ads: 'bg-blue-100 text-blue-800',
      meta_ads: 'bg-purple-100 text-purple-800',
      default: 'bg-gray-100 text-gray-800'
    };

    return colors[platform as keyof typeof colors] || colors.default;
  };

  const getAlertIcon = (alertLevel: string) => {
    if (alertLevel === 'warning' || alertLevel === 'critical') {
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Campaign Performance</h3>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {[
                { key: 'name', label: 'Campaign' },
                { key: 'platform', label: 'Platform' },
                { key: 'spend', label: 'Spend' },
                { key: 'revenue', label: 'Revenue' },
                { key: 'roas', label: 'ROAS' },
                { key: 'cac', label: 'CAC' },
                { key: 'budget_utilization', label: 'Budget Used' }
              ].map((column) => (
                <th
                  key={column.key}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort(column.key as keyof Campaign)}
                >
                  <div className="flex items-center">
                    {column.label}
                    <ChevronDown className="ml-1 h-4 w-4" />
                  </div>
                </th>
              ))}
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedCampaigns.map((campaign) => (
              <tr
                key={campaign.campaign_id}
                className="hover:bg-gray-50 cursor-pointer"
                onClick={() => onCampaignClick?.(campaign)}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {campaign.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {campaign.campaign_id}
                      </div>
                    </div>
                    {getAlertIcon(campaign.alert_level)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPlatformBadge(campaign.platform)}`}>
                    {campaign.platform.replace('_', ' ')}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${campaign.spend.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${campaign.revenue.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`text-sm font-medium ${
                    campaign.roas >= 3 ? 'text-green-600' :
                    campaign.roas >= 2 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {campaign.roas.toFixed(2)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    ${campaign.cac.toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-500">
                    Target: ${campaign.target_cac.toFixed(2)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          campaign.budget_utilization >= 90 ? 'bg-red-500' :
                          campaign.budget_utilization >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(campaign.budget_utilization, 100)}%` }}
                      />
                    </div>
                    <span className="ml-2 text-sm text-gray-600">
                      {campaign.budget_utilization.toFixed(0)}%
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button className="text-indigo-600 hover:text-indigo-900">
                    <ExternalLink className="h-4 w-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
EOF

# Create enhanced dashboard page
mkdir -p src/app/dashboard
cat > src/app/dashboard/page.tsx << 'EOF'
'use client';

import { useQuery, useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import MetricCard from '@/components/dashboard/MetricCard';
import AIAgentCard from '@/components/dashboard/AIAgentCard';
import CampaignTable from '@/components/dashboard/CampaignTable';
import CustomLineChart from '@/components/charts/LineChart';
import api from '@/lib/api';
import { DashboardOverview } from '@/types';

export default function DashboardPage() {
  const [selectedDateRange, setSelectedDateRange] = useState('7');

  // Fetch dashboard data
  const { data: overview, isLoading: overviewLoading } = useQuery({
    queryKey: ['dashboard-overview', selectedDateRange],
    queryFn: async () => {
      const response = await api.get<DashboardOverview>(`/dashboard/overview?client_id=1&days=${selectedDateRange}`);
      return response.data;
    },
  });

  const { data: campaigns, isLoading: campaignsLoading } = useQuery({
    queryKey: ['dashboard-campaigns'],
    queryFn: async () => {
      const response = await api.get('/dashboard/campaigns?client_id=1');
      return response.data;
    },
  });

  const { data: aiAgents, isLoading: aiLoading } = useQuery({
    queryKey: ['dashboard-ai-agents'],
    queryFn: async () => {
      const response = await api.get('/dashboard/ai-agents?client_id=1');
      return response.data;
    },
  });

  const { data: alerts } = useQuery({
    queryKey: ['dashboard-alerts'],
    queryFn: async () => {
      const response = await api.get('/dashboard/alerts?client_id=1');
      return response.data;
    },
  });

  // Trigger AI analysis mutation
  const triggerAnalysis = useMutation({
    mutationFn: async (agent: string) => {
      const response = await api.post('/agents/analyze', {
        client_id: '1',
        agent,
        force_analysis: true
      });
      return response.data;
    },
    onSuccess: () => {
      // Refresh AI agents data
      // queryClient.invalidateQueries(['dashboard-ai-agents']);
    }
  });

  // Generate sample trend data
  const generateTrendData = (baseValue: number, days: number = 7) => {
    return Array.from({ length: days }, (_, i) => ({
      date: new Date(Date.now() - (days - 1 - i) * 24 * 60 * 60 * 1000).toISOString(),
      value: baseValue * (0.8 + Math.random() * 0.4)
    }));
  };

  if (overviewLoading || campaignsLoading || aiLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
          <div className="flex items-center space-x-4">
            <select
              value={selectedDateRange}
              onChange={(e) => setSelectedDateRange(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
            <button
              onClick={() => window.location.reload()}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Alerts Banner */}
        {alerts?.alerts && alerts.alerts.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  {alerts.alerts.length} Active Alert{alerts.alerts.length > 1 ? 's' : ''}
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>{alerts.alerts[0]?.title}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* KPI Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Spend"
            value={overview?.total_spend || 0}
            format="currency"
            change={12.5}
            trend="up"
            subtitle="This period"
          />
          <MetricCard
            title="Total Revenue"
            value={overview?.total_revenue || 0}
            format="currency"
            change={18.3}
            trend="up"
            subtitle="This period"
          />
          <MetricCard
            title="Current ROAS"
            value={overview?.current_roas || 0}
            format="number"
            change={5.2}
            trend={overview?.roas_trend === 'improving' ? 'up' : overview?.roas_trend === 'declining' ? 'down' : 'stable'}
            subtitle={`Target: 3.0`}
          />
          <MetricCard
            title="Current CAC"
            value={overview?.current_cac || 0}
            format="currency"
            change={-8.1}
            trend="down"
            subtitle="Target: $45.00"
          />
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow border">
            <h3 className="text-lg font-medium text-gray-900 mb-4">ROAS Trend</h3>
            <CustomLineChart
              data={generateTrendData(overview?.current_roas || 3.0)}
              dataKey="value"
              color="#10b981"
              height={250}
            />
          </div>
          <div className="bg-white p-6 rounded-lg shadow border">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Spend vs Revenue</h3>
            <CustomLineChart
              data={generateTrendData(overview?.total_spend || 15000)}
              dataKey="value"
              color="#3b82f6"
              height={250}
            />
          </div>
        </div>

        {/* AI Agents Status */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">AI Agents Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <AIAgentCard
              name="ROI Engine X™"
              status={aiAgents?.roi_engine?.status === 'active' ? 'active' : 'inactive'}
              lastRun={aiAgents?.roi_engine?.last_run || new Date().toISOString()}
              decisionsToday={aiAgents?.roi_engine?.decisions_today || 0}
              successRate={aiAgents?.roi_engine?.success_rate || 0}
              description="CAC optimization with real-time bid adjustments"
              onTrigger={() => triggerAnalysis.mutate('roi_engine')}
            />
            <AIAgentCard
              name="Retention Reactor Pro™"
              status={aiAgents?.retention_reactor?.status === 'active' ? 'active' : 'inactive'}
              lastRun={aiAgents?.retention_reactor?.last_run || new Date().toISOString()}
              decisionsToday={aiAgents?.retention_reactor?.actions_triggered || 0}
              successRate={aiAgents?.retention_reactor?.success_rate || 0}
              description="Churn prediction and automated retention actions"
              onTrigger={() => triggerAnalysis.mutate('retention_reactor')}
            />
            <AIAgentCard
              name="EngageSense Ultra™"
              status={aiAgents?.engage_sense?.status === 'active' ? 'active' : 'inactive'}
              lastRun={aiAgents?.engage_sense?.last_run || new Date().toISOString()}
              decisionsToday={aiAgents?.engage_sense?.campaigns_triggered || 0}
              successRate={85}
              description="Customer behavior scoring and personalized outreach"
              onTrigger={() => triggerAnalysis.mutate('engage_sense')}
            />
          </div>
        </div>

        {/* Campaign Performance */}
        <div>
          <CampaignTable
            campaigns={campaigns?.campaigns || []}
            onCampaignClick={(campaign) => {
              console.log('Campaign clicked:', campaign);
              // Navigate to campaign details
            }}
          />
        </div>

        {/* AI Insights Summary */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
          <h3 className="text-lg font-semibold mb-2">Today's AI Impact</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-indigo-100">AI Decisions Made</p>
              <p className="text-2xl font-bold">{overview?.ai_decisions_today || 0}</p>
            </div>
            <div>
              <p className="text-indigo-100">Estimated Savings</p>
              <p className="text-2xl font-bold">${(overview?.estimated_savings || 0).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-indigo-100">Performance Improvement</p>
              <p className="text-2xl font-bold">+{((overview?.current_roas || 0) * 10).toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
EOF

# Update root layout
cat > src/app/layout.tsx << 'EOF'
'use client';

import { Inter } from 'next/font/google';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <html lang="en">
      <body className={inter.className}>
        <QueryClientProvider client={queryClient}>
          {children}
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </body>
    </html>
  );
}
EOF

# Create package.json scripts
npm pkg set scripts.dev="next dev"
npm pkg set scripts.build="next build"
npm pkg set scripts.start="next start"
npm pkg set scripts.lint="next lint"

echo "✅ Frontend setup complete!"
echo ""
echo "Next steps:"
echo "1. cd frontend"
echo "2. npm run dev"
echo "3. Open http://localhost:3000"
echo ""
echo "Test credentials:"
echo "Email: <EMAIL>"
echo "Password: admin123"
