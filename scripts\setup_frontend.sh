#!/bin/bash

# Omnify Marketing Cloud - Frontend Setup Script
# Sets up Next.js dashboard with all required dependencies

set -e

echo "🚀 Setting up Omnify Marketing Cloud Frontend..."

# Create frontend directory
mkdir -p frontend
cd frontend

# Initialize Next.js project with TypeScript
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install additional dependencies
echo "📦 Installing dependencies..."
npm install \
  @tanstack/react-query \
  @tanstack/react-query-devtools \
  recharts \
  lucide-react \
  @radix-ui/react-alert-dialog \
  @radix-ui/react-avatar \
  @radix-ui/react-badge \
  @radix-ui/react-button \
  @radix-ui/react-card \
  @radix-ui/react-dialog \
  @radix-ui/react-dropdown-menu \
  @radix-ui/react-input \
  @radix-ui/react-label \
  @radix-ui/react-select \
  @radix-ui/react-table \
  @radix-ui/react-tabs \
  @radix-ui/react-toast \
  class-variance-authority \
  clsx \
  tailwind-merge \
  date-fns \
  axios \
  js-cookie \
  @types/js-cookie

# Install dev dependencies
npm install -D \
  @types/node \
  autoprefixer \
  postcss \
  tailwindcss

# Create environment file
cat > .env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws
NEXT_PUBLIC_APP_NAME=Omnify Marketing Cloud
EOF

# Create lib directory structure
mkdir -p src/lib src/components src/hooks src/types

# Create API client
cat > src/lib/api.ts << 'EOF'
import axios from 'axios';
import Cookies from 'js-cookie';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = Cookies.get('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      Cookies.remove('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
EOF

# Create types
cat > src/types/index.ts << 'EOF'
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  client_id?: number;
  client_name?: string;
}

export interface DashboardOverview {
  client_id: string;
  total_spend: number;
  total_revenue: number;
  current_roas: number;
  current_cac: number;
  ai_decisions_today: number;
  estimated_savings: number;
  roas_trend: 'improving' | 'stable' | 'declining';
}

export interface Campaign {
  campaign_id: string;
  name: string;
  platform: string;
  status: string;
  spend: number;
  revenue: number;
  roas: number;
  cac: number;
  target_cac: number;
  budget_utilization: number;
  last_ai_optimization: string;
  alert_level: string;
}

export interface Alert {
  alert_id: string;
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'critical' | 'emergency';
  category: string;
  created_at: string;
  status: string;
}
EOF

# Create auth hook
cat > src/hooks/useAuth.ts << 'EOF'
import { useState, useEffect } from 'react';
import { User } from '@/types';
import api from '@/lib/api';
import Cookies from 'js-cookie';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = Cookies.get('access_token');
    if (token) {
      // Verify token and get user info
      api.get('/auth/me')
        .then(response => {
          setUser(response.data);
        })
        .catch(() => {
          Cookies.remove('access_token');
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    const { access_token } = response.data;
    
    Cookies.set('access_token', access_token, { expires: 1 }); // 1 day
    
    // Get user info
    const userResponse = await api.get('/auth/me');
    setUser(userResponse.data);
    
    return response.data;
  };

  const logout = () => {
    Cookies.remove('access_token');
    setUser(null);
  };

  return { user, loading, login, logout };
};
EOF

# Create basic layout
mkdir -p src/components/layout
cat > src/components/layout/DashboardLayout.tsx << 'EOF'
'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, logout } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Omnify Marketing Cloud
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                {user?.first_name} {user?.last_name}
              </span>
              <button
                onClick={logout}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  );
}
EOF

# Create login page
mkdir -p src/app/login
cat > src/app/login/page.tsx << 'EOF'
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(email, password);
      router.push('/dashboard');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to Omnify
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="sr-only">Email</label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="relative block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Email address"
            />
          </div>
          <div>
            <label htmlFor="password" className="sr-only">Password</label>
            <input
              id="password"
              name="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="relative block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Password"
            />
          </div>
          
          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? 'Signing in...' : 'Sign in'}
          </button>
        </form>
      </div>
    </div>
  );
}
EOF

# Create dashboard page
mkdir -p src/app/dashboard
cat > src/app/dashboard/page.tsx << 'EOF'
'use client';

import { useQuery } from '@tanstack/react-query';
import DashboardLayout from '@/components/layout/DashboardLayout';
import api from '@/lib/api';
import { DashboardOverview } from '@/types';

export default function DashboardPage() {
  const { data: overview, isLoading } = useQuery({
    queryKey: ['dashboard-overview'],
    queryFn: async () => {
      const response = await api.get<DashboardOverview>('/dashboard/overview?client_id=1');
      return response.data;
    },
  });

  if (isLoading) {
    return (
      <DashboardLayout>
        <div>Loading...</div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
        
        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Total Spend</h3>
            <p className="text-2xl font-bold text-gray-900">
              ${overview?.total_spend?.toLocaleString() || '0'}
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Total Revenue</h3>
            <p className="text-2xl font-bold text-gray-900">
              ${overview?.total_revenue?.toLocaleString() || '0'}
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Current ROAS</h3>
            <p className="text-2xl font-bold text-gray-900">
              {overview?.current_roas?.toFixed(2) || '0.00'}
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Current CAC</h3>
            <p className="text-2xl font-bold text-gray-900">
              ${overview?.current_cac?.toFixed(2) || '0.00'}
            </p>
          </div>
        </div>

        {/* AI Decisions */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">AI Activity Today</h3>
          <p className="text-3xl font-bold text-indigo-600">
            {overview?.ai_decisions_today || 0} decisions made
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Estimated savings: ${overview?.estimated_savings?.toLocaleString() || '0'}
          </p>
        </div>
      </div>
    </DashboardLayout>
  );
}
EOF

# Update root layout
cat > src/app/layout.tsx << 'EOF'
'use client';

import { Inter } from 'next/font/google';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <html lang="en">
      <body className={inter.className}>
        <QueryClientProvider client={queryClient}>
          {children}
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </body>
    </html>
  );
}
EOF

# Create package.json scripts
npm pkg set scripts.dev="next dev"
npm pkg set scripts.build="next build"
npm pkg set scripts.start="next start"
npm pkg set scripts.lint="next lint"

echo "✅ Frontend setup complete!"
echo ""
echo "Next steps:"
echo "1. cd frontend"
echo "2. npm run dev"
echo "3. Open http://localhost:3000"
echo ""
echo "Test credentials:"
echo "Email: <EMAIL>"
echo "Password: admin123"
