"""
Advanced Email Integration Service for Omnify Marketing Cloud
Supports SendGrid, Mailchimp, and custom SMTP
"""
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from abc import ABC, abstractmethod
import structlog
from pydantic import BaseModel, EmailStr, Field
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

from apps.core.config import settings
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class EmailTemplate(BaseModel):
    """Email template model"""
    template_id: str
    name: str
    subject: str
    html_content: str
    text_content: str
    variables: List[str] = Field(default_factory=list)
    category: str  # "retention", "engagement", "alert", "onboarding"

class EmailRecipient(BaseModel):
    """Email recipient model"""
    email: EmailStr
    name: Optional[str] = None
    variables: Dict[str, Any] = Field(default_factory=dict)

class EmailCampaign(BaseModel):
    """Email campaign model"""
    campaign_id: str
    template_id: str
    recipients: List[EmailRecipient]
    send_time: Optional[datetime] = None
    priority: int = Field(default=3, ge=1, le=5)  # 1=highest, 5=lowest
    tracking_enabled: bool = True

class EmailResult(BaseModel):
    """Email sending result"""
    success: bool
    message_id: Optional[str] = None
    error_message: Optional[str] = None
    recipient_email: str
    sent_at: datetime
    provider: str

class EmailServiceInterface(ABC):
    """Abstract interface for email services"""
    
    @abstractmethod
    async def send_email(
        self, 
        recipient: EmailRecipient, 
        template: EmailTemplate,
        variables: Dict[str, Any] = None
    ) -> EmailResult:
        """Send a single email"""
        pass
    
    @abstractmethod
    async def send_campaign(self, campaign: EmailCampaign) -> List[EmailResult]:
        """Send email campaign to multiple recipients"""
        pass
    
    @abstractmethod
    async def get_templates(self) -> List[EmailTemplate]:
        """Get available email templates"""
        pass

class SendGridService(EmailServiceInterface):
    """SendGrid email service implementation"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.sendgrid.com/v3"
        
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def send_email(
        self, 
        recipient: EmailRecipient, 
        template: EmailTemplate,
        variables: Dict[str, Any] = None
    ) -> EmailResult:
        """Send email via SendGrid"""
        try:
            # Merge variables
            all_variables = {**recipient.variables, **(variables or {})}
            
            # Replace variables in content
            subject = self._replace_variables(template.subject, all_variables)
            html_content = self._replace_variables(template.html_content, all_variables)
            text_content = self._replace_variables(template.text_content, all_variables)
            
            payload = {
                "personalizations": [{
                    "to": [{"email": recipient.email, "name": recipient.name}],
                    "subject": subject
                }],
                "from": {"email": "<EMAIL>", "name": "Omnify Marketing Cloud"},
                "content": [
                    {"type": "text/plain", "value": text_content},
                    {"type": "text/html", "value": html_content}
                ],
                "categories": [template.category],
                "tracking_settings": {
                    "click_tracking": {"enable": True},
                    "open_tracking": {"enable": True}
                }
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/mail/send",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json=payload,
                    timeout=30.0
                )
                
                if response.status_code == 202:
                    message_id = response.headers.get("X-Message-Id")
                    logger.info(
                        "Email sent via SendGrid",
                        recipient=recipient.email,
                        template=template.template_id,
                        message_id=message_id
                    )
                    
                    return EmailResult(
                        success=True,
                        message_id=message_id,
                        recipient_email=recipient.email,
                        sent_at=datetime.utcnow(),
                        provider="sendgrid"
                    )
                else:
                    error_msg = f"SendGrid API error: {response.status_code} - {response.text}"
                    logger.error("SendGrid email failed", error=error_msg)
                    
                    return EmailResult(
                        success=False,
                        error_message=error_msg,
                        recipient_email=recipient.email,
                        sent_at=datetime.utcnow(),
                        provider="sendgrid"
                    )
                    
        except Exception as e:
            logger.error("SendGrid email exception", error=str(e))
            return EmailResult(
                success=False,
                error_message=str(e),
                recipient_email=recipient.email,
                sent_at=datetime.utcnow(),
                provider="sendgrid"
            )
    
    async def send_campaign(self, campaign: EmailCampaign) -> List[EmailResult]:
        """Send email campaign via SendGrid"""
        # Get template
        templates = await self.get_templates()
        template = next((t for t in templates if t.template_id == campaign.template_id), None)
        
        if not template:
            raise ValueError(f"Template {campaign.template_id} not found")
        
        # Send emails concurrently (with rate limiting)
        semaphore = asyncio.Semaphore(10)  # Max 10 concurrent sends
        
        async def send_with_semaphore(recipient):
            async with semaphore:
                return await self.send_email(recipient, template)
        
        results = await asyncio.gather(
            *[send_with_semaphore(recipient) for recipient in campaign.recipients],
            return_exceptions=True
        )
        
        # Filter out exceptions and convert to EmailResult
        email_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                email_results.append(EmailResult(
                    success=False,
                    error_message=str(result),
                    recipient_email=campaign.recipients[i].email,
                    sent_at=datetime.utcnow(),
                    provider="sendgrid"
                ))
            else:
                email_results.append(result)
        
        return email_results
    
    async def get_templates(self) -> List[EmailTemplate]:
        """Get SendGrid templates (simplified - would fetch from API)"""
        return [
            EmailTemplate(
                template_id="retention_high_risk",
                name="High Churn Risk Alert",
                subject="We miss you, {{customer_name}}! Special offer inside",
                html_content="""
                <h2>We miss you, {{customer_name}}!</h2>
                <p>We noticed you haven't been active lately. Here's a special {{discount_percent}}% discount to welcome you back!</p>
                <p>Use code: {{discount_code}}</p>
                <a href="{{shop_url}}">Shop Now</a>
                """,
                text_content="We miss you, {{customer_name}}! Use code {{discount_code}} for {{discount_percent}}% off.",
                variables=["customer_name", "discount_percent", "discount_code", "shop_url"],
                category="retention"
            ),
            EmailTemplate(
                template_id="engagement_champion",
                name="VIP Champion Rewards",
                subject="Exclusive VIP rewards for our top customer!",
                html_content="""
                <h2>Thank you for being a champion customer!</h2>
                <p>Your loyalty means everything to us. Enjoy these exclusive VIP benefits:</p>
                <ul>
                    <li>Early access to new products</li>
                    <li>Exclusive discounts</li>
                    <li>Priority customer support</li>
                </ul>
                """,
                text_content="Thank you for being a champion customer! Enjoy exclusive VIP benefits.",
                variables=["customer_name"],
                category="engagement"
            ),
            EmailTemplate(
                template_id="alert_low_roas",
                name="Low ROAS Campaign Alert",
                subject="Campaign Performance Alert - {{campaign_name}}",
                html_content="""
                <h2>Campaign Performance Alert</h2>
                <p>Campaign "{{campaign_name}}" has a ROAS of {{current_roas}}, which is below the target of {{target_roas}}.</p>
                <p>AI recommendation: {{ai_recommendation}}</p>
                <p>Confidence: {{ai_confidence}}%</p>
                """,
                text_content="Campaign {{campaign_name}} ROAS: {{current_roas}} (target: {{target_roas}}). AI recommends: {{ai_recommendation}}",
                variables=["campaign_name", "current_roas", "target_roas", "ai_recommendation", "ai_confidence"],
                category="alert"
            )
        ]
    
    def _replace_variables(self, content: str, variables: Dict[str, Any]) -> str:
        """Replace template variables in content"""
        for key, value in variables.items():
            content = content.replace(f"{{{{{key}}}}}", str(value))
        return content

class MailchimpService(EmailServiceInterface):
    """Mailchimp email service implementation"""
    
    def __init__(self, api_key: str, server_prefix: str):
        self.api_key = api_key
        self.server_prefix = server_prefix
        self.base_url = f"https://{server_prefix}.api.mailchimp.com/3.0"
    
    async def send_email(
        self, 
        recipient: EmailRecipient, 
        template: EmailTemplate,
        variables: Dict[str, Any] = None
    ) -> EmailResult:
        """Send email via Mailchimp (simplified implementation)"""
        # Mailchimp typically uses campaigns rather than individual emails
        # This is a simplified implementation
        try:
            logger.info("Email would be sent via Mailchimp", recipient=recipient.email)
            return EmailResult(
                success=True,
                message_id=f"mailchimp_{int(datetime.utcnow().timestamp())}",
                recipient_email=recipient.email,
                sent_at=datetime.utcnow(),
                provider="mailchimp"
            )
        except Exception as e:
            return EmailResult(
                success=False,
                error_message=str(e),
                recipient_email=recipient.email,
                sent_at=datetime.utcnow(),
                provider="mailchimp"
            )
    
    async def send_campaign(self, campaign: EmailCampaign) -> List[EmailResult]:
        """Send campaign via Mailchimp"""
        # Implementation would create Mailchimp campaign and send
        results = []
        for recipient in campaign.recipients:
            result = await self.send_email(recipient, None)
            results.append(result)
        return results
    
    async def get_templates(self) -> List[EmailTemplate]:
        """Get Mailchimp templates"""
        return []  # Would fetch from Mailchimp API

class SMTPService(EmailServiceInterface):
    """SMTP email service implementation"""
    
    def __init__(self, host: str, port: int, username: str, password: str, use_tls: bool = True):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.use_tls = use_tls
    
    async def send_email(
        self, 
        recipient: EmailRecipient, 
        template: EmailTemplate,
        variables: Dict[str, Any] = None
    ) -> EmailResult:
        """Send email via SMTP"""
        try:
            # Merge variables
            all_variables = {**recipient.variables, **(variables or {})}
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = self._replace_variables(template.subject, all_variables)
            msg['From'] = self.username
            msg['To'] = recipient.email
            
            # Add text and HTML parts
            text_content = self._replace_variables(template.text_content, all_variables)
            html_content = self._replace_variables(template.html_content, all_variables)
            
            text_part = MIMEText(text_content, 'plain')
            html_part = MIMEText(html_content, 'html')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # Send email
            server = smtplib.SMTP(self.host, self.port)
            if self.use_tls:
                server.starttls()
            server.login(self.username, self.password)
            
            text = msg.as_string()
            server.sendmail(self.username, recipient.email, text)
            server.quit()
            
            logger.info("Email sent via SMTP", recipient=recipient.email)
            
            return EmailResult(
                success=True,
                message_id=f"smtp_{int(datetime.utcnow().timestamp())}",
                recipient_email=recipient.email,
                sent_at=datetime.utcnow(),
                provider="smtp"
            )
            
        except Exception as e:
            logger.error("SMTP email failed", error=str(e))
            return EmailResult(
                success=False,
                error_message=str(e),
                recipient_email=recipient.email,
                sent_at=datetime.utcnow(),
                provider="smtp"
            )
    
    async def send_campaign(self, campaign: EmailCampaign) -> List[EmailResult]:
        """Send campaign via SMTP"""
        templates = await self.get_templates()
        template = next((t for t in templates if t.template_id == campaign.template_id), None)
        
        if not template:
            raise ValueError(f"Template {campaign.template_id} not found")
        
        results = []
        for recipient in campaign.recipients:
            result = await self.send_email(recipient, template)
            results.append(result)
            # Add small delay to avoid overwhelming SMTP server
            await asyncio.sleep(0.1)
        
        return results
    
    async def get_templates(self) -> List[EmailTemplate]:
        """Get SMTP templates (stored locally)"""
        return []  # Would load from local storage or database
    
    def _replace_variables(self, content: str, variables: Dict[str, Any]) -> str:
        """Replace template variables in content"""
        for key, value in variables.items():
            content = content.replace(f"{{{{{key}}}}}", str(value))
        return content

class EmailServiceManager:
    """Manages multiple email service providers with fallback"""
    
    def __init__(self):
        self.services: List[EmailServiceInterface] = []
        self.primary_service: Optional[EmailServiceInterface] = None
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize email services based on configuration"""
        # Initialize SendGrid if configured
        if hasattr(settings, 'SENDGRID_API_KEY') and settings.SENDGRID_API_KEY:
            sendgrid_service = SendGridService(settings.SENDGRID_API_KEY)
            self.services.append(sendgrid_service)
            if not self.primary_service:
                self.primary_service = sendgrid_service
        
        # Initialize SMTP if configured
        if all([
            hasattr(settings, 'SMTP_HOST') and settings.SMTP_HOST,
            hasattr(settings, 'SMTP_USER') and settings.SMTP_USER,
            hasattr(settings, 'SMTP_PASSWORD') and settings.SMTP_PASSWORD
        ]):
            smtp_service = SMTPService(
                host=settings.SMTP_HOST,
                port=getattr(settings, 'SMTP_PORT', 587),
                username=settings.SMTP_USER,
                password=settings.SMTP_PASSWORD
            )
            self.services.append(smtp_service)
            if not self.primary_service:
                self.primary_service = smtp_service
    
    async def send_email(
        self, 
        recipient: EmailRecipient, 
        template_id: str,
        variables: Dict[str, Any] = None
    ) -> EmailResult:
        """Send email with fallback to secondary services"""
        
        # Get template
        template = await self._get_template(template_id)
        if not template:
            raise ValueError(f"Template {template_id} not found")
        
        # Try primary service first
        if self.primary_service:
            result = await self.primary_service.send_email(recipient, template, variables)
            if result.success:
                return result
            
            logger.warning(
                "Primary email service failed, trying fallback",
                error=result.error_message,
                recipient=recipient.email
            )
        
        # Try fallback services
        for service in self.services:
            if service == self.primary_service:
                continue
                
            try:
                result = await service.send_email(recipient, template, variables)
                if result.success:
                    logger.info(
                        "Email sent via fallback service",
                        provider=result.provider,
                        recipient=recipient.email
                    )
                    return result
            except Exception as e:
                logger.error(
                    "Fallback email service failed",
                    error=str(e),
                    provider=service.__class__.__name__
                )
        
        # All services failed
        return EmailResult(
            success=False,
            error_message="All email services failed",
            recipient_email=recipient.email,
            sent_at=datetime.utcnow(),
            provider="none"
        )
    
    async def send_campaign(self, campaign: EmailCampaign) -> List[EmailResult]:
        """Send email campaign with fallback"""
        if self.primary_service:
            try:
                return await self.primary_service.send_campaign(campaign)
            except Exception as e:
                logger.error("Campaign sending failed", error=str(e))
        
        # Fallback to individual sends
        results = []
        for recipient in campaign.recipients:
            result = await self.send_email(recipient, campaign.template_id)
            results.append(result)
        
        return results
    
    async def _get_template(self, template_id: str) -> Optional[EmailTemplate]:
        """Get template from any available service"""
        for service in self.services:
            templates = await service.get_templates()
            template = next((t for t in templates if t.template_id == template_id), None)
            if template:
                return template
        return None

# Global email service manager instance
email_manager = EmailServiceManager()
