# Omnify Marketing Cloud - API Guide

## Overview

The Omnify Marketing Cloud API provides endpoints for managing AI-driven marketing automation. This guide covers the core functionality needed to integrate with the three main AI agents.

## Authentication

All API endpoints require JWT authentication. First, obtain a token:

```bash
curl -X POST "http://localhost:8000/api/v1/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=your-password"
```

Use the returned token in subsequent requests:

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/campaigns/"
```

## Core Endpoints

### 1. Analytics - ROAS Tracking

Get real-time ROAS with 7-day trend and threshold alerts:

```bash
GET /api/v1/analytics/roas?client_id=123&days=7
```

**Response:**
```json
{
  "client_id": "123",
  "period_start": "2024-01-01T00:00:00Z",
  "period_end": "2024-01-08T00:00:00Z",
  "total_spend": 5000.0,
  "total_revenue": 12500.0,
  "roas": 2.5,
  "daily_metrics": [
    {
      "date": "2024-01-07",
      "spend": 714.29,
      "revenue": 1785.71,
      "roas": 2.5
    }
  ],
  "threshold_alerts": []
}
```

### 2. AI Agents - ROI Engine X™

Trigger CAC optimization:

```bash
POST /api/v1/agents/optimize
```

**Request Body:**
```json
{
  "client_id": "123",
  "campaign_ids": ["campaign_123"],
  "agent_type": "roi_engine",
  "force_execution": false
}
```

**Response:**
```json
{
  "request_id": "opt_1704067200",
  "client_id": "123",
  "agent_type": "roi_engine",
  "decisions": [],
  "status": "processing",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 3. Campaign Management

Create a new campaign:

```bash
POST /api/v1/campaigns/?client_id=123
```

**Request Body:**
```json
{
  "name": "Q1 Brand Campaign",
  "platform": "google_ads",
  "campaign_id": "12345678",
  "budget": 10000.0,
  "target_cac": 50.0
}
```

### 4. Webhooks for n8n Integration

Trigger campaign metrics collection:

```bash
POST /api/v1/webhooks/n8n/campaign-metrics
```

**Request Body:**
```json
{
  "client_id": "123"
}
```

## AI Decision Flow

The ROI Engine X™ follows this decision process:

1. **Data Collection**: Fetch campaign metrics from Google/Meta Ads
2. **AI Analysis**: Process through Manus RL (primary) or OpenAI GPT-4 (fallback)
3. **Decision Making**: Generate action with confidence score
4. **Execution**: Apply bid adjustments if confidence > 85%
5. **Logging**: Record decision and results for analysis

## Rate Limiting

- **Default**: 60 requests per minute per IP
- **Burst**: 10 requests in quick succession
- **Headers**: Check `X-RateLimit-*` headers in responses

## Error Handling

All errors follow this format:

```json
{
  "error": "Error type",
  "message": "Detailed error message",
  "details": {}
}
```

Common HTTP status codes:
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Monitoring

### Health Check
```bash
GET /health
```

### Metrics (Prometheus format)
```bash
GET /metrics
```

### Agent Status
```bash
GET /api/v1/agents/status/roi_engine?client_id=123
```

## Integration Examples

### n8n Workflow Integration

1. **Schedule Trigger**: Every 15 minutes
2. **Fetch CAC Data**: `GET /api/v1/analytics/cac`
3. **Check Performance**: If CAC > target
4. **Trigger AI**: `POST /api/v1/agents/optimize`
5. **Execute Action**: Apply recommended bid adjustments

### Retool Dashboard Integration

```javascript
// Fetch ROAS data
const roasData = await omnifyApi.get('/api/v1/analytics/roas', {
  params: { client_id: {{ clientId.value }}, days: 7 }
});

// Display in line chart
return roasData.daily_metrics;
```

## Best Practices

1. **Authentication**: Store JWT tokens securely and refresh before expiry
2. **Rate Limiting**: Implement exponential backoff for retries
3. **Error Handling**: Always check response status and handle errors gracefully
4. **Monitoring**: Use the `/health` endpoint for service monitoring
5. **Webhooks**: Validate webhook signatures for security

## Support

For technical support or questions about the API:
- Documentation: http://localhost:8000/docs
- Health Status: http://localhost:8000/health
- Metrics: http://localhost:8000/metrics
