"""
Webhooks API endpoints for n8n integration and external notifications
"""
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Request, HTTPException, BackgroundTasks, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel
import structlog
import json

from apps.core.database import get_db, Campaign, CampaignMetric, Client
from lib.connectors.google_ads import GoogleAdsConnector
from lib.connectors.meta_ads import MetaAdsConnector

logger = structlog.get_logger()

router = APIRouter()

class WebhookPayload(BaseModel):
    event_type: str
    client_id: str
    data: Dict[str, Any]
    timestamp: Optional[datetime] = None

class N8nTriggerResponse(BaseModel):
    status: str
    message: str
    webhook_id: str
    timestamp: datetime

@router.post("/n8n/campaign-metrics")
async def n8n_campaign_metrics_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Webhook endpoint for n8n to trigger campaign metrics collection
    """
    try:
        # Parse request body
        body = await request.body()
        payload = json.loads(body)
        
        client_id = payload.get("client_id")
        if not client_id:
            raise HTTPException(status_code=400, detail="client_id is required")
        
        # Verify client exists
        client_query = select(Client).where(Client.id == client_id)
        client_result = await db.execute(client_query)
        client = client_result.scalar_one_or_none()
        
        if not client:
            raise HTTPException(status_code=404, detail="Client not found")
        
        # Trigger metrics collection in background
        background_tasks.add_task(
            _collect_campaign_metrics,
            client,
            db
        )
        
        webhook_id = f"metrics_{int(datetime.utcnow().timestamp())}"
        
        logger.info(
            "Campaign metrics collection triggered via n8n",
            client_id=client_id,
            webhook_id=webhook_id
        )
        
        return N8nTriggerResponse(
            status="triggered",
            message="Campaign metrics collection started",
            webhook_id=webhook_id,
            timestamp=datetime.utcnow()
        )
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    except Exception as e:
        logger.error("n8n webhook failed", error=str(e))
        raise HTTPException(status_code=500, detail="Webhook processing failed")

@router.post("/n8n/ai-decision")
async def n8n_ai_decision_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Webhook endpoint for n8n to trigger AI decision making
    """
    try:
        # Parse request body
        body = await request.body()
        payload = json.loads(body)
        
        client_id = payload.get("client_id")
        campaign_id = payload.get("campaign_id")
        
        if not client_id or not campaign_id:
            raise HTTPException(
                status_code=400, 
                detail="client_id and campaign_id are required"
            )
        
        # Verify campaign exists
        campaign_query = select(Campaign).where(
            Campaign.client_id == client_id,
            Campaign.campaign_id == campaign_id
        )
        campaign_result = await db.execute(campaign_query)
        campaign = campaign_result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Trigger AI decision in background
        background_tasks.add_task(
            _trigger_ai_decision,
            campaign,
            payload.get("force_execution", False),
            db
        )
        
        webhook_id = f"ai_decision_{int(datetime.utcnow().timestamp())}"
        
        logger.info(
            "AI decision triggered via n8n",
            client_id=client_id,
            campaign_id=campaign_id,
            webhook_id=webhook_id
        )
        
        return N8nTriggerResponse(
            status="triggered",
            message="AI decision process started",
            webhook_id=webhook_id,
            timestamp=datetime.utcnow()
        )
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    except Exception as e:
        logger.error("n8n AI decision webhook failed", error=str(e))
        raise HTTPException(status_code=500, detail="Webhook processing failed")

@router.post("/external/performance-alert")
async def external_performance_alert(
    payload: WebhookPayload,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    External webhook for performance alerts from monitoring systems
    """
    try:
        # Verify client exists
        client_query = select(Client).where(Client.id == payload.client_id)
        client_result = await db.execute(client_query)
        client = client_result.scalar_one_or_none()
        
        if not client:
            raise HTTPException(status_code=404, detail="Client not found")
        
        # Process alert based on event type
        if payload.event_type == "high_cac_alert":
            background_tasks.add_task(
                _handle_high_cac_alert,
                client,
                payload.data,
                db
            )
        elif payload.event_type == "low_roas_alert":
            background_tasks.add_task(
                _handle_low_roas_alert,
                client,
                payload.data,
                db
            )
        elif payload.event_type == "budget_exhaustion_alert":
            background_tasks.add_task(
                _handle_budget_alert,
                client,
                payload.data,
                db
            )
        
        logger.info(
            "External performance alert received",
            client_id=payload.client_id,
            event_type=payload.event_type
        )
        
        return {"status": "received", "message": "Alert processed"}
        
    except Exception as e:
        logger.error("External alert webhook failed", error=str(e))
        raise HTTPException(status_code=500, detail="Alert processing failed")

@router.get("/health")
async def webhook_health_check():
    """Health check for webhook endpoints"""
    return {
        "status": "healthy",
        "endpoints": [
            "/webhooks/n8n/campaign-metrics",
            "/webhooks/n8n/ai-decision",
            "/webhooks/external/performance-alert"
        ],
        "timestamp": datetime.utcnow()
    }

# Background task functions

async def _collect_campaign_metrics(client: Client, db: AsyncSession):
    """
    Collect campaign metrics from advertising platforms
    """
    try:
        # Get active campaigns for client
        campaigns_query = select(Campaign).where(
            Campaign.client_id == client.id,
            Campaign.status == "active"
        )
        campaigns_result = await db.execute(campaigns_query)
        campaigns = campaigns_result.scalars().all()
        
        for campaign in campaigns:
            try:
                if campaign.platform == "google_ads" and client.google_ads_customer_id:
                    connector = GoogleAdsConnector(client.google_ads_customer_id)
                    
                    # Get yesterday's metrics
                    end_date = datetime.utcnow().date()
                    start_date = end_date
                    
                    metrics = await connector.get_campaign_metrics(
                        start_date=datetime.combine(start_date, datetime.min.time()),
                        end_date=datetime.combine(end_date, datetime.max.time()),
                        campaign_ids=[campaign.campaign_id]
                    )
                    
                    # Save metrics to database
                    for metric in metrics:
                        db_metric = CampaignMetric(
                            campaign_id=campaign.id,
                            date=metric.date,
                            impressions=metric.impressions,
                            clicks=metric.clicks,
                            conversions=metric.conversions,
                            spend=metric.cost,
                            ctr=metric.ctr,
                            cpc=metric.cpc
                        )
                        db.add(db_metric)
                
                elif campaign.platform == "meta_ads" and client.meta_ads_account_id:
                    connector = MetaAdsConnector(client.meta_ads_account_id)
                    
                    # Get yesterday's metrics
                    end_date = datetime.utcnow().date()
                    start_date = end_date
                    
                    metrics = await connector.get_campaign_metrics(
                        start_date=datetime.combine(start_date, datetime.min.time()),
                        end_date=datetime.combine(end_date, datetime.max.time()),
                        campaign_ids=[campaign.campaign_id]
                    )
                    
                    # Save metrics to database
                    for metric in metrics:
                        db_metric = CampaignMetric(
                            campaign_id=campaign.id,
                            date=metric.date,
                            impressions=metric.impressions,
                            clicks=metric.clicks,
                            conversions=metric.conversions,
                            spend=metric.spend,
                            ctr=metric.ctr,
                            cpc=metric.cpc
                        )
                        db.add(db_metric)
                        
            except Exception as e:
                logger.error(
                    "Failed to collect metrics for campaign",
                    error=str(e),
                    campaign_id=campaign.id
                )
        
        await db.commit()
        
        logger.info(
            "Campaign metrics collection completed",
            client_id=client.id,
            campaign_count=len(campaigns)
        )
        
    except Exception as e:
        logger.error(
            "Campaign metrics collection failed",
            error=str(e),
            client_id=client.id
        )

async def _trigger_ai_decision(
    campaign: Campaign,
    force_execution: bool,
    db: AsyncSession
):
    """
    Trigger AI decision for specific campaign
    """
    try:
        # Import here to avoid circular imports
        from lib.ai.decision_engine import ROIEngineX
        
        roi_engine = ROIEngineX()
        
        # Get historical metrics (last 30 days)
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)
        
        metrics_query = select(CampaignMetric).where(
            CampaignMetric.campaign_id == campaign.id,
            CampaignMetric.date >= start_date
        ).order_by(CampaignMetric.date.desc())
        
        metrics_result = await db.execute(metrics_query)
        metrics = metrics_result.scalars().all()
        
        # Convert to dict format
        historical_metrics = [
            {
                "date": m.date.isoformat(),
                "spend": m.spend,
                "revenue": m.revenue or 0,
                "conversions": m.conversions,
                "clicks": m.clicks,
                "impressions": m.impressions
            }
            for m in metrics
        ]
        
        # Prepare campaign data
        campaign_data = {
            "campaign_id": campaign.campaign_id,
            "platform": campaign.platform,
            "budget": campaign.budget,
            "target_cac": campaign.target_cac,
            "budget_remaining": campaign.budget * 0.7,  # Assume 70% remaining
            "days_remaining": 30
        }
        
        # Get AI decision
        decision = await roi_engine.optimize_campaign(
            client_id=str(campaign.client_id),
            campaign_data=campaign_data,
            historical_metrics=historical_metrics
        )
        
        logger.info(
            "AI decision completed via webhook",
            campaign_id=campaign.id,
            action=decision.action,
            confidence=decision.confidence
        )
        
    except Exception as e:
        logger.error(
            "AI decision failed via webhook",
            error=str(e),
            campaign_id=campaign.id
        )

async def _handle_high_cac_alert(client: Client, alert_data: Dict[str, Any], db: AsyncSession):
    """Handle high CAC alert"""
    logger.info("Processing high CAC alert", client_id=client.id, data=alert_data)
    # TODO: Implement automated response to high CAC

async def _handle_low_roas_alert(client: Client, alert_data: Dict[str, Any], db: AsyncSession):
    """Handle low ROAS alert"""
    logger.info("Processing low ROAS alert", client_id=client.id, data=alert_data)
    # TODO: Implement automated response to low ROAS

async def _handle_budget_alert(client: Client, alert_data: Dict[str, Any], db: AsyncSession):
    """Handle budget exhaustion alert"""
    logger.info("Processing budget alert", client_id=client.id, data=alert_data)
    # TODO: Implement automated budget management
