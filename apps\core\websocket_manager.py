"""
Real-time WebSocket Manager for Omnify Marketing Cloud
Handles real-time updates for dashboard, alerts, and AI decisions
"""
import asyncio
import json
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel
import structlog

logger = structlog.get_logger()

class WebSocketMessage(BaseModel):
    """WebSocket message structure"""
    type: str
    data: Dict[str, Any]
    timestamp: datetime
    client_id: Optional[str] = None

class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        # Store connections by client_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str, user_id: str):
        """Accept new WebSocket connection"""
        await websocket.accept()
        
        # Initialize client connections if not exists
        if client_id not in self.active_connections:
            self.active_connections[client_id] = set()
        
        # Add connection
        self.active_connections[client_id].add(websocket)
        
        # Store metadata
        self.connection_metadata[websocket] = {
            "client_id": client_id,
            "user_id": user_id,
            "connected_at": datetime.utcnow(),
            "last_ping": datetime.utcnow()
        }
        
        logger.info(
            "WebSocket connected",
            client_id=client_id,
            user_id=user_id,
            total_connections=len(self.connection_metadata)
        )
        
        # Send welcome message
        await self.send_personal_message(websocket, {
            "type": "connection_established",
            "data": {
                "message": "Connected to Omnify Marketing Cloud",
                "client_id": client_id,
                "features": ["real_time_metrics", "ai_notifications", "alerts"]
            },
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        if websocket in self.connection_metadata:
            metadata = self.connection_metadata[websocket]
            client_id = metadata["client_id"]
            user_id = metadata["user_id"]
            
            # Remove from active connections
            if client_id in self.active_connections:
                self.active_connections[client_id].discard(websocket)
                
                # Clean up empty client sets
                if not self.active_connections[client_id]:
                    del self.active_connections[client_id]
            
            # Remove metadata
            del self.connection_metadata[websocket]
            
            logger.info(
                "WebSocket disconnected",
                client_id=client_id,
                user_id=user_id,
                total_connections=len(self.connection_metadata)
            )
    
    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(json.dumps(message, default=str))
        except Exception as e:
            logger.error("Failed to send personal message", error=str(e))
            self.disconnect(websocket)
    
    async def send_to_client(self, client_id: str, message: Dict[str, Any]):
        """Send message to all connections for a client"""
        if client_id not in self.active_connections:
            return
        
        # Create list to avoid modification during iteration
        connections = list(self.active_connections[client_id])
        
        for websocket in connections:
            try:
                await websocket.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(
                    "Failed to send message to client",
                    error=str(e),
                    client_id=client_id
                )
                self.disconnect(websocket)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        for client_id in list(self.active_connections.keys()):
            await self.send_to_client(client_id, message)
    
    async def send_metric_update(self, client_id: str, metrics: Dict[str, Any]):
        """Send real-time metric update"""
        message = {
            "type": "metric_update",
            "data": metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_to_client(client_id, message)
    
    async def send_ai_decision(self, client_id: str, decision: Dict[str, Any]):
        """Send AI decision notification"""
        message = {
            "type": "ai_decision",
            "data": {
                "agent": decision.get("agent_name"),
                "decision_type": decision.get("decision_type"),
                "confidence": decision.get("confidence"),
                "campaign_id": decision.get("campaign_id"),
                "reasoning": decision.get("reasoning"),
                "parameters": decision.get("parameters", {}),
                "timestamp": decision.get("created_at", datetime.utcnow().isoformat())
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_to_client(client_id, message)
    
    async def send_alert(self, client_id: str, alert: Dict[str, Any]):
        """Send alert notification"""
        message = {
            "type": "alert",
            "data": {
                "alert_id": alert.get("alert_id"),
                "title": alert.get("title"),
                "message": alert.get("message"),
                "severity": alert.get("severity"),
                "category": alert.get("category"),
                "campaign_id": alert.get("campaign_id"),
                "actions": alert.get("recommended_actions", [])
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_to_client(client_id, message)
    
    async def send_campaign_update(self, client_id: str, campaign_update: Dict[str, Any]):
        """Send campaign status update"""
        message = {
            "type": "campaign_update",
            "data": campaign_update,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_to_client(client_id, message)
    
    async def send_system_status(self, status: Dict[str, Any]):
        """Send system status update to all clients"""
        message = {
            "type": "system_status",
            "data": status,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.broadcast_to_all(message)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        total_connections = len(self.connection_metadata)
        clients_connected = len(self.active_connections)
        
        # Calculate connections per client
        connections_per_client = {}
        for client_id, connections in self.active_connections.items():
            connections_per_client[client_id] = len(connections)
        
        return {
            "total_connections": total_connections,
            "clients_connected": clients_connected,
            "connections_per_client": connections_per_client,
            "uptime_seconds": 0  # Would track actual uptime
        }
    
    async def ping_all_connections(self):
        """Send ping to all connections to keep them alive"""
        ping_message = {
            "type": "ping",
            "data": {"timestamp": datetime.utcnow().isoformat()},
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Send ping to all connections
        for websocket in list(self.connection_metadata.keys()):
            try:
                await websocket.send_text(json.dumps(ping_message, default=str))
                self.connection_metadata[websocket]["last_ping"] = datetime.utcnow()
            except Exception as e:
                logger.error("Failed to ping connection", error=str(e))
                self.disconnect(websocket)

# Global connection manager instance
connection_manager = ConnectionManager()

class WebSocketNotificationService:
    """Service for sending notifications via WebSocket"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
    
    async def notify_metric_change(
        self, 
        client_id: str, 
        metric_name: str, 
        old_value: float, 
        new_value: float,
        change_percentage: float
    ):
        """Notify about metric changes"""
        await self.connection_manager.send_metric_update(client_id, {
            "metric_name": metric_name,
            "old_value": old_value,
            "new_value": new_value,
            "change_percentage": change_percentage,
            "trend": "up" if change_percentage > 0 else "down" if change_percentage < 0 else "stable"
        })
    
    async def notify_ai_optimization(
        self,
        client_id: str,
        agent_name: str,
        campaign_id: str,
        optimization_type: str,
        expected_impact: Dict[str, Any]
    ):
        """Notify about AI optimizations"""
        await self.connection_manager.send_ai_decision(client_id, {
            "agent_name": agent_name,
            "campaign_id": campaign_id,
            "decision_type": optimization_type,
            "confidence": expected_impact.get("confidence", 0.8),
            "reasoning": f"AI optimization: {optimization_type}",
            "parameters": expected_impact,
            "created_at": datetime.utcnow().isoformat()
        })
    
    async def notify_budget_threshold(
        self,
        client_id: str,
        campaign_id: str,
        current_spend: float,
        budget_limit: float,
        threshold_percentage: float
    ):
        """Notify about budget threshold alerts"""
        await self.connection_manager.send_alert(client_id, {
            "alert_id": f"budget_{campaign_id}_{int(datetime.utcnow().timestamp())}",
            "title": "Budget Threshold Alert",
            "message": f"Campaign has used {threshold_percentage}% of budget",
            "severity": "warning" if threshold_percentage < 90 else "critical",
            "category": "budget",
            "campaign_id": campaign_id,
            "recommended_actions": [
                "Review campaign performance",
                "Consider budget adjustment",
                "Pause low-performing ads"
            ]
        })
    
    async def notify_performance_anomaly(
        self,
        client_id: str,
        campaign_id: str,
        metric_name: str,
        anomaly_type: str,
        severity: str
    ):
        """Notify about performance anomalies"""
        await self.connection_manager.send_alert(client_id, {
            "alert_id": f"anomaly_{campaign_id}_{int(datetime.utcnow().timestamp())}",
            "title": f"Performance Anomaly: {metric_name}",
            "message": f"Detected {anomaly_type} in {metric_name}",
            "severity": severity,
            "category": "performance",
            "campaign_id": campaign_id,
            "recommended_actions": [
                "Investigate recent changes",
                "Check competitor activity",
                "Review targeting settings"
            ]
        })

# Global notification service
notification_service = WebSocketNotificationService(connection_manager)

async def start_background_tasks():
    """Start background tasks for WebSocket management"""
    
    async def ping_task():
        """Background task to ping all connections"""
        while True:
            await asyncio.sleep(30)  # Ping every 30 seconds
            await connection_manager.ping_all_connections()
    
    async def metrics_broadcast_task():
        """Background task to broadcast periodic metric updates"""
        while True:
            await asyncio.sleep(60)  # Update every minute
            
            # Get current metrics for all clients
            for client_id in connection_manager.active_connections.keys():
                try:
                    # This would fetch real metrics from database
                    sample_metrics = {
                        "total_spend": 15420.50,
                        "total_revenue": 48650.25,
                        "current_roas": 3.15,
                        "current_cac": 42.30,
                        "ai_decisions_today": 23,
                        "last_updated": datetime.utcnow().isoformat()
                    }
                    
                    await connection_manager.send_metric_update(client_id, sample_metrics)
                    
                except Exception as e:
                    logger.error("Failed to broadcast metrics", error=str(e), client_id=client_id)
    
    # Start background tasks
    asyncio.create_task(ping_task())
    asyncio.create_task(metrics_broadcast_task())
