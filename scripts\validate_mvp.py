#!/usr/bin/env python3
"""
MVP Validation Script for Omnify Marketing Cloud
Comprehensive testing of all MVP features and user journeys
"""
import asyncio
import httpx
import json
from datetime import datetime
from typing import Dict, Any, List
import structlog

logger = structlog.get_logger()

class MVPValidator:
    """Validates MVP functionality end-to-end"""
    
    def __init__(self, base_url: str = "http://localhost:8000/api/v1"):
        self.base_url = base_url
        self.test_results = []
        self.access_token = None
        self.client_id = None
    
    async def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.utcnow().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details}")
    
    async def test_system_health(self) -> bool:
        """Test system health and availability"""
        try:
            async with httpx.AsyncClient() as client:
                # Test main health endpoint
                response = await client.get(f"{self.base_url}/health")
                if response.status_code != 200:
                    await self.log_test_result(
                        "System Health", 
                        False, 
                        f"Health endpoint returned {response.status_code}"
                    )
                    return False
                
                # Test database connectivity
                health_data = response.json()
                if health_data.get("database") != "connected":
                    await self.log_test_result(
                        "Database Health", 
                        False, 
                        "Database not connected"
                    )
                    return False
                
                await self.log_test_result("System Health", True, "All systems operational")
                return True
                
        except Exception as e:
            await self.log_test_result("System Health", False, f"Error: {str(e)}")
            return False
    
    async def test_user_registration_and_login(self) -> bool:
        """Test user registration and authentication"""
        try:
            async with httpx.AsyncClient() as client:
                # Test user registration
                registration_data = {
                    "email": "<EMAIL>",
                    "password": "TestPassword123!",
                    "first_name": "MVP",
                    "last_name": "Test",
                    "company_name": "MVP Test Company"
                }
                
                response = await client.post(
                    f"{self.base_url}/auth/register",
                    json=registration_data
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "User Registration", 
                        False, 
                        f"Registration failed: {response.status_code}"
                    )
                    return False
                
                user_data = response.json()
                self.client_id = user_data.get("client_id")
                
                # Test login
                login_data = {
                    "email": "<EMAIL>",
                    "password": "TestPassword123!"
                }
                
                response = await client.post(
                    f"{self.base_url}/auth/login",
                    json=login_data
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "User Login", 
                        False, 
                        f"Login failed: {response.status_code}"
                    )
                    return False
                
                login_response = response.json()
                self.access_token = login_response.get("access_token")
                
                if not self.access_token:
                    await self.log_test_result(
                        "User Login", 
                        False, 
                        "No access token received"
                    )
                    return False
                
                await self.log_test_result(
                    "User Registration & Login", 
                    True, 
                    f"User created with client_id: {self.client_id}"
                )
                return True
                
        except Exception as e:
            await self.log_test_result(
                "User Registration & Login", 
                False, 
                f"Error: {str(e)}"
            )
            return False
    
    async def test_dashboard_apis(self) -> bool:
        """Test dashboard API endpoints"""
        if not self.access_token or not self.client_id:
            await self.log_test_result(
                "Dashboard APIs", 
                False, 
                "No authentication token available"
            )
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with httpx.AsyncClient() as client:
                # Test dashboard overview
                response = await client.get(
                    f"{self.base_url}/dashboard/overview?client_id={self.client_id}",
                    headers=headers
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "Dashboard Overview", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                overview_data = response.json()
                required_fields = ["total_spend", "total_revenue", "current_roas", "current_cac"]
                
                for field in required_fields:
                    if field not in overview_data:
                        await self.log_test_result(
                            "Dashboard Overview", 
                            False, 
                            f"Missing field: {field}"
                        )
                        return False
                
                # Test campaigns endpoint
                response = await client.get(
                    f"{self.base_url}/dashboard/campaigns?client_id={self.client_id}",
                    headers=headers
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "Dashboard Campaigns", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                # Test AI agents endpoint
                response = await client.get(
                    f"{self.base_url}/dashboard/ai-agents?client_id={self.client_id}",
                    headers=headers
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "AI Agents Status", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                ai_data = response.json()
                required_agents = ["roi_engine", "retention_reactor", "engage_sense"]
                
                for agent in required_agents:
                    if agent not in ai_data:
                        await self.log_test_result(
                            "AI Agents Status", 
                            False, 
                            f"Missing agent: {agent}"
                        )
                        return False
                
                await self.log_test_result(
                    "Dashboard APIs", 
                    True, 
                    "All dashboard endpoints working"
                )
                return True
                
        except Exception as e:
            await self.log_test_result(
                "Dashboard APIs", 
                False, 
                f"Error: {str(e)}"
            )
            return False
    
    async def test_ai_agents(self) -> bool:
        """Test AI agents functionality"""
        if not self.access_token or not self.client_id:
            await self.log_test_result(
                "AI Agents", 
                False, 
                "No authentication token available"
            )
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with httpx.AsyncClient() as client:
                # Test AI analysis trigger
                analysis_data = {
                    "client_id": self.client_id,
                    "agent": "roi_engine",
                    "campaign_ids": [],
                    "force_analysis": True
                }
                
                response = await client.post(
                    f"{self.base_url}/agents/analyze",
                    json=analysis_data,
                    headers=headers
                )
                
                if response.status_code not in [200, 202]:
                    await self.log_test_result(
                        "AI Analysis Trigger", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                await self.log_test_result(
                    "AI Agents", 
                    True, 
                    "AI analysis can be triggered"
                )
                return True
                
        except Exception as e:
            await self.log_test_result(
                "AI Agents", 
                False, 
                f"Error: {str(e)}"
            )
            return False
    
    async def test_onboarding_process(self) -> bool:
        """Test onboarding process"""
        if not self.access_token or not self.client_id:
            await self.log_test_result(
                "Onboarding Process", 
                False, 
                "No authentication token available"
            )
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with httpx.AsyncClient() as client:
                # Test onboarding start
                onboarding_data = {
                    "client_id": self.client_id,
                    "google_ads_credentials": {
                        "customer_id": "123-456-7890",
                        "developer_token": "test_token"
                    },
                    "meta_ads_credentials": {
                        "account_id": "act_123456789",
                        "access_token": "test_token"
                    }
                }
                
                response = await client.post(
                    f"{self.base_url}/onboarding/start",
                    json=onboarding_data,
                    headers=headers
                )
                
                if response.status_code not in [200, 202]:
                    await self.log_test_result(
                        "Onboarding Start", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                onboarding_response = response.json()
                process_id = onboarding_response.get("process_id")
                
                if not process_id:
                    await self.log_test_result(
                        "Onboarding Start", 
                        False, 
                        "No process_id returned"
                    )
                    return False
                
                # Test onboarding status
                response = await client.get(
                    f"{self.base_url}/onboarding/status/{process_id}",
                    headers=headers
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "Onboarding Status", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                await self.log_test_result(
                    "Onboarding Process", 
                    True, 
                    f"Process started with ID: {process_id}"
                )
                return True
                
        except Exception as e:
            await self.log_test_result(
                "Onboarding Process", 
                False, 
                f"Error: {str(e)}"
            )
            return False
    
    async def test_system_endpoints(self) -> bool:
        """Test system management endpoints"""
        if not self.access_token:
            await self.log_test_result(
                "System Endpoints", 
                False, 
                "No authentication token available"
            )
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with httpx.AsyncClient() as client:
                # Test system status
                response = await client.get(
                    f"{self.base_url}/system/status",
                    headers=headers
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "System Status", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                # Test cache stats
                response = await client.get(
                    f"{self.base_url}/system/cache/stats",
                    headers=headers
                )
                
                if response.status_code != 200:
                    await self.log_test_result(
                        "Cache Stats", 
                        False, 
                        f"API returned {response.status_code}"
                    )
                    return False
                
                await self.log_test_result(
                    "System Endpoints", 
                    True, 
                    "System management endpoints working"
                )
                return True
                
        except Exception as e:
            await self.log_test_result(
                "System Endpoints", 
                False, 
                f"Error: {str(e)}"
            )
            return False
    
    async def run_full_validation(self) -> Dict[str, Any]:
        """Run complete MVP validation"""
        print("🚀 Starting Omnify Marketing Cloud MVP Validation...")
        print("=" * 60)
        
        # Run all tests
        tests = [
            ("System Health", self.test_system_health),
            ("User Registration & Login", self.test_user_registration_and_login),
            ("Dashboard APIs", self.test_dashboard_apis),
            ("AI Agents", self.test_ai_agents),
            ("Onboarding Process", self.test_onboarding_process),
            ("System Endpoints", self.test_system_endpoints)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                success = await test_func()
                if success:
                    passed_tests += 1
            except Exception as e:
                await self.log_test_result(test_name, False, f"Exception: {str(e)}")
        
        # Generate summary
        success_rate = (passed_tests / total_tests) * 100
        
        print("\n" + "=" * 60)
        print("📊 MVP Validation Summary")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("\n🎉 MVP is READY for pilot clients!")
            mvp_status = "READY"
        elif success_rate >= 70:
            print("\n⚠️  MVP needs minor fixes before launch")
            mvp_status = "NEEDS_FIXES"
        else:
            print("\n❌ MVP has critical issues that must be resolved")
            mvp_status = "NOT_READY"
        
        # Show failed tests
        failed_tests = [r for r in self.test_results if not r["success"]]
        if failed_tests:
            print("\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"  - {test['test_name']}: {test['details']}")
        
        return {
            "status": mvp_status,
            "success_rate": success_rate,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "test_results": self.test_results,
            "timestamp": datetime.utcnow().isoformat()
        }

async def main():
    """Main validation function"""
    validator = MVPValidator()
    
    try:
        results = await validator.run_full_validation()
        
        # Save results to file
        with open("mvp_validation_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: mvp_validation_results.json")
        
        # Return appropriate exit code
        return results["status"] == "READY"
        
    except Exception as e:
        print(f"❌ Validation failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
