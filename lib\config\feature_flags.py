"""
Feature Flags and Configuration Management for Omnify Marketing Cloud
"""
import os
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum
import structlog
from pydantic import BaseModel, Field

logger = structlog.get_logger()

class FeatureFlag(BaseModel):
    """Feature flag model"""
    name: str
    enabled: bool
    description: str
    rollout_percentage: float = Field(default=100.0, ge=0.0, le=100.0)
    client_whitelist: List[str] = Field(default_factory=list)
    client_blacklist: List[str] = Field(default_factory=list)
    environment_restrictions: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class ConfigurationLevel(str, Enum):
    """Configuration levels"""
    GLOBAL = "global"
    CLIENT = "client"
    CAMPAIGN = "campaign"
    USER = "user"

class Configuration(BaseModel):
    """Configuration model"""
    key: str
    value: Any
    level: ConfigurationLevel
    entity_id: Optional[str] = None  # client_id, campaign_id, user_id
    description: str
    data_type: str = "string"  # string, int, float, bool, json
    is_sensitive: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class FeatureFlagManager:
    """Manages feature flags and configuration"""
    
    def __init__(self):
        self.feature_flags: Dict[str, FeatureFlag] = {}
        self.configurations: Dict[str, Configuration] = {}
        self._load_default_flags()
        self._load_default_configurations()
    
    def _load_default_flags(self):
        """Load default feature flags"""
        default_flags = [
            FeatureFlag(
                name="enable_manus_rl",
                enabled=os.getenv("ENABLE_MANUS_RL", "true").lower() == "true",
                description="Enable Manus RL for AI decision making",
                rollout_percentage=100.0
            ),
            FeatureFlag(
                name="enable_advanced_analytics",
                enabled=os.getenv("ENABLE_ADVANCED_ANALYTICS", "true").lower() == "true",
                description="Enable advanced analytics features",
                rollout_percentage=100.0
            ),
            FeatureFlag(
                name="enable_real_time_bidding",
                enabled=os.getenv("ENABLE_REAL_TIME_BIDDING", "false").lower() == "true",
                description="Enable real-time bid adjustments",
                rollout_percentage=50.0  # Gradual rollout
            ),
            FeatureFlag(
                name="enable_churn_prediction",
                enabled=os.getenv("ENABLE_CHURN_PREDICTION", "true").lower() == "true",
                description="Enable churn prediction features",
                rollout_percentage=100.0
            ),
            FeatureFlag(
                name="enable_personalization",
                enabled=os.getenv("ENABLE_PERSONALIZATION", "true").lower() == "true",
                description="Enable personalization engine",
                rollout_percentage=100.0
            ),
            FeatureFlag(
                name="enable_auto_budget_adjustment",
                enabled=os.getenv("ENABLE_AUTO_BUDGET_ADJUSTMENT", "false").lower() == "true",
                description="Enable automatic budget adjustments",
                rollout_percentage=25.0,  # Limited rollout
                environment_restrictions=["production"]
            ),
            FeatureFlag(
                name="enable_advanced_alerts",
                enabled=True,
                description="Enable advanced alerting system",
                rollout_percentage=100.0
            ),
            FeatureFlag(
                name="enable_bulk_import",
                enabled=True,
                description="Enable bulk data import features",
                rollout_percentage=100.0
            ),
            FeatureFlag(
                name="enable_api_rate_limiting",
                enabled=True,
                description="Enable API rate limiting",
                rollout_percentage=100.0
            ),
            FeatureFlag(
                name="enable_detailed_logging",
                enabled=os.getenv("ENVIRONMENT", "development") == "development",
                description="Enable detailed debug logging",
                rollout_percentage=100.0
            )
        ]
        
        for flag in default_flags:
            self.feature_flags[flag.name] = flag
    
    def _load_default_configurations(self):
        """Load default configurations"""
        default_configs = [
            Configuration(
                key="ai_confidence_threshold",
                value=float(os.getenv("AI_CONFIDENCE_THRESHOLD", "0.85")),
                level=ConfigurationLevel.GLOBAL,
                description="Minimum confidence threshold for AI decisions",
                data_type="float"
            ),
            Configuration(
                key="max_bid_adjustment",
                value=float(os.getenv("DEFAULT_BID_ADJUSTMENT_LIMIT", "0.25")),
                level=ConfigurationLevel.GLOBAL,
                description="Maximum bid adjustment percentage",
                data_type="float"
            ),
            Configuration(
                key="min_campaign_budget",
                value=float(os.getenv("MIN_CAMPAIGN_BUDGET", "100.0")),
                level=ConfigurationLevel.GLOBAL,
                description="Minimum campaign budget",
                data_type="float"
            ),
            Configuration(
                key="max_campaign_budget",
                value=float(os.getenv("MAX_CAMPAIGN_BUDGET", "10000.0")),
                level=ConfigurationLevel.GLOBAL,
                description="Maximum campaign budget",
                data_type="float"
            ),
            Configuration(
                key="churn_prediction_threshold",
                value=0.8,
                level=ConfigurationLevel.GLOBAL,
                description="Churn probability threshold for alerts",
                data_type="float"
            ),
            Configuration(
                key="engagement_score_weights",
                value={
                    "engagement": 0.3,
                    "purchase_intent": 0.35,
                    "loyalty": 0.25,
                    "digital_adoption": 0.1
                },
                level=ConfigurationLevel.GLOBAL,
                description="Weights for engagement score calculation",
                data_type="json"
            ),
            Configuration(
                key="alert_cooldown_period",
                value=3600,  # 1 hour
                level=ConfigurationLevel.GLOBAL,
                description="Cooldown period between similar alerts (seconds)",
                data_type="int"
            ),
            Configuration(
                key="data_retention_days",
                value=int(os.getenv("DATA_RETENTION_DAYS", "2555")),  # 7 years
                level=ConfigurationLevel.GLOBAL,
                description="Data retention period in days",
                data_type="int"
            ),
            Configuration(
                key="api_rate_limit_per_minute",
                value=int(os.getenv("RATE_LIMIT_PER_MINUTE", "60")),
                level=ConfigurationLevel.GLOBAL,
                description="API rate limit per minute",
                data_type="int"
            ),
            Configuration(
                key="email_notification_enabled",
                value=True,
                level=ConfigurationLevel.GLOBAL,
                description="Enable email notifications",
                data_type="bool"
            )
        ]
        
        for config in default_configs:
            config_key = f"{config.level.value}:{config.entity_id or 'default'}:{config.key}"
            self.configurations[config_key] = config
    
    def is_feature_enabled(
        self, 
        feature_name: str, 
        client_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> bool:
        """Check if a feature is enabled for the given context"""
        
        flag = self.feature_flags.get(feature_name)
        if not flag:
            logger.warning(f"Feature flag not found: {feature_name}")
            return False
        
        # Check if globally disabled
        if not flag.enabled:
            return False
        
        # Check environment restrictions
        current_env = os.getenv("ENVIRONMENT", "development")
        if flag.environment_restrictions and current_env not in flag.environment_restrictions:
            return False
        
        # Check client blacklist
        if client_id and client_id in flag.client_blacklist:
            return False
        
        # Check client whitelist (if specified, only whitelisted clients get the feature)
        if flag.client_whitelist and client_id and client_id not in flag.client_whitelist:
            return False
        
        # Check rollout percentage
        if flag.rollout_percentage < 100.0:
            # Use deterministic hash for consistent rollout
            hash_input = f"{feature_name}:{client_id or user_id or 'anonymous'}"
            hash_value = hash(hash_input) % 100
            if hash_value >= flag.rollout_percentage:
                return False
        
        return True
    
    def get_configuration(
        self, 
        key: str, 
        level: ConfigurationLevel = ConfigurationLevel.GLOBAL,
        entity_id: Optional[str] = None,
        default: Any = None
    ) -> Any:
        """Get configuration value with fallback hierarchy"""
        
        # Try specific entity first (if provided)
        if entity_id:
            specific_key = f"{level.value}:{entity_id}:{key}"
            config = self.configurations.get(specific_key)
            if config:
                return self._convert_value(config.value, config.data_type)
        
        # Fall back to default for this level
        default_key = f"{level.value}:default:{key}"
        config = self.configurations.get(default_key)
        if config:
            return self._convert_value(config.value, config.data_type)
        
        # Fall back to global default
        if level != ConfigurationLevel.GLOBAL:
            global_key = f"{ConfigurationLevel.GLOBAL.value}:default:{key}"
            config = self.configurations.get(global_key)
            if config:
                return self._convert_value(config.value, config.data_type)
        
        # Return provided default
        return default
    
    def set_configuration(
        self,
        key: str,
        value: Any,
        level: ConfigurationLevel = ConfigurationLevel.GLOBAL,
        entity_id: Optional[str] = None,
        description: str = "",
        data_type: str = "string"
    ):
        """Set configuration value"""
        
        config_key = f"{level.value}:{entity_id or 'default'}:{key}"
        
        config = Configuration(
            key=key,
            value=value,
            level=level,
            entity_id=entity_id,
            description=description,
            data_type=data_type,
            updated_at=datetime.utcnow()
        )
        
        self.configurations[config_key] = config
        
        logger.info(
            "Configuration updated",
            key=key,
            level=level.value,
            entity_id=entity_id,
            value=value if not config.is_sensitive else "[REDACTED]"
        )
    
    def _convert_value(self, value: Any, data_type: str) -> Any:
        """Convert configuration value to appropriate type"""
        if data_type == "int":
            return int(value)
        elif data_type == "float":
            return float(value)
        elif data_type == "bool":
            if isinstance(value, str):
                return value.lower() in ("true", "1", "yes", "on")
            return bool(value)
        elif data_type == "json":
            if isinstance(value, str):
                return json.loads(value)
            return value
        else:  # string
            return str(value)
    
    def update_feature_flag(
        self,
        name: str,
        enabled: Optional[bool] = None,
        rollout_percentage: Optional[float] = None,
        client_whitelist: Optional[List[str]] = None,
        client_blacklist: Optional[List[str]] = None
    ):
        """Update feature flag settings"""
        
        flag = self.feature_flags.get(name)
        if not flag:
            raise ValueError(f"Feature flag not found: {name}")
        
        if enabled is not None:
            flag.enabled = enabled
        
        if rollout_percentage is not None:
            flag.rollout_percentage = rollout_percentage
        
        if client_whitelist is not None:
            flag.client_whitelist = client_whitelist
        
        if client_blacklist is not None:
            flag.client_blacklist = client_blacklist
        
        flag.updated_at = datetime.utcnow()
        
        logger.info(
            "Feature flag updated",
            name=name,
            enabled=flag.enabled,
            rollout_percentage=flag.rollout_percentage
        )
    
    def get_all_feature_flags(self) -> Dict[str, FeatureFlag]:
        """Get all feature flags"""
        return self.feature_flags.copy()
    
    def get_all_configurations(
        self, 
        level: Optional[ConfigurationLevel] = None,
        entity_id: Optional[str] = None
    ) -> Dict[str, Configuration]:
        """Get all configurations with optional filtering"""
        
        configs = {}
        
        for key, config in self.configurations.items():
            if level and config.level != level:
                continue
            
            if entity_id and config.entity_id != entity_id:
                continue
            
            # Don't expose sensitive values
            if config.is_sensitive:
                config_copy = config.copy()
                config_copy.value = "[REDACTED]"
                configs[key] = config_copy
            else:
                configs[key] = config
        
        return configs

# Global feature flag manager instance
feature_manager = FeatureFlagManager()

# Convenience functions
def is_feature_enabled(feature_name: str, client_id: Optional[str] = None) -> bool:
    """Check if feature is enabled"""
    return feature_manager.is_feature_enabled(feature_name, client_id)

def get_config(key: str, default: Any = None, client_id: Optional[str] = None) -> Any:
    """Get configuration value"""
    return feature_manager.get_configuration(
        key, 
        ConfigurationLevel.CLIENT if client_id else ConfigurationLevel.GLOBAL,
        client_id,
        default
    )
