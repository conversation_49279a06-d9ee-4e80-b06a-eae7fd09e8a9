"""
Retry utilities with exponential backoff and circuit breaker pattern
"""
import asyncio
import time
from functools import wraps
from typing import Callable, Any, Optional, Type, Tuple
import structlog
from prometheus_client import Counter, Histogram

logger = structlog.get_logger()

# Prometheus metrics
retry_attempts_total = Counter(
    'retry_attempts_total',
    'Total number of retry attempts',
    ['function_name', 'attempt']
)

retry_duration_seconds = Histogram(
    'retry_duration_seconds',
    'Time spent in retry operations',
    ['function_name']
)

circuit_breaker_state = Counter(
    'circuit_breaker_state_total',
    'Circuit breaker state changes',
    ['function_name', 'state']
)

class CircuitBreakerError(Exception):
    """Raised when circuit breaker is open"""
    pass

class CircuitBreaker:
    """Circuit breaker implementation"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
    
    def __call__(self, func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if self.state == "open":
                if self._should_attempt_reset():
                    self.state = "half_open"
                    circuit_breaker_state.labels(
                        function_name=func.__name__,
                        state="half_open"
                    ).inc()
                else:
                    raise CircuitBreakerError(f"Circuit breaker is open for {func.__name__}")
            
            try:
                result = await func(*args, **kwargs)
                self._on_success(func.__name__)
                return result
            except self.expected_exception as e:
                self._on_failure(func.__name__)
                raise
        
        return wrapper
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        return (
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self, func_name: str):
        """Handle successful execution"""
        self.failure_count = 0
        if self.state == "half_open":
            self.state = "closed"
            circuit_breaker_state.labels(
                function_name=func_name,
                state="closed"
            ).inc()
    
    def _on_failure(self, func_name: str):
        """Handle failed execution"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            circuit_breaker_state.labels(
                function_name=func_name,
                state="open"
            ).inc()

def retry_with_backoff(
    max_retries: int = 3,
    backoff_factor: float = 2.0,
    max_backoff: float = 60.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,)
):
    """
    Decorator for retry logic with exponential backoff
    
    Args:
        max_retries: Maximum number of retry attempts
        backoff_factor: Multiplier for backoff delay
        max_backoff: Maximum backoff delay in seconds
        exceptions: Tuple of exceptions to retry on
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            with retry_duration_seconds.labels(function_name=func.__name__).time():
                for attempt in range(max_retries + 1):
                    try:
                        retry_attempts_total.labels(
                            function_name=func.__name__,
                            attempt=str(attempt)
                        ).inc()
                        
                        result = await func(*args, **kwargs)
                        
                        if attempt > 0:
                            logger.info(
                                "Function succeeded after retries",
                                function=func.__name__,
                                attempt=attempt
                            )
                        
                        return result
                        
                    except exceptions as e:
                        last_exception = e
                        
                        if attempt == max_retries:
                            logger.error(
                                "Function failed after all retries",
                                function=func.__name__,
                                attempts=attempt + 1,
                                error=str(e)
                            )
                            break
                        
                        # Calculate backoff delay
                        delay = min(
                            backoff_factor ** attempt,
                            max_backoff
                        )
                        
                        logger.warning(
                            "Function failed, retrying",
                            function=func.__name__,
                            attempt=attempt,
                            delay=delay,
                            error=str(e)
                        )
                        
                        await asyncio.sleep(delay)
            
            # Re-raise the last exception if all retries failed
            if last_exception:
                raise last_exception
        
        return wrapper
    return decorator

def with_circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    expected_exception: Type[Exception] = Exception
):
    """
    Decorator to add circuit breaker pattern to a function
    """
    def decorator(func: Callable) -> Callable:
        circuit_breaker = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            expected_exception=expected_exception
        )
        return circuit_breaker(func)
    
    return decorator

async def exponential_backoff(
    attempt: int,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    jitter: bool = True
) -> None:
    """
    Perform exponential backoff delay
    
    Args:
        attempt: Current attempt number (0-based)
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        jitter: Whether to add random jitter
    """
    import random
    
    delay = min(base_delay * (2 ** attempt), max_delay)
    
    if jitter:
        # Add ±25% jitter to prevent thundering herd
        jitter_range = delay * 0.25
        delay += random.uniform(-jitter_range, jitter_range)
    
    delay = max(0, delay)  # Ensure non-negative
    await asyncio.sleep(delay)
