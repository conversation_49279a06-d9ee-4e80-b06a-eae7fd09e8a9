"""
Real-time Dashboard API endpoints for Retool integration
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from pydantic import BaseModel, Field
import structlog

from apps.core.database import (
    get_db, Client, Campaign, CampaignMetric, AIDecision, 
    CustomerProfile, RetentionAction
)
from apps.auth.dependencies import get_current_user

logger = structlog.get_logger()

router = APIRouter()

class DashboardOverview(BaseModel):
    """Main dashboard overview data"""
    client_id: str
    period_start: datetime
    period_end: datetime
    
    # Key Metrics
    total_spend: float
    total_revenue: float
    total_conversions: int
    current_roas: float
    current_cac: float
    
    # Performance vs Targets
    roas_vs_target: float  # percentage
    cac_vs_target: float   # percentage
    
    # AI Agent Activity
    ai_decisions_today: int
    successful_optimizations: int
    estimated_savings: float
    
    # Customer Health
    total_customers: int
    at_risk_customers: int
    champion_customers: int
    churn_prevented: int
    
    # Trends
    roas_trend: str  # "improving", "stable", "declining"
    cac_trend: str
    engagement_trend: str

class CampaignPerformance(BaseModel):
    """Individual campaign performance"""
    campaign_id: str
    campaign_name: str
    platform: str
    status: str
    
    # Metrics
    spend: float
    revenue: float
    conversions: int
    roas: float
    cac: float
    
    # AI Optimization
    last_optimization: Optional[datetime]
    optimization_impact: Optional[float]
    ai_confidence: Optional[float]
    
    # Alerts
    has_alerts: bool
    alert_level: str  # "none", "warning", "critical"

class AIAgentStatus(BaseModel):
    """AI agent status and performance"""
    agent_name: str
    status: str  # "active", "idle", "error"
    last_run: datetime
    decisions_today: int
    success_rate: float
    avg_confidence: float
    impact_metrics: Dict[str, float]

class CustomerSegmentData(BaseModel):
    """Customer segmentation data"""
    segment: str
    count: int
    percentage: float
    avg_engagement_score: float
    avg_lifetime_value: float
    churn_risk: float

class AlertData(BaseModel):
    """System alerts and notifications"""
    alert_id: str
    type: str  # "performance", "budget", "churn", "system"
    severity: str  # "info", "warning", "critical"
    title: str
    message: str
    timestamp: datetime
    is_resolved: bool
    affected_campaigns: List[str] = Field(default_factory=list)

@router.get("/overview", response_model=DashboardOverview)
async def get_dashboard_overview(
    client_id: str = Query(..., description="Client ID"),
    days: int = Query(7, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get comprehensive dashboard overview for Retool
    """
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get campaign metrics
        metrics_query = (
            select(
                func.sum(CampaignMetric.spend).label('total_spend'),
                func.sum(CampaignMetric.revenue).label('total_revenue'),
                func.sum(CampaignMetric.conversions).label('total_conversions'),
                func.count(CampaignMetric.id).label('metric_count')
            )
            .join(Campaign)
            .join(Client)
            .where(
                Client.id == client_id,
                CampaignMetric.date >= start_date,
                CampaignMetric.date <= end_date
            )
        )
        
        metrics_result = await db.execute(metrics_query)
        metrics = metrics_result.fetchone()
        
        if not metrics or not metrics.total_spend:
            raise HTTPException(status_code=404, detail="No metrics found for client")
        
        # Calculate key metrics
        total_spend = float(metrics.total_spend or 0)
        total_revenue = float(metrics.total_revenue or 0)
        total_conversions = int(metrics.total_conversions or 0)
        current_roas = total_revenue / total_spend if total_spend > 0 else 0.0
        current_cac = total_spend / total_conversions if total_conversions > 0 else 0.0
        
        # Get client targets
        client_query = await db.execute(select(Client).where(Client.id == client_id))
        client = client_query.scalar_one_or_none()
        
        # Calculate performance vs targets
        target_roas = 2.5  # Default target
        target_cac = 50.0  # Default target
        
        roas_vs_target = ((current_roas - target_roas) / target_roas * 100) if target_roas > 0 else 0
        cac_vs_target = ((current_cac - target_cac) / target_cac * 100) if target_cac > 0 else 0
        
        # Get AI decisions today
        today = datetime.utcnow().date()
        ai_decisions_query = (
            select(
                func.count(AIDecision.id).label('decisions_today'),
                func.sum(
                    func.case((AIDecision.result == 'success', 1), else_=0)
                ).label('successful_optimizations')
            )
            .where(
                AIDecision.client_id == client_id,
                func.date(AIDecision.created_at) == today
            )
        )
        
        ai_result = await db.execute(ai_decisions_query)
        ai_data = ai_result.fetchone()
        
        ai_decisions_today = int(ai_data.decisions_today or 0)
        successful_optimizations = int(ai_data.successful_optimizations or 0)
        
        # Estimate savings (simplified calculation)
        estimated_savings = successful_optimizations * 150.0  # $150 avg savings per optimization
        
        # Get customer data
        customer_query = (
            select(
                func.count(CustomerProfile.id).label('total_customers'),
                func.sum(
                    func.case((CustomerProfile.risk_level == 'high', 1), else_=0)
                ).label('at_risk_customers'),
                func.sum(
                    func.case((CustomerProfile.segment == 'champion', 1), else_=0)
                ).label('champion_customers')
            )
            .where(CustomerProfile.client_id == client_id)
        )
        
        customer_result = await db.execute(customer_query)
        customer_data = customer_result.fetchone()
        
        total_customers = int(customer_data.total_customers or 0)
        at_risk_customers = int(customer_data.at_risk_customers or 0)
        champion_customers = int(customer_data.champion_customers or 0)
        
        # Get churn prevention count
        churn_prevented_query = (
            select(func.count(RetentionAction.id))
            .where(
                RetentionAction.client_id == client_id,
                RetentionAction.success == True,
                func.date(RetentionAction.created_at) >= today - timedelta(days=7)
            )
        )
        
        churn_result = await db.execute(churn_prevented_query)
        churn_prevented = int(churn_result.scalar() or 0)
        
        # Calculate trends (simplified)
        roas_trend = "improving" if roas_vs_target > 5 else "declining" if roas_vs_target < -5 else "stable"
        cac_trend = "improving" if cac_vs_target < -5 else "declining" if cac_vs_target > 5 else "stable"
        engagement_trend = "improving"  # Would calculate from engagement scores
        
        logger.info(
            "Dashboard overview generated",
            client_id=client_id,
            total_spend=total_spend,
            current_roas=current_roas,
            ai_decisions_today=ai_decisions_today
        )
        
        return DashboardOverview(
            client_id=client_id,
            period_start=start_date,
            period_end=end_date,
            total_spend=total_spend,
            total_revenue=total_revenue,
            total_conversions=total_conversions,
            current_roas=current_roas,
            current_cac=current_cac,
            roas_vs_target=roas_vs_target,
            cac_vs_target=cac_vs_target,
            ai_decisions_today=ai_decisions_today,
            successful_optimizations=successful_optimizations,
            estimated_savings=estimated_savings,
            total_customers=total_customers,
            at_risk_customers=at_risk_customers,
            champion_customers=champion_customers,
            churn_prevented=churn_prevented,
            roas_trend=roas_trend,
            cac_trend=cac_trend,
            engagement_trend=engagement_trend
        )
        
    except Exception as e:
        logger.error("Failed to generate dashboard overview", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to generate dashboard overview")

@router.get("/campaigns", response_model=List[CampaignPerformance])
async def get_campaign_performance(
    client_id: str = Query(..., description="Client ID"),
    days: int = Query(7, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get individual campaign performance data
    """
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get campaigns with aggregated metrics
        campaigns_query = (
            select(
                Campaign.id,
                Campaign.name,
                Campaign.platform,
                Campaign.status,
                Campaign.campaign_id,
                func.sum(CampaignMetric.spend).label('total_spend'),
                func.sum(CampaignMetric.revenue).label('total_revenue'),
                func.sum(CampaignMetric.conversions).label('total_conversions')
            )
            .join(CampaignMetric, Campaign.id == CampaignMetric.campaign_id)
            .where(
                Campaign.client_id == client_id,
                CampaignMetric.date >= start_date,
                CampaignMetric.date <= end_date
            )
            .group_by(Campaign.id, Campaign.name, Campaign.platform, Campaign.status, Campaign.campaign_id)
        )
        
        campaigns_result = await db.execute(campaigns_query)
        campaigns = campaigns_result.fetchall()
        
        campaign_performances = []
        
        for campaign in campaigns:
            # Calculate metrics
            spend = float(campaign.total_spend or 0)
            revenue = float(campaign.total_revenue or 0)
            conversions = int(campaign.total_conversions or 0)
            roas = revenue / spend if spend > 0 else 0.0
            cac = spend / conversions if conversions > 0 else 0.0
            
            # Get latest AI decision for this campaign
            ai_decision_query = (
                select(AIDecision)
                .where(
                    AIDecision.client_id == client_id,
                    AIDecision.input_data['campaign_id'].astext == campaign.campaign_id
                )
                .order_by(AIDecision.created_at.desc())
                .limit(1)
            )
            
            ai_result = await db.execute(ai_decision_query)
            latest_decision = ai_result.scalar_one_or_none()
            
            # Determine alerts
            has_alerts = False
            alert_level = "none"
            
            if roas < 1.5:  # Critical ROAS threshold
                has_alerts = True
                alert_level = "critical"
            elif cac > 75:  # Warning CAC threshold
                has_alerts = True
                alert_level = "warning"
            
            campaign_performances.append(CampaignPerformance(
                campaign_id=campaign.campaign_id,
                campaign_name=campaign.name,
                platform=campaign.platform,
                status=campaign.status,
                spend=spend,
                revenue=revenue,
                conversions=conversions,
                roas=roas,
                cac=cac,
                last_optimization=latest_decision.created_at if latest_decision else None,
                optimization_impact=0.15 if latest_decision and latest_decision.result == 'success' else None,
                ai_confidence=latest_decision.confidence if latest_decision else None,
                has_alerts=has_alerts,
                alert_level=alert_level
            ))
        
        return campaign_performances
        
    except Exception as e:
        logger.error("Failed to get campaign performance", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to get campaign performance")

@router.get("/ai-agents", response_model=List[AIAgentStatus])
async def get_ai_agent_status(
    client_id: str = Query(..., description="Client ID"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get AI agent status and performance
    """
    try:
        agents = ["roi_engine", "retention_reactor", "engage_sense"]
        agent_statuses = []
        
        for agent in agents:
            # Get today's decisions for this agent
            today = datetime.utcnow().date()
            
            decisions_query = (
                select(
                    func.count(AIDecision.id).label('decisions_today'),
                    func.avg(AIDecision.confidence).label('avg_confidence'),
                    func.max(AIDecision.created_at).label('last_run'),
                    func.sum(
                        func.case((AIDecision.result == 'success', 1), else_=0)
                    ).label('successful_decisions')
                )
                .where(
                    AIDecision.client_id == client_id,
                    AIDecision.agent_type == agent,
                    func.date(AIDecision.created_at) == today
                )
            )
            
            result = await db.execute(decisions_query)
            data = result.fetchone()
            
            decisions_today = int(data.decisions_today or 0)
            avg_confidence = float(data.avg_confidence or 0)
            last_run = data.last_run or datetime.min
            successful_decisions = int(data.successful_decisions or 0)
            
            success_rate = (successful_decisions / decisions_today * 100) if decisions_today > 0 else 0
            
            # Determine status
            if decisions_today == 0:
                status = "idle"
            elif datetime.utcnow() - last_run < timedelta(hours=2):
                status = "active"
            else:
                status = "idle"
            
            # Calculate impact metrics (simplified)
            impact_metrics = {}
            if agent == "roi_engine":
                impact_metrics = {"cac_reduction": 12.5, "roas_improvement": 8.3}
            elif agent == "retention_reactor":
                impact_metrics = {"churn_prevented": 15, "retention_rate": 85.2}
            elif agent == "engage_sense":
                impact_metrics = {"engagement_lift": 23.1, "conversion_rate": 4.7}
            
            agent_statuses.append(AIAgentStatus(
                agent_name=agent.replace("_", " ").title(),
                status=status,
                last_run=last_run,
                decisions_today=decisions_today,
                success_rate=success_rate,
                avg_confidence=avg_confidence,
                impact_metrics=impact_metrics
            ))
        
        return agent_statuses
        
    except Exception as e:
        logger.error("Failed to get AI agent status", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to get AI agent status")

@router.get("/customer-segments", response_model=List[CustomerSegmentData])
async def get_customer_segments(
    client_id: str = Query(..., description="Client ID"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get customer segmentation data for EngageSense Ultra™
    """
    try:
        segments_query = (
            select(
                CustomerProfile.segment,
                func.count(CustomerProfile.id).label('count'),
                func.avg(CustomerProfile.engagement_score).label('avg_engagement'),
                func.avg(CustomerProfile.lifetime_value).label('avg_ltv'),
                func.avg(CustomerProfile.churn_probability).label('avg_churn_risk')
            )
            .where(CustomerProfile.client_id == client_id)
            .group_by(CustomerProfile.segment)
        )
        
        result = await db.execute(segments_query)
        segments = result.fetchall()
        
        # Get total customers for percentage calculation
        total_query = (
            select(func.count(CustomerProfile.id))
            .where(CustomerProfile.client_id == client_id)
        )
        total_result = await db.execute(total_query)
        total_customers = total_result.scalar() or 1
        
        segment_data = []
        for segment in segments:
            percentage = (segment.count / total_customers * 100) if total_customers > 0 else 0
            
            segment_data.append(CustomerSegmentData(
                segment=segment.segment or "unknown",
                count=segment.count,
                percentage=percentage,
                avg_engagement_score=float(segment.avg_engagement or 0),
                avg_lifetime_value=float(segment.avg_ltv or 0),
                churn_risk=float(segment.avg_churn_risk or 0) * 100
            ))
        
        return segment_data
        
    except Exception as e:
        logger.error("Failed to get customer segments", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to get customer segments")

@router.get("/alerts", response_model=List[AlertData])
async def get_system_alerts(
    client_id: str = Query(..., description="Client ID"),
    limit: int = Query(10, description="Maximum number of alerts"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get system alerts and notifications
    """
    try:
        alerts = []
        
        # Check for performance alerts
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=1)
        
        # Low ROAS campaigns
        low_roas_query = (
            select(Campaign.name, Campaign.campaign_id, func.avg(CampaignMetric.roas).label('avg_roas'))
            .join(CampaignMetric)
            .where(
                Campaign.client_id == client_id,
                CampaignMetric.date >= start_date,
                CampaignMetric.roas < 1.5
            )
            .group_by(Campaign.name, Campaign.campaign_id)
        )
        
        low_roas_result = await db.execute(low_roas_query)
        low_roas_campaigns = low_roas_result.fetchall()
        
        for campaign in low_roas_campaigns:
            alerts.append(AlertData(
                alert_id=f"low_roas_{campaign.campaign_id}",
                type="performance",
                severity="critical",
                title="Low ROAS Alert",
                message=f"Campaign '{campaign.name}' has ROAS of {campaign.avg_roas:.2f} (below 1.5 threshold)",
                timestamp=datetime.utcnow(),
                is_resolved=False,
                affected_campaigns=[campaign.campaign_id]
            ))
        
        # High churn risk customers
        high_churn_query = (
            select(func.count(CustomerProfile.id))
            .where(
                CustomerProfile.client_id == client_id,
                CustomerProfile.churn_probability > 0.8
            )
        )
        
        high_churn_result = await db.execute(high_churn_query)
        high_churn_count = high_churn_result.scalar() or 0
        
        if high_churn_count > 0:
            alerts.append(AlertData(
                alert_id=f"high_churn_{int(datetime.utcnow().timestamp())}",
                type="churn",
                severity="warning",
                title="High Churn Risk",
                message=f"{high_churn_count} customers have high churn probability (>80%)",
                timestamp=datetime.utcnow(),
                is_resolved=False
            ))
        
        # AI agent errors
        failed_decisions_query = (
            select(func.count(AIDecision.id))
            .where(
                AIDecision.client_id == client_id,
                AIDecision.result == 'failed',
                AIDecision.created_at >= start_date
            )
        )
        
        failed_result = await db.execute(failed_decisions_query)
        failed_count = failed_result.scalar() or 0
        
        if failed_count > 5:  # More than 5 failures in 24h
            alerts.append(AlertData(
                alert_id=f"ai_errors_{int(datetime.utcnow().timestamp())}",
                type="system",
                severity="warning",
                title="AI Agent Errors",
                message=f"{failed_count} AI decisions failed in the last 24 hours",
                timestamp=datetime.utcnow(),
                is_resolved=False
            ))
        
        # Sort by severity and timestamp
        severity_order = {"critical": 0, "warning": 1, "info": 2}
        alerts.sort(key=lambda x: (severity_order.get(x.severity, 3), x.timestamp), reverse=True)
        
        return alerts[:limit]
        
    except Exception as e:
        logger.error("Failed to get system alerts", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to get system alerts")
