#!/usr/bin/env python3
"""
Omnify Marketing Cloud - Universal Cross-Platform Demo
Works on Windows, macOS, and Linux with automatic port detection and Unicode handling
"""
import json
import random
import webbrowser
import time
import socket
import sys
import os
import platform
from datetime import datetime
from typing import Dict, Any, Optional

# Platform detection
IS_WINDOWS = platform.system() == "Windows"
IS_MACOS = platform.system() == "Darwin"
IS_LINUX = platform.system() == "Linux"

def setup_console_encoding():
    """Setup console encoding for cross-platform Unicode support"""
    if IS_WINDOWS:
        try:
            # Try to set UTF-8 encoding for Windows console
            os.system('chcp 65001 >nul 2>&1')
        except:
            pass

def safe_print(message: str, fallback: str = None):
    """Print with Unicode fallback for Windows compatibility"""
    try:
        print(message)
    except UnicodeEncodeError:
        if fallback:
            print(fallback)
        else:
            # Remove Unicode characters and print ASCII version
            ascii_message = message.encode('ascii', 'ignore').decode('ascii')
            print(ascii_message)

def find_available_port(start_port: int = 8000, max_attempts: int = 10) -> int:
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            # Test both localhost and 0.0.0.0 binding
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                s.bind(('0.0.0.0', port))
                return port
        except OSError:
            continue

    # If no port found, return a random high port
    return random.randint(8100, 8999)

# Install dependencies if needed
try:
    from fastapi import FastAPI
    from fastapi.responses import HTMLResponse
    import uvicorn
except ImportError:
    safe_print("📦 Installing required dependencies...", "Installing required dependencies...")
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn[standard]"],
                  capture_output=True)
    from fastapi import FastAPI
    from fastapi.responses import HTMLResponse
    import uvicorn

# Setup console encoding
setup_console_encoding()

app = FastAPI(title="Omnify Marketing Cloud Demo", version="1.0.0")

def generate_demo_data() -> Dict[str, Any]:
    """Generate realistic demo data"""
    return {
        "roi_engine": {
            "active_campaigns": 12,
            "optimizations_today": random.randint(45, 78),
            "roi_improvement": round(random.uniform(156, 312), 1),
            "revenue_impact": random.randint(8400, 15600),
            "confidence_score": random.randint(85, 95)
        },
        "retention_reactor": {
            "customers_monitored": random.randint(12800, 15200),
            "at_risk_customers": random.randint(67, 89),
            "retention_rate": round(random.uniform(76, 82), 1),
            "revenue_at_risk": random.randint(89000, 125000)
        },
        "engagesense": {
            "total_customers": random.randint(14500, 16800),
            "personalizations_today": random.randint(1200, 1800),
            "engagement_improvement": round(random.uniform(320, 380), 1),
            "conversion_lift": round(random.uniform(145, 175), 1)
        },
        "platform": {
            "total_revenue_impact": random.randint(47000, 68000),
            "ai_decisions_today": random.randint(1247, 1856),
            "uptime": "99.97%",
            "platform_os": platform.system(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "last_updated": datetime.now().strftime("%H:%M:%S")
        }
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Omnify Marketing Cloud Demo",
        "version": "1.0.0",
        "status": "healthy",
        "platform": platform.system(),
        "demo_dashboard": "/demo",
        "api_docs": "/docs"
    }

@app.get("/health")
async def health():
    """Health check"""
    return {
        "status": "healthy",
        "ai_agents": "active",
        "demo_mode": True,
        "platform": platform.system(),
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}"
    }

@app.get("/demo", response_class=HTMLResponse)
async def demo_dashboard():
    """Universal demo dashboard"""
    data = generate_demo_data()

    # Platform-specific icons (Unicode for Mac/Linux, ASCII for Windows)
    if IS_WINDOWS:
        icons = {"roi": "$", "retention": "R", "engagement": "E", "rocket": "", "target": ""}
    else:
        icons = {"roi": "💰", "retention": "🔄", "engagement": "👥", "rocket": "🚀", "target": "🎯"}

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Omnify Marketing Cloud - Live Demo</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            * {{ margin: 0; padding: 0; box-sizing: border-box; }}
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh; color: #333; padding: 2rem;
            }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{
                background: rgba(255,255,255,0.95); padding: 2rem; border-radius: 12px;
                margin-bottom: 2rem; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }}
            .header h1 {{ font-size: 2.5rem; color: #2d3748; margin-bottom: 0.5rem; }}
            .header p {{ color: #718096; font-size: 1.1rem; }}
            .platform-info {{
                background: #e2e8f0; padding: 0.5rem 1rem; border-radius: 20px;
                display: inline-block; margin-top: 1rem; font-size: 0.9rem; color: #4a5568;
            }}
            .live-indicator {{
                display: inline-flex; align-items: center; gap: 0.5rem;
                background: #48bb78; color: white; padding: 0.5rem 1rem;
                border-radius: 20px; font-weight: 600; margin-bottom: 2rem;
            }}
            .pulse {{
                width: 8px; height: 8px; background: white; border-radius: 50%;
                animation: pulse 2s infinite;
            }}
            @keyframes pulse {{ 0%, 100% {{ opacity: 1; }} 50% {{ opacity: 0.5; }} }}
            .metrics {{
                display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem; margin-bottom: 3rem;
            }}
            .metric-card {{
                background: rgba(255,255,255,0.95); padding: 2rem; border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1); text-align: center;
            }}
            .metric-value {{
                font-size: 2.5rem; font-weight: 700; color: #4299e1; margin-bottom: 0.5rem;
            }}
            .metric-label {{ color: #718096; font-weight: 600; }}
            .agents {{
                display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 2rem;
            }}
            .agent-card {{
                background: rgba(255,255,255,0.95); padding: 2rem; border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }}
            .agent-header {{
                display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;
            }}
            .agent-icon {{
                width: 50px; height: 50px; border-radius: 50%; display: flex;
                align-items: center; justify-content: center; font-size: 1.5rem; color: white;
            }}
            .roi-icon {{ background: linear-gradient(135deg, #4299e1, #3182ce); }}
            .retention-icon {{ background: linear-gradient(135deg, #48bb78, #38a169); }}
            .engagement-icon {{ background: linear-gradient(135deg, #ed8936, #dd6b20); }}
            .agent-title {{ font-size: 1.5rem; font-weight: 700; color: #2d3748; }}
            .agent-status {{
                background: #48bb78; color: white; padding: 0.25rem 0.75rem;
                border-radius: 20px; font-size: 0.8rem; font-weight: 600;
            }}
            .agent-metrics {{
                display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-top: 1rem;
            }}
            .refresh-btn {{
                background: #4299e1; color: white; border: none; padding: 1rem 2rem;
                border-radius: 8px; font-weight: 600; cursor: pointer; font-size: 1rem;
                margin: 2rem auto; display: block; transition: background 0.2s;
            }}
            .refresh-btn:hover {{ background: #3182ce; }}
            .footer {{
                text-align: center; margin-top: 3rem; color: rgba(255,255,255,0.8);
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{icons['rocket']} Omnify Marketing Cloud</h1>
                <p>AI-Native Marketing Automation Platform</p>
                <div class="platform-info">
                    Running on {data['platform']['platform_os']} | Python {data['platform']['python_version']}
                </div>
                <div class="live-indicator">
                    <div class="pulse"></div>
                    Live Demo - AI Agents Active
                </div>
            </div>

            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value">${data['platform']['total_revenue_impact']:,}</div>
                    <div class="metric-label">Revenue Impact Today</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data['platform']['ai_decisions_today']:,}</div>
                    <div class="metric-label">AI Decisions Made</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data['platform']['uptime']}</div>
                    <div class="metric-label">Platform Uptime</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data['platform']['last_updated']}</div>
                    <div class="metric-label">Last Updated</div>
                </div>
            </div>

            <div class="agents">
                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon roi-icon">{icons['roi']}</div>
                        <div>
                            <div class="agent-title">ROI Engine X™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="agent-metrics">
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['roi_engine']['roi_improvement']}%</div>
                            <div class="metric-label">ROI Improvement</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['roi_engine']['optimizations_today']}</div>
                            <div class="metric-label">Optimizations Today</div>
                        </div>
                    </div>
                    <p style="margin-top: 1rem; color: #718096;">
                        AI-powered campaign optimization with {data['roi_engine']['confidence_score']}% confidence.
                        Generated ${data['roi_engine']['revenue_impact']:,} in additional revenue today.
                    </p>
                </div>

                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon retention-icon">{icons['retention']}</div>
                        <div>
                            <div class="agent-title">Retention Reactor Pro™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="agent-metrics">
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['retention_reactor']['at_risk_customers']}</div>
                            <div class="metric-label">At-Risk Customers</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['retention_reactor']['retention_rate']}%</div>
                            <div class="metric-label">Retention Rate</div>
                        </div>
                    </div>
                    <p style="margin-top: 1rem; color: #718096;">
                        Monitoring {data['retention_reactor']['customers_monitored']:,} customers.
                        ${data['retention_reactor']['revenue_at_risk']:,} in revenue at risk identified.
                    </p>
                </div>

                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon engagement-icon">{icons['engagement']}</div>
                        <div>
                            <div class="agent-title">EngageSense Ultra™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="agent-metrics">
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['engagesense']['engagement_improvement']}%</div>
                            <div class="metric-label">Engagement Boost</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.8rem;">{data['engagesense']['personalizations_today']:,}</div>
                            <div class="metric-label">Personalizations</div>
                        </div>
                    </div>
                    <p style="margin-top: 1rem; color: #718096;">
                        Segmenting {data['engagesense']['total_customers']:,} customers in real-time.
                        {data['engagesense']['conversion_lift']}% conversion rate improvement achieved.
                    </p>
                </div>
            </div>

            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Live Data</button>

            <div class="footer">
                <p>Omnify Marketing Cloud Demo | Platform: {data['platform']['platform_os']} | Last Updated: {data['platform']['last_updated']}</p>
                <p><a href="/docs" style="color: rgba(255,255,255,0.9);">API Documentation</a> |
                   <a href="/" style="color: rgba(255,255,255,0.9);">API Status</a></p>
            </div>
        </div>

        <script>
            // Auto-refresh every 30 seconds
            setTimeout(() => location.reload(), 30000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html)

@app.get("/api/demo/data")
async def demo_data():
    """API endpoint for demo data"""
    return generate_demo_data()

def main():
    """Start the universal demo server"""
    # Platform-specific console output
    safe_print("🎯 OMNIFY MARKETING CLOUD - UNIVERSAL DEMO", "OMNIFY MARKETING CLOUD - UNIVERSAL DEMO")
    print("=" * 60)
    safe_print(f"🖥️  Platform: {platform.system()}", f"Platform: {platform.system()}")
    safe_print(f"🐍 Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}", f"Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    safe_print("🚀 Starting demo server...", "Starting demo server...")

    # Find available port
    port = find_available_port()

    print(f"📍 Demo will be available at:")
    print(f"   • Demo Dashboard: http://localhost:{port}/demo")
    print(f"   • API Docs: http://localhost:{port}/docs")
    print(f"   • Health Check: http://localhost:{port}/health")
    print()
    if port != 8000:
        safe_print(f"⚠️  Port 8000 was busy, using port {port} instead", f"Port 8000 was busy, using port {port} instead")
        print()
    safe_print("💡 Press Ctrl+C to stop", "Press Ctrl+C to stop")
    print("=" * 60)

    # Open browser after a short delay
    def open_browser():
        time.sleep(2)
        webbrowser.open(f"http://localhost:{port}/demo")

    import threading
    threading.Thread(target=open_browser, daemon=True).start()

    # Start server with error handling
    try:
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
    except KeyboardInterrupt:
        safe_print("\n👋 Demo stopped. Thank you!", "\nDemo stopped. Thank you!")
    except OSError as e:
        if "address already in use" in str(e).lower() or "10048" in str(e):
            safe_print(f"\n⚠️  Port {port} is busy. Trying to find another port...", f"\nPort {port} is busy. Trying to find another port...")
            # Try to find another port
            new_port = find_available_port(port + 1)
            safe_print(f"🔄 Retrying on port {new_port}...", f"Retrying on port {new_port}...")
            try:
                uvicorn.run(app, host="0.0.0.0", port=new_port, log_level="info")
            except Exception as retry_e:
                safe_print(f"\n❌ Failed to start on port {new_port}: {retry_e}", f"\nFailed to start on port {new_port}: {retry_e}")
        else:
            safe_print(f"\n❌ Error starting demo: {e}", f"\nError starting demo: {e}")
    except Exception as e:
        safe_print(f"\n❌ Unexpected error: {e}", f"\nUnexpected error: {e}")
        safe_print("💡 Try running the demo again or check if another service is using the port.", "Try running the demo again or check if another service is using the port.")

if __name__ == "__main__":
    main()
