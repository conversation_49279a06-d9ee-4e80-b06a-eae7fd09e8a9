"""
Campaign management API endpoints
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field
import structlog

from apps.core.database import get_db, Campaign, Client, CampaignMetric
from apps.auth.dependencies import get_current_user

logger = structlog.get_logger()

router = APIRouter()

class CampaignCreate(BaseModel):
    name: str
    platform: str = Field(..., regex="^(google_ads|meta_ads)$")
    campaign_id: str
    budget: float = Field(..., gt=0)
    target_cac: float = Field(..., gt=0)

class CampaignUpdate(BaseModel):
    name: Optional[str] = None
    budget: Optional[float] = Field(None, gt=0)
    target_cac: Optional[float] = Field(None, gt=0)
    status: Optional[str] = Field(None, regex="^(active|paused|archived)$")

class CampaignResponse(BaseModel):
    id: int
    client_id: int
    name: str
    platform: str
    campaign_id: str
    budget: float
    target_cac: float
    current_cac: Optional[float]
    roas: Optional[float]
    status: str
    created_at: datetime
    updated_at: Optional[datetime]

class CampaignMetricsResponse(BaseModel):
    campaign_id: int
    metrics: List[dict]
    summary: dict

@router.post("/", response_model=CampaignResponse)
async def create_campaign(
    client_id: int,
    campaign_data: CampaignCreate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new campaign"""
    try:
        # Verify client exists
        client_query = select(Client).where(Client.id == client_id)
        client_result = await db.execute(client_query)
        client = client_result.scalar_one_or_none()
        
        if not client:
            raise HTTPException(status_code=404, detail="Client not found")
        
        # Create campaign
        db_campaign = Campaign(
            client_id=client_id,
            name=campaign_data.name,
            platform=campaign_data.platform,
            campaign_id=campaign_data.campaign_id,
            budget=campaign_data.budget,
            target_cac=campaign_data.target_cac
        )
        
        db.add(db_campaign)
        await db.commit()
        await db.refresh(db_campaign)
        
        logger.info(
            "Campaign created",
            campaign_id=db_campaign.id,
            client_id=client_id,
            platform=campaign_data.platform
        )
        
        return CampaignResponse(
            id=db_campaign.id,
            client_id=db_campaign.client_id,
            name=db_campaign.name,
            platform=db_campaign.platform,
            campaign_id=db_campaign.campaign_id,
            budget=db_campaign.budget,
            target_cac=db_campaign.target_cac,
            current_cac=db_campaign.current_cac,
            roas=db_campaign.roas,
            status=db_campaign.status,
            created_at=db_campaign.created_at,
            updated_at=db_campaign.updated_at
        )
        
    except Exception as e:
        logger.error("Failed to create campaign", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to create campaign")

@router.get("/", response_model=List[CampaignResponse])
async def list_campaigns(
    client_id: Optional[int] = Query(None),
    platform: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """List campaigns with optional filters"""
    try:
        query = select(Campaign)
        
        # Apply filters
        if client_id:
            query = query.where(Campaign.client_id == client_id)
        if platform:
            query = query.where(Campaign.platform == platform)
        if status:
            query = query.where(Campaign.status == status)
        
        query = query.order_by(Campaign.created_at.desc())
        
        result = await db.execute(query)
        campaigns = result.scalars().all()
        
        return [
            CampaignResponse(
                id=campaign.id,
                client_id=campaign.client_id,
                name=campaign.name,
                platform=campaign.platform,
                campaign_id=campaign.campaign_id,
                budget=campaign.budget,
                target_cac=campaign.target_cac,
                current_cac=campaign.current_cac,
                roas=campaign.roas,
                status=campaign.status,
                created_at=campaign.created_at,
                updated_at=campaign.updated_at
            )
            for campaign in campaigns
        ]
        
    except Exception as e:
        logger.error("Failed to list campaigns", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list campaigns")

@router.get("/{campaign_id}", response_model=CampaignResponse)
async def get_campaign(
    campaign_id: int,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get campaign by ID"""
    try:
        query = select(Campaign).where(Campaign.id == campaign_id)
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        return CampaignResponse(
            id=campaign.id,
            client_id=campaign.client_id,
            name=campaign.name,
            platform=campaign.platform,
            campaign_id=campaign.campaign_id,
            budget=campaign.budget,
            target_cac=campaign.target_cac,
            current_cac=campaign.current_cac,
            roas=campaign.roas,
            status=campaign.status,
            created_at=campaign.created_at,
            updated_at=campaign.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get campaign", error=str(e), campaign_id=campaign_id)
        raise HTTPException(status_code=500, detail="Failed to get campaign")

@router.put("/{campaign_id}", response_model=CampaignResponse)
async def update_campaign(
    campaign_id: int,
    campaign_data: CampaignUpdate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Update campaign"""
    try:
        query = select(Campaign).where(Campaign.id == campaign_id)
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Update fields
        update_data = campaign_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(campaign, field, value)
        
        await db.commit()
        await db.refresh(campaign)
        
        logger.info("Campaign updated", campaign_id=campaign_id, updates=update_data)
        
        return CampaignResponse(
            id=campaign.id,
            client_id=campaign.client_id,
            name=campaign.name,
            platform=campaign.platform,
            campaign_id=campaign.campaign_id,
            budget=campaign.budget,
            target_cac=campaign.target_cac,
            current_cac=campaign.current_cac,
            roas=campaign.roas,
            status=campaign.status,
            created_at=campaign.created_at,
            updated_at=campaign.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update campaign", error=str(e), campaign_id=campaign_id)
        raise HTTPException(status_code=500, detail="Failed to update campaign")

@router.delete("/{campaign_id}")
async def delete_campaign(
    campaign_id: int,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Delete campaign (soft delete by setting status to archived)"""
    try:
        query = select(Campaign).where(Campaign.id == campaign_id)
        result = await db.execute(query)
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Soft delete
        campaign.status = "archived"
        await db.commit()
        
        logger.info("Campaign deleted", campaign_id=campaign_id)
        
        return {"message": "Campaign deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete campaign", error=str(e), campaign_id=campaign_id)
        raise HTTPException(status_code=500, detail="Failed to delete campaign")
