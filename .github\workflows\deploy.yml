name: Deploy Omnify Marketing Cloud

on:
  push:
    branches: [main, staging]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: omnify/marketing-cloud

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: omnify_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Set up environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/omnify_test" >> .env
        echo "REDIS_URL=redis://localhost:6379/0" >> .env
        echo "ENVIRONMENT=test" >> .env
    
    - name: Run database migrations
      run: |
        alembic upgrade head
    
    - name: Run tests
      run: |
        pytest tests/ -v --cov=apps --cov=lib --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  build:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.prod
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging'
    environment: staging
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Deploy to ECS Staging
      run: |
        # Update ECS service with new image
        aws ecs update-service \
          --cluster omnify-staging \
          --service omnify-api-staging \
          --force-new-deployment \
          --task-definition omnify-api-staging
    
    - name: Wait for deployment
      run: |
        aws ecs wait services-stable \
          --cluster omnify-staging \
          --services omnify-api-staging
    
    - name: Run health check
      run: |
        # Wait for service to be healthy
        sleep 30
        curl -f https://staging-api.omnify.com/health || exit 1
    
    - name: Run smoke tests
      run: |
        # Run basic smoke tests against staging
        python scripts/smoke_tests.py --environment staging

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Create deployment
      id: deployment
      uses: actions/github-script@v7
      with:
        script: |
          const deployment = await github.rest.repos.createDeployment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            ref: context.sha,
            environment: 'production',
            description: 'Deploy to production',
            auto_merge: false
          });
          return deployment.data.id;
    
    - name: Set deployment status to in_progress
      uses: actions/github-script@v7
      with:
        script: |
          await github.rest.repos.createDeploymentStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            deployment_id: ${{ steps.deployment.outputs.result }},
            state: 'in_progress',
            description: 'Deployment started'
          });
    
    - name: Deploy to ECS Production
      run: |
        # Blue-green deployment strategy
        
        # 1. Update task definition with new image
        TASK_DEFINITION=$(aws ecs describe-task-definition \
          --task-definition omnify-api-production \
          --query 'taskDefinition' \
          --output json)
        
        # 2. Update image in task definition
        NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "${{ needs.build.outputs.image-tag }}" \
          '.containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.placementConstraints) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
        
        # 3. Register new task definition
        NEW_TASK_DEF_ARN=$(echo $NEW_TASK_DEFINITION | aws ecs register-task-definition \
          --cli-input-json file:///dev/stdin \
          --query 'taskDefinition.taskDefinitionArn' \
          --output text)
        
        # 4. Update service with new task definition
        aws ecs update-service \
          --cluster omnify-production \
          --service omnify-api-production \
          --task-definition $NEW_TASK_DEF_ARN
    
    - name: Wait for deployment
      run: |
        aws ecs wait services-stable \
          --cluster omnify-production \
          --services omnify-api-production \
          --cli-read-timeout 900 \
          --cli-connect-timeout 60
    
    - name: Run health check
      run: |
        # Wait for service to be healthy
        sleep 60
        
        # Check health endpoint
        for i in {1..10}; do
          if curl -f https://api.omnify.com/health; then
            echo "Health check passed"
            break
          fi
          echo "Health check failed, retrying in 30s..."
          sleep 30
        done
    
    - name: Run production smoke tests
      run: |
        python scripts/smoke_tests.py --environment production
    
    - name: Set deployment status to success
      if: success()
      uses: actions/github-script@v7
      with:
        script: |
          await github.rest.repos.createDeploymentStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            deployment_id: ${{ steps.deployment.outputs.result }},
            state: 'success',
            description: 'Deployment completed successfully',
            environment_url: 'https://api.omnify.com'
          });
    
    - name: Set deployment status to failure
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          await github.rest.repos.createDeploymentStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            deployment_id: ${{ steps.deployment.outputs.result }},
            state: 'failure',
            description: 'Deployment failed'
          });
    
    - name: Rollback on failure
      if: failure()
      run: |
        echo "Deployment failed, initiating rollback..."
        
        # Get previous task definition
        PREVIOUS_TASK_DEF=$(aws ecs describe-services \
          --cluster omnify-production \
          --services omnify-api-production \
          --query 'services[0].deployments[1].taskDefinition' \
          --output text)
        
        if [ "$PREVIOUS_TASK_DEF" != "None" ]; then
          # Rollback to previous task definition
          aws ecs update-service \
            --cluster omnify-production \
            --service omnify-api-production \
            --task-definition $PREVIOUS_TASK_DEF
          
          # Wait for rollback to complete
          aws ecs wait services-stable \
            --cluster omnify-production \
            --services omnify-api-production
          
          echo "Rollback completed"
        fi
    
    - name: Notify team
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

  database-backup:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: deploy-production
    
    steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Create RDS snapshot
      run: |
        SNAPSHOT_ID="omnify-prod-$(date +%Y%m%d-%H%M%S)"
        
        aws rds create-db-snapshot \
          --db-instance-identifier omnify-production \
          --db-snapshot-identifier $SNAPSHOT_ID
        
        echo "Created database snapshot: $SNAPSHOT_ID"
