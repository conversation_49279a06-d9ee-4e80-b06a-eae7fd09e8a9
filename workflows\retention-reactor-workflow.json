{"name": "Retention Reactor Pro™ - Churn Prevention Workflow", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "value": 6}]}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://localhost:8000/api/v1/agents/optimize", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"agent_type\": \"retention_reactor\",\n  \"force_execution\": false\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [460, 300], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"amount": 30, "unit": "seconds"}, "name": "Wait for Processing", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "http://localhost:8000/api/v1/agents/decisions", "options": {"queryParameters": {"parameters": [{"name": "client_id", "value": "={{$json[\"client_id\"]}}"}, {"name": "agent_type", "value": "retention_reactor"}, {"name": "days", "value": "1"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Get Churn Predictions", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 300], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"decision\"][\"risk_level\"]}}", "operation": "equal", "value2": "critical"}]}}, "name": "Check Critical Risk", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"url": "http://localhost:8000/api/v1/webhooks/external/performance-alert", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"event_type\": \"critical_churn_risk\",\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"data\": {\n    \"customer_id\": \"{{$json[\"decision\"][\"customer_id\"]}}\",\n    \"churn_probability\": {{$json[\"decision\"][\"churn_probability\"]}},\n    \"recommended_actions\": {{$json[\"decision\"][\"recommended_actions\"]}}\n  }\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Send Critical Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 200], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"decision\"][\"risk_level\"]}}", "operation": "equal", "value2": "high"}]}}, "name": "Check High Risk", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 400]}, {"parameters": {"url": "https://api.sendgrid.com/v3/mail/send", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"personalizations\": [{\n    \"to\": [{\"email\": \"<EMAIL>\"}],\n    \"subject\": \"High Churn Risk Alert - {{$json[\"decision\"][\"customer_id\"]}}\"\n  }],\n  \"from\": {\"email\": \"<EMAIL>\"},\n  \"content\": [{\n    \"type\": \"text/html\",\n    \"value\": \"Customer {{$json[\"decision\"][\"customer_id\"]}} has {{$json[\"decision\"][\"churn_probability\"]}}% churn probability. Immediate action required.\"\n  }]\n}", "headers": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_SENDGRID_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}}}, "name": "Send <PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1560, 300]}], "connections": {"Schedule Trigger": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Trigger Churn Analysis": {"main": [[{"node": "Wait for Processing", "type": "main", "index": 0}]]}, "Wait for Processing": {"main": [[{"node": "Get Churn Predictions", "type": "main", "index": 0}]]}, "Get Churn Predictions": {"main": [[{"node": "Check Critical Risk", "type": "main", "index": 0}]]}, "Check Critical Risk": {"main": [[{"node": "Send Critical Alert", "type": "main", "index": 0}], [{"node": "Check High Risk", "type": "main", "index": 0}]]}, "Check High Risk": {"main": [[{"node": "Send <PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "id": "2"}