# Omnify Marketing Cloud - Deployment Guide

## Quick Start (Development)

### 1. Prerequisites
- Python 3.11+
- Docker & Docker Compose
- PostgreSQL (or use Docker)
- Redis (or use Docker)

### 2. Environment Setup

```bash
# Clone and setup
git clone <repository-url>
cd Ominify.SAS

# Copy environment file
cp .env.example .env

# Edit .env with your API keys
# Required: OPENAI_API_KEY, GOOGLE_ADS_*, META_*
```

### 3. Start Infrastructure Services

```bash
# Start PostgreSQL, Redis, n8n, Prometheus, Grafana
docker-compose up -d

# Wait for services to be ready (30-60 seconds)
docker-compose logs -f postgres
```

### 4. Install Dependencies & Initialize Database

```bash
# Install Python dependencies
pip install -r requirements.txt

# Run database migrations
alembic upgrade head

# Check system status
python scripts/start.py
```

### 5. Start the API

```bash
# Development mode with hot reload
uvicorn apps.core.main:app --reload --host 0.0.0.0 --port 8000

# Or production mode
uvicorn apps.core.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 6. Verify Installation

Visit these URLs to confirm everything is working:

- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **n8n Workflows**: http://localhost:5678 (admin/omnify123)
- **Grafana Dashboard**: http://localhost:3000 (admin/omnify123)
- **Prometheus Metrics**: http://localhost:9090

## Production Deployment

### AWS ECS with Terraform

```bash
# Navigate to infrastructure directory
cd infrastructure/

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var="environment=production"

# Deploy
terraform apply
```

### Docker Production Build

```bash
# Build production image
docker build -t omnify-marketing-cloud:latest .

# Run with production settings
docker run -d \
  --name omnify-api \
  -p 8000:8000 \
  -e DATABASE_URL="postgresql://..." \
  -e OPENAI_API_KEY="..." \
  omnify-marketing-cloud:latest
```

### Environment Variables (Production)

```bash
# Core Settings
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=<strong-secret-key>

# Database
DATABASE_URL=postgresql+asyncpg://user:pass@host:5432/omnify_prod

# AI Services
OPENAI_API_KEY=sk-...
MANUS_RL_API_KEY=...
MANUS_RL_ENDPOINT=https://api.manus.rl/v1

# Google Ads API
GOOGLE_ADS_DEVELOPER_TOKEN=...
GOOGLE_ADS_CLIENT_ID=...
GOOGLE_ADS_CLIENT_SECRET=...

# Meta Ads API
META_APP_ID=...
META_APP_SECRET=...
META_ACCESS_TOKEN=...

# Monitoring
SENTRY_DSN=https://...
```

## n8n Workflow Setup

### 1. Import Workflows

```bash
# Copy workflow files to n8n
cp workflows/*.json /path/to/n8n/workflows/

# Or import via n8n UI:
# 1. Go to http://localhost:5678
# 2. Click "Import from file"
# 3. Select workflows/roi-engine-workflow.json
```

### 2. Configure Credentials

In n8n, create these credentials:

1. **Omnify API Auth** (HTTP Header Auth)
   - Name: `Authorization`
   - Value: `Bearer YOUR_JWT_TOKEN`

2. **Google Ads Credentials**
   - Developer Token: `YOUR_DEVELOPER_TOKEN`
   - Client ID: `YOUR_CLIENT_ID`
   - Client Secret: `YOUR_CLIENT_SECRET`

### 3. Activate Workflows

1. Open each workflow in n8n
2. Click "Active" toggle
3. Test with "Execute Workflow"

## Monitoring Setup

### Prometheus Configuration

The system automatically exposes metrics at `/metrics`. Key metrics include:

- `http_requests_total` - Total HTTP requests
- `http_request_duration_seconds` - Request latency
- `ai_decisions_total` - AI agent decisions
- `campaign_optimizations_total` - Campaign optimizations

### Grafana Dashboards

Import the pre-built dashboard:

1. Go to http://localhost:3000
2. Login: admin/omnify123
3. Import dashboard from `infrastructure/grafana/`

### Alerting Rules

Key alerts to configure:

- High error rate (>5%)
- High latency (>2s p95)
- AI confidence drops (<70%)
- Campaign budget exhaustion

## Security Considerations

### 1. API Security
- Use strong JWT secrets
- Enable HTTPS in production
- Implement rate limiting
- Validate all inputs

### 2. Database Security
- Use connection pooling
- Enable SSL connections
- Regular backups
- Access control

### 3. API Keys
- Store in environment variables
- Rotate regularly
- Use least privilege access
- Monitor usage

## Scaling Guidelines

### Horizontal Scaling
- Run multiple API instances behind load balancer
- Use Redis for session storage
- Database connection pooling

### Performance Optimization
- Enable database query caching
- Use CDN for static assets
- Optimize AI model calls
- Implement circuit breakers

### Database Scaling
- Read replicas for analytics queries
- Partitioning for large tables
- Regular maintenance and optimization

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL is running
   docker-compose ps postgres
   
   # Check connection string
   echo $DATABASE_URL
   ```

2. **AI API Timeouts**
   ```bash
   # Check API keys are set
   echo $OPENAI_API_KEY
   
   # Monitor API usage
   curl http://localhost:8000/metrics | grep ai_
   ```

3. **n8n Workflows Not Triggering**
   ```bash
   # Check n8n logs
   docker-compose logs n8n
   
   # Verify webhook URLs
   curl http://localhost:5678/webhook/test
   ```

### Log Analysis

```bash
# API logs
docker-compose logs -f api

# Database logs
docker-compose logs -f postgres

# All services
docker-compose logs -f
```

### Health Checks

```bash
# API health
curl http://localhost:8000/health

# Database health
curl http://localhost:8000/health | jq .database

# AI services health
curl http://localhost:8000/api/v1/agents/status/roi_engine?client_id=1
```

## Backup & Recovery

### Database Backup
```bash
# Create backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Restore backup
psql $DATABASE_URL < backup_20240101.sql
```

### Configuration Backup
```bash
# Backup n8n workflows
cp -r /path/to/n8n/workflows/ ./backups/workflows/

# Backup environment
cp .env ./backups/env_$(date +%Y%m%d)
```

## Support

For deployment issues:
- Check logs: `docker-compose logs`
- Health status: http://localhost:8000/health
- Metrics: http://localhost:8000/metrics
- Documentation: http://localhost:8000/docs
