#!/usr/bin/env python3
"""
Enhanced Setup Wizard Launcher for Omnify Marketing Cloud
Quick launcher for the enhanced setup wizard with multi-cloud support
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """Launch the enhanced setup wizard"""
    print("\n" + "="*80)
    print("🚀 OMNIFY MARKETING CLOUD - ENHANCED SETUP WIZARD")
    print("="*80)
    print("\n🌐 World's First Multi-Cloud Marketing Automation Platform")
    print("\n🎯 This wizard will help you:")
    print("   • Analyze your business requirements")
    print("   • Get AI-powered cloud recommendations")
    print("   • Deploy your preferred cloud variant")
    print("   • Set up integrations and configurations")
    print("   • Validate your deployment")
    print("\n" + "="*80)
    
    # Check if we're in the right directory
    if not Path("scripts/enhanced_setup_wizard.py").exists():
        print("\n❌ Error: Enhanced setup wizard not found!")
        print("   Please run this script from the project root directory.")
        print("   Expected to find: scripts/enhanced_setup_wizard.py")
        return 1
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("\n❌ Error: Python 3.8+ required")
        print(f"   Current version: {sys.version}")
        print("   Please upgrade Python and try again.")
        return 1
    
    # Launch the enhanced setup wizard
    try:
        print("\n🚀 Launching Enhanced Setup Wizard...")
        result = subprocess.run([
            sys.executable, 
            "scripts/enhanced_setup_wizard.py"
        ], check=True)
        
        print("\n✅ Setup wizard completed successfully!")
        return result.returncode
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Setup wizard failed with exit code: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n\n⏸️ Setup wizard interrupted. Goodbye!")
        return 0
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
