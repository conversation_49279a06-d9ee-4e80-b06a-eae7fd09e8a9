"""
System Management and Testing API endpoints
"""
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
import structlog

from apps.core.database import get_db
from apps.auth.dependencies import get_admin_user
from lib.monitoring.alert_manager import alert_manager
from lib.utils.cache import cache_manager
from lib.config.feature_flags import feature_manager
from lib.data.import_manager import import_manager
from lib.integrations.email_service import email_manager, EmailRecipient

logger = structlog.get_logger()

router = APIRouter()

class SystemStatus(BaseModel):
    """System status response"""
    status: str
    version: str
    environment: str
    uptime_seconds: float
    database_status: str
    cache_status: str
    ai_services_status: str
    external_apis_status: Dict[str, str]
    active_alerts: int
    feature_flags: Dict[str, bool]

class TestResult(BaseModel):
    """Test result model"""
    test_name: str
    status: str  # "passed", "failed", "skipped"
    duration_ms: float
    message: str
    details: Dict[str, Any] = {}

class SystemTest(BaseModel):
    """System test suite result"""
    test_suite: str
    total_tests: int
    passed: int
    failed: int
    skipped: int
    duration_ms: float
    results: List[TestResult]

@router.get("/status", response_model=SystemStatus)
async def get_system_status(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_admin_user)
):
    """
    Get comprehensive system status
    """
    try:
        start_time = datetime.utcnow()
        
        # Check database
        try:
            await db.execute("SELECT 1")
            database_status = "healthy"
        except Exception:
            database_status = "unhealthy"
        
        # Check cache
        try:
            await cache_manager.cache.set("health_check", "ok", 60)
            cache_result = await cache_manager.cache.get("health_check")
            cache_status = "healthy" if cache_result == "ok" else "unhealthy"
        except Exception:
            cache_status = "unhealthy"
        
        # Check AI services
        ai_services_status = "healthy"  # Would check OpenAI, Manus RL APIs
        
        # Check external APIs
        external_apis_status = {
            "google_ads": "unknown",  # Would test actual API
            "meta_ads": "unknown",
            "sendgrid": "unknown"
        }
        
        # Get active alerts
        active_alerts = len(alert_manager.get_active_alerts())
        
        # Get feature flags
        feature_flags = {
            name: flag.enabled 
            for name, flag in feature_manager.get_all_feature_flags().items()
        }
        
        # Calculate uptime (simplified)
        uptime_seconds = 3600.0  # Would track actual uptime
        
        return SystemStatus(
            status="healthy" if all([
                database_status == "healthy",
                cache_status == "healthy",
                ai_services_status == "healthy"
            ]) else "degraded",
            version="1.0.0",
            environment="production",  # Would get from config
            uptime_seconds=uptime_seconds,
            database_status=database_status,
            cache_status=cache_status,
            ai_services_status=ai_services_status,
            external_apis_status=external_apis_status,
            active_alerts=active_alerts,
            feature_flags=feature_flags
        )
        
    except Exception as e:
        logger.error("Failed to get system status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get system status")

@router.post("/test/ai-agents", response_model=SystemTest)
async def test_ai_agents(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_admin_user)
):
    """
    Test all AI agents functionality
    """
    try:
        start_time = datetime.utcnow()
        results = []
        
        # Test ROI Engine X™
        roi_start = datetime.utcnow()
        try:
            from lib.ai.decision_engine import ROIEngineX
            roi_engine = ROIEngineX()
            
            # Test with sample data
            sample_campaign = {
                "campaign_id": "test_campaign",
                "platform": "google_ads",
                "budget": 1000.0,
                "target_cac": 50.0,
                "budget_remaining": 700.0,
                "days_remaining": 15
            }
            
            sample_metrics = [
                {
                    "date": "2024-01-01",
                    "spend": 100.0,
                    "revenue": 250.0,
                    "conversions": 5,
                    "clicks": 50,
                    "impressions": 1000
                }
            ]
            
            decision = await roi_engine.optimize_campaign(
                "test_client",
                sample_campaign,
                sample_metrics
            )
            
            roi_duration = (datetime.utcnow() - roi_start).total_seconds() * 1000
            
            results.append(TestResult(
                test_name="ROI Engine X™",
                status="passed",
                duration_ms=roi_duration,
                message="AI decision generated successfully",
                details={
                    "action": decision.action,
                    "confidence": decision.confidence,
                    "model_used": decision.model_used
                }
            ))
            
        except Exception as e:
            roi_duration = (datetime.utcnow() - roi_start).total_seconds() * 1000
            results.append(TestResult(
                test_name="ROI Engine X™",
                status="failed",
                duration_ms=roi_duration,
                message=f"Test failed: {str(e)}"
            ))
        
        # Test Retention Reactor Pro™
        retention_start = datetime.utcnow()
        try:
            from lib.ai.retention_reactor import RetentionReactorPro, CustomerData
            
            retention_reactor = RetentionReactorPro()
            await retention_reactor.initialize_model()
            
            sample_customer = CustomerData(
                customer_id="test_customer",
                days_since_last_purchase=30,
                total_purchases=5,
                average_order_value=100.0,
                total_spent=500.0,
                email_engagement_rate=70.0,
                support_tickets=1,
                last_login_days=7,
                subscription_tier="premium",
                geographic_region="US"
            )
            
            prediction = await retention_reactor.predict_churn(sample_customer)
            
            retention_duration = (datetime.utcnow() - retention_start).total_seconds() * 1000
            
            results.append(TestResult(
                test_name="Retention Reactor Pro™",
                status="passed",
                duration_ms=retention_duration,
                message="Churn prediction generated successfully",
                details={
                    "churn_probability": prediction.churn_probability,
                    "risk_level": prediction.risk_level,
                    "actions_count": len(prediction.recommended_actions)
                }
            ))
            
        except Exception as e:
            retention_duration = (datetime.utcnow() - retention_start).total_seconds() * 1000
            results.append(TestResult(
                test_name="Retention Reactor Pro™",
                status="failed",
                duration_ms=retention_duration,
                message=f"Test failed: {str(e)}"
            ))
        
        # Test EngageSense Ultra™
        engage_start = datetime.utcnow()
        try:
            from lib.ai.engage_sense import EngageSenseUltra, CustomerBehavior
            
            engage_sense = EngageSenseUltra()
            await engage_sense.initialize_models()
            
            sample_behavior = CustomerBehavior(
                customer_id="test_customer",
                page_views=50,
                session_duration_avg=8.0,
                bounce_rate=30.0,
                email_opens=15,
                email_clicks=5,
                social_shares=2,
                product_views=25,
                cart_additions=8,
                purchase_frequency=2.0,
                support_interactions=1,
                feature_usage_score=80.0,
                mobile_vs_desktop_ratio=0.6
            )
            
            engagement_score = await engage_sense.score_customer_engagement(sample_behavior)
            
            engage_duration = (datetime.utcnow() - engage_start).total_seconds() * 1000
            
            results.append(TestResult(
                test_name="EngageSense Ultra™",
                status="passed",
                duration_ms=engage_duration,
                message="Engagement scoring completed successfully",
                details={
                    "overall_score": engagement_score.overall_score,
                    "segment": engagement_score.segment,
                    "trend": engagement_score.trend
                }
            ))
            
        except Exception as e:
            engage_duration = (datetime.utcnow() - engage_start).total_seconds() * 1000
            results.append(TestResult(
                test_name="EngageSense Ultra™",
                status="failed",
                duration_ms=engage_duration,
                message=f"Test failed: {str(e)}"
            ))
        
        # Calculate summary
        total_duration = (datetime.utcnow() - start_time).total_seconds() * 1000
        passed = len([r for r in results if r.status == "passed"])
        failed = len([r for r in results if r.status == "failed"])
        skipped = len([r for r in results if r.status == "skipped"])
        
        return SystemTest(
            test_suite="AI Agents",
            total_tests=len(results),
            passed=passed,
            failed=failed,
            skipped=skipped,
            duration_ms=total_duration,
            results=results
        )
        
    except Exception as e:
        logger.error("AI agents test failed", error=str(e))
        raise HTTPException(status_code=500, detail="AI agents test failed")

@router.post("/test/integrations", response_model=SystemTest)
async def test_integrations(
    current_user = Depends(get_admin_user)
):
    """
    Test external integrations
    """
    try:
        start_time = datetime.utcnow()
        results = []
        
        # Test email service
        email_start = datetime.utcnow()
        try:
            test_recipient = EmailRecipient(
                email="<EMAIL>",
                variables={"test_message": "System test"}
            )
            
            # This would send a test email in production
            # For testing, we'll just validate the service is configured
            email_duration = (datetime.utcnow() - email_start).total_seconds() * 1000
            
            results.append(TestResult(
                test_name="Email Service",
                status="passed",
                duration_ms=email_duration,
                message="Email service configuration validated"
            ))
            
        except Exception as e:
            email_duration = (datetime.utcnow() - email_start).total_seconds() * 1000
            results.append(TestResult(
                test_name="Email Service",
                status="failed",
                duration_ms=email_duration,
                message=f"Email test failed: {str(e)}"
            ))
        
        # Test cache system
        cache_start = datetime.utcnow()
        try:
            test_key = "system_test_cache"
            test_value = {"test": True, "timestamp": datetime.utcnow().isoformat()}
            
            # Test set
            await cache_manager.cache.set(test_key, test_value, 60)
            
            # Test get
            cached_value = await cache_manager.cache.get(test_key)
            
            # Test delete
            await cache_manager.cache.delete(test_key)
            
            cache_duration = (datetime.utcnow() - cache_start).total_seconds() * 1000
            
            if cached_value and cached_value.get("test") == True:
                results.append(TestResult(
                    test_name="Cache System",
                    status="passed",
                    duration_ms=cache_duration,
                    message="Cache operations completed successfully"
                ))
            else:
                results.append(TestResult(
                    test_name="Cache System",
                    status="failed",
                    duration_ms=cache_duration,
                    message="Cache value mismatch"
                ))
                
        except Exception as e:
            cache_duration = (datetime.utcnow() - cache_start).total_seconds() * 1000
            results.append(TestResult(
                test_name="Cache System",
                status="failed",
                duration_ms=cache_duration,
                message=f"Cache test failed: {str(e)}"
            ))
        
        # Test alert system
        alert_start = datetime.utcnow()
        try:
            # Test alert creation
            await alert_manager.check_metric(
                "test_metric",
                100.0,
                "test_client",
                additional_context={"test": True}
            )
            
            alert_duration = (datetime.utcnow() - alert_start).total_seconds() * 1000
            
            results.append(TestResult(
                test_name="Alert System",
                status="passed",
                duration_ms=alert_duration,
                message="Alert system functioning correctly"
            ))
            
        except Exception as e:
            alert_duration = (datetime.utcnow() - alert_start).total_seconds() * 1000
            results.append(TestResult(
                test_name="Alert System",
                status="failed",
                duration_ms=alert_duration,
                message=f"Alert test failed: {str(e)}"
            ))
        
        # Calculate summary
        total_duration = (datetime.utcnow() - start_time).total_seconds() * 1000
        passed = len([r for r in results if r.status == "passed"])
        failed = len([r for r in results if r.status == "failed"])
        skipped = len([r for r in results if r.status == "skipped"])
        
        return SystemTest(
            test_suite="Integrations",
            total_tests=len(results),
            passed=passed,
            failed=failed,
            skipped=skipped,
            duration_ms=total_duration,
            results=results
        )
        
    except Exception as e:
        logger.error("Integration tests failed", error=str(e))
        raise HTTPException(status_code=500, detail="Integration tests failed")

@router.post("/cache/clear")
async def clear_cache(
    current_user = Depends(get_admin_user)
):
    """Clear all cache entries"""
    try:
        success = await cache_manager.cache.clear()
        
        if success:
            logger.info("Cache cleared by admin", user_id=current_user.id)
            return {"status": "success", "message": "Cache cleared successfully"}
        else:
            return {"status": "error", "message": "Failed to clear cache"}
            
    except Exception as e:
        logger.error("Failed to clear cache", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to clear cache")

@router.get("/cache/stats")
async def get_cache_stats(
    current_user = Depends(get_admin_user)
):
    """Get cache statistics"""
    try:
        stats = await cache_manager.cache.get_stats()
        return stats.dict()
        
    except Exception as e:
        logger.error("Failed to get cache stats", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get cache stats")

@router.get("/alerts")
async def get_system_alerts(
    severity: Optional[str] = None,
    category: Optional[str] = None,
    current_user = Depends(get_admin_user)
):
    """Get system alerts"""
    try:
        from lib.monitoring.alert_manager import AlertSeverity, AlertCategory
        
        severity_filter = AlertSeverity(severity) if severity else None
        category_filter = AlertCategory(category) if category else None
        
        alerts = alert_manager.get_active_alerts(
            severity=severity_filter,
            category=category_filter
        )
        
        return [alert.dict() for alert in alerts]
        
    except Exception as e:
        logger.error("Failed to get system alerts", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get system alerts")

@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(
    alert_id: str,
    current_user = Depends(get_admin_user)
):
    """Resolve a system alert"""
    try:
        await alert_manager.resolve_alert(alert_id, f"user:{current_user.id}")
        
        return {"status": "success", "message": f"Alert {alert_id} resolved"}
        
    except Exception as e:
        logger.error("Failed to resolve alert", error=str(e), alert_id=alert_id)
        raise HTTPException(status_code=500, detail="Failed to resolve alert")

@router.get("/feature-flags")
async def get_feature_flags(
    current_user = Depends(get_admin_user)
):
    """Get all feature flags"""
    try:
        flags = feature_manager.get_all_feature_flags()
        return {name: flag.dict() for name, flag in flags.items()}
        
    except Exception as e:
        logger.error("Failed to get feature flags", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get feature flags")

@router.post("/feature-flags/{flag_name}/toggle")
async def toggle_feature_flag(
    flag_name: str,
    enabled: bool,
    current_user = Depends(get_admin_user)
):
    """Toggle a feature flag"""
    try:
        feature_manager.update_feature_flag(flag_name, enabled=enabled)
        
        logger.info(
            "Feature flag toggled",
            flag_name=flag_name,
            enabled=enabled,
            user_id=current_user.id
        )
        
        return {
            "status": "success", 
            "message": f"Feature flag {flag_name} {'enabled' if enabled else 'disabled'}"
        }
        
    except Exception as e:
        logger.error("Failed to toggle feature flag", error=str(e), flag_name=flag_name)
        raise HTTPException(status_code=500, detail="Failed to toggle feature flag")
