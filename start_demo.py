#!/usr/bin/env python3
"""
Quick Demo Starter for Omnify Marketing Cloud
Starts the demo environment without Docker complications
"""
import asyncio
import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def start_redis():
    """Start Redis if not running"""
    print("🔄 Checking Redis...")
    try:
        # Check if Redis is already running
        result = subprocess.run(["redis-cli", "ping"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Redis is already running")
            return True
    except:
        pass
    
    # Try to start Redis with Docker
    try:
        print("🚀 Starting Redis with Docker...")
        subprocess.run([
            "docker", "run", "-d", "--name", "omnify-redis-quick",
            "-p", "6379:6379", "redis:7-alpine"
        ], check=True, capture_output=True)
        
        # Wait for Redis to be ready
        for i in range(10):
            try:
                result = subprocess.run(["redis-cli", "ping"], 
                                      capture_output=True, text=True, timeout=2)
                if result.returncode == 0:
                    print("✅ Redis started successfully")
                    return True
            except:
                pass
            time.sleep(1)
        
        print("⚠️  Redis started but not responding to ping")
        return True
        
    except subprocess.CalledProcessError:
        print("⚠️  Could not start Redis with Docker. Demo will use in-memory cache.")
        return True

def create_demo_env():
    """Create demo environment file"""
    env_content = """# Omnify Demo Environment
DATABASE_URL=sqlite:///./omnify_demo.db
REDIS_URL=redis://localhost:6379/0
ENVIRONMENT=demo
DEBUG=true
DEMO_MODE=true
CLOUD_VARIANT=local
AI_ENGINE=demo
SECRET_KEY=demo-secret-key-not-for-production
OPENAI_API_KEY=demo-key
"""
    
    with open(".env.demo", "w") as f:
        f.write(env_content)
    print("✅ Demo environment configured")

def start_api_server():
    """Start the FastAPI server"""
    print("🚀 Starting Omnify API server...")
    
    # Set environment variables
    import os
    os.environ["DATABASE_URL"] = "sqlite:///./omnify_demo.db"
    os.environ["REDIS_URL"] = "redis://localhost:6379/0"
    os.environ["ENVIRONMENT"] = "demo"
    os.environ["DEBUG"] = "true"
    os.environ["DEMO_MODE"] = "true"
    os.environ["CLOUD_VARIANT"] = "local"
    os.environ["AI_ENGINE"] = "demo"
    
    try:
        # Start the server
        cmd = [sys.executable, "-m", "uvicorn", "apps.core.main:app", 
               "--host", "0.0.0.0", "--port", "8000", "--reload"]
        
        print("🌐 API will be available at: http://localhost:8000")
        print("📚 API docs will be available at: http://localhost:8000/docs")
        print("🎯 Demo dashboard at: http://localhost:8000/demo")
        print("\n🔄 Starting server... (Press Ctrl+C to stop)")
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(3)
            webbrowser.open("http://localhost:8000/docs")
        
        import threading
        threading.Thread(target=open_browser, daemon=True).start()
        
        # Start the server
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")

def main():
    """Main demo startup function"""
    print("🎯 OMNIFY MARKETING CLOUD - QUICK DEMO STARTER")
    print("=" * 60)
    
    # Check prerequisites
    if not check_python_version():
        return
    
    # Install requirements
    if not install_requirements():
        print("⚠️  Continuing without installing requirements...")
    
    # Start Redis
    start_redis()
    
    # Create demo environment
    create_demo_env()
    
    # Start API server
    start_api_server()

if __name__ == "__main__":
    main()
