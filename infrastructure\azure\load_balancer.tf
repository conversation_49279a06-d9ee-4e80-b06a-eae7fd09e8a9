# Azure Load Balancer and Networking Configuration
# Implements Application Gateway, Front Door, and VNet security

# Virtual Network
resource "azurerm_virtual_network" "omnify_vnet" {
  name                = "omnify-vnet-${var.environment}"
  address_space       = ["10.0.0.0/16"]
  location            = var.location
  resource_group_name = var.resource_group_name

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Subnets
resource "azurerm_subnet" "app_subnet" {
  name                 = "omnify-app-subnet-${var.environment}"
  resource_group_name  = var.resource_group_name
  virtual_network_name = azurerm_virtual_network.omnify_vnet.name
  address_prefixes     = ["********/24"]

  delegation {
    name = "container-instances"
    service_delegation {
      name    = "Microsoft.ContainerInstance/containerGroups"
      actions = ["Microsoft.Network/virtualNetworks/subnets/action"]
    }
  }
}

resource "azurerm_subnet" "database_subnet" {
  name                 = "omnify-db-subnet-${var.environment}"
  resource_group_name  = var.resource_group_name
  virtual_network_name = azurerm_virtual_network.omnify_vnet.name
  address_prefixes     = ["********/24"]

  service_endpoints = ["Microsoft.Sql"]
}

resource "azurerm_subnet" "gateway_subnet" {
  name                 = "omnify-gateway-subnet-${var.environment}"
  resource_group_name  = var.resource_group_name
  virtual_network_name = azurerm_virtual_network.omnify_vnet.name
  address_prefixes     = ["********/24"]
}

# Network Security Groups
resource "azurerm_network_security_group" "app_nsg" {
  name                = "omnify-app-nsg-${var.environment}"
  location            = var.location
  resource_group_name = var.resource_group_name

  security_rule {
    name                       = "AllowHTTP"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "AllowHTTPS"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "AllowAPI"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "8000"
    source_address_prefix      = "********/24"  # Only from gateway subnet
    destination_address_prefix = "*"
  }

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

resource "azurerm_network_security_group" "db_nsg" {
  name                = "omnify-db-nsg-${var.environment}"
  location            = var.location
  resource_group_name = var.resource_group_name

  security_rule {
    name                       = "AllowSQL"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "1433"
    source_address_prefix      = "********/24"  # Only from app subnet
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "AllowRedis"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "6380"
    source_address_prefix      = "********/24"  # Only from app subnet
    destination_address_prefix = "*"
  }

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Associate NSGs with subnets
resource "azurerm_subnet_network_security_group_association" "app_nsg_association" {
  subnet_id                 = azurerm_subnet.app_subnet.id
  network_security_group_id = azurerm_network_security_group.app_nsg.id
}

resource "azurerm_subnet_network_security_group_association" "db_nsg_association" {
  subnet_id                 = azurerm_subnet.database_subnet.id
  network_security_group_id = azurerm_network_security_group.db_nsg.id
}

# Public IP for Application Gateway
resource "azurerm_public_ip" "app_gateway_pip" {
  name                = "omnify-appgw-pip-${var.environment}"
  resource_group_name = var.resource_group_name
  location            = var.location
  allocation_method   = "Static"
  sku                 = "Standard"

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Application Gateway
resource "azurerm_application_gateway" "omnify_appgw" {
  name                = "omnify-appgw-${var.environment}"
  resource_group_name = var.resource_group_name
  location            = var.location

  sku {
    name     = var.environment == "prod" ? "WAF_v2" : "Standard_v2"
    tier     = var.environment == "prod" ? "WAF_v2" : "Standard_v2"
    capacity = 2
  }

  gateway_ip_configuration {
    name      = "omnify-gateway-ip-configuration"
    subnet_id = azurerm_subnet.gateway_subnet.id
  }

  frontend_port {
    name = "omnify-frontend-port-80"
    port = 80
  }

  frontend_port {
    name = "omnify-frontend-port-443"
    port = 443
  }

  frontend_ip_configuration {
    name                 = "omnify-frontend-ip-configuration"
    public_ip_address_id = azurerm_public_ip.app_gateway_pip.id
  }

  backend_address_pool {
    name = "omnify-backend-pool"
  }

  backend_http_settings {
    name                  = "omnify-backend-http-settings"
    cookie_based_affinity = "Disabled"
    path                  = "/health"
    port                  = 8000
    protocol              = "Http"
    request_timeout       = 60
    probe_name            = "omnify-health-probe"
  }

  http_listener {
    name                           = "omnify-http-listener"
    frontend_ip_configuration_name = "omnify-frontend-ip-configuration"
    frontend_port_name             = "omnify-frontend-port-80"
    protocol                       = "Http"
  }

  request_routing_rule {
    name                       = "omnify-routing-rule"
    rule_type                  = "Basic"
    http_listener_name         = "omnify-http-listener"
    backend_address_pool_name  = "omnify-backend-pool"
    backend_http_settings_name = "omnify-backend-http-settings"
    priority                   = 100
  }

  probe {
    name                = "omnify-health-probe"
    protocol            = "Http"
    path                = "/health"
    host                = "127.0.0.1"
    interval            = 30
    timeout             = 30
    unhealthy_threshold = 3
  }

  # WAF Configuration (only for production)
  dynamic "waf_configuration" {
    for_each = var.environment == "prod" ? [1] : []
    content {
      enabled          = true
      firewall_mode    = "Prevention"
      rule_set_type    = "OWASP"
      rule_set_version = "3.2"
    }
  }

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Azure Front Door for global distribution
resource "azurerm_cdn_frontdoor_profile" "omnify_frontdoor" {
  name                = "omnify-frontdoor-${var.environment}"
  resource_group_name = var.resource_group_name
  sku_name            = var.environment == "prod" ? "Premium_AzureFrontDoor" : "Standard_AzureFrontDoor"

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

resource "azurerm_cdn_frontdoor_endpoint" "omnify_endpoint" {
  name                     = "omnify-endpoint-${var.environment}"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.omnify_frontdoor.id

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

resource "azurerm_cdn_frontdoor_origin_group" "omnify_origin_group" {
  name                     = "omnify-origin-group"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.omnify_frontdoor.id

  load_balancing {
    sample_size                 = 4
    successful_samples_required = 3
  }

  health_probe {
    path                = "/health"
    request_type        = "GET"
    protocol            = "Http"
    interval_in_seconds = 100
  }
}

resource "azurerm_cdn_frontdoor_origin" "omnify_origin" {
  name                          = "omnify-origin"
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.omnify_origin_group.id

  enabled                        = true
  host_name                      = azurerm_public_ip.app_gateway_pip.fqdn
  http_port                      = 80
  https_port                     = 443
  origin_host_header             = azurerm_public_ip.app_gateway_pip.fqdn
  priority                       = 1
  weight                         = 1000
  certificate_name_check_enabled = true
}

resource "azurerm_cdn_frontdoor_route" "omnify_route" {
  name                          = "omnify-route"
  cdn_frontdoor_endpoint_id     = azurerm_cdn_frontdoor_endpoint.omnify_endpoint.id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.omnify_origin_group.id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.omnify_origin.id]

  supported_protocols    = ["Http", "Https"]
  patterns_to_match      = ["/*"]
  forwarding_protocol    = "HttpsOnly"
  link_to_default_domain = true
  https_redirect_enabled = true
}

# DDoS Protection Plan
resource "azurerm_network_ddos_protection_plan" "omnify_ddos" {
  count               = var.environment == "prod" ? 1 : 0
  name                = "omnify-ddos-protection-${var.environment}"
  location            = var.location
  resource_group_name = var.resource_group_name

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Private DNS Zone for internal resolution
resource "azurerm_private_dns_zone" "omnify_dns" {
  name                = "omnify.internal"
  resource_group_name = var.resource_group_name

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

resource "azurerm_private_dns_zone_virtual_network_link" "omnify_dns_link" {
  name                  = "omnify-dns-link"
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.omnify_dns.name
  virtual_network_id    = azurerm_virtual_network.omnify_vnet.id

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Variables
variable "location" {
  description = "Azure region"
  type        = string
  default     = "East US"
}

variable "resource_group_name" {
  description = "Resource group name"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

# Outputs
output "vnet_id" {
  description = "ID of the virtual network"
  value       = azurerm_virtual_network.omnify_vnet.id
}

output "app_subnet_id" {
  description = "ID of the application subnet"
  value       = azurerm_subnet.app_subnet.id
}

output "database_subnet_id" {
  description = "ID of the database subnet"
  value       = azurerm_subnet.database_subnet.id
}

output "application_gateway_public_ip" {
  description = "Public IP of the Application Gateway"
  value       = azurerm_public_ip.app_gateway_pip.ip_address
}

output "frontdoor_endpoint_hostname" {
  description = "Front Door endpoint hostname"
  value       = azurerm_cdn_frontdoor_endpoint.omnify_endpoint.host_name
}

output "application_gateway_id" {
  description = "ID of the Application Gateway"
  value       = azurerm_application_gateway.omnify_appgw.id
}
