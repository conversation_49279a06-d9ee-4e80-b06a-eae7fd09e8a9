"""
Analytics API endpoints for ROAS tracking and performance metrics
"""
from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel, Field
import structlog

from apps.core.database import get_db, CampaignMetric, Campaign, Client
from apps.auth.dependencies import get_current_user

logger = structlog.get_logger()

router = APIRouter()

class ROASResponse(BaseModel):
    """ROAS tracking response model"""
    client_id: str
    period_start: datetime
    period_end: datetime
    total_spend: float
    total_revenue: float
    roas: float
    daily_metrics: List[dict]
    threshold_alerts: List[dict] = Field(default_factory=list)

class CACResponse(BaseModel):
    """CAC tracking response model"""
    client_id: str
    period_start: datetime
    period_end: datetime
    total_spend: float
    total_conversions: int
    cac: float
    target_cac: float
    performance_status: str  # "above_target", "on_target", "below_target"

class PerformanceMetrics(BaseModel):
    """Comprehensive performance metrics"""
    roas: float
    cac: float
    total_spend: float
    total_revenue: float
    total_conversions: int
    total_clicks: int
    total_impressions: int
    ctr: float
    conversion_rate: float

@router.get("/roas", response_model=ROASResponse)
async def get_roas_metrics(
    client_id: str = Query(..., description="Client ID"),
    days: int = Query(7, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get real-time ROAS display with 7-day trend and threshold alerts
    """
    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Query campaign metrics
        query = (
            select(CampaignMetric, Campaign.name)
            .join(Campaign)
            .join(Client)
            .where(
                Client.id == client_id,
                CampaignMetric.date >= start_date,
                CampaignMetric.date <= end_date
            )
            .order_by(CampaignMetric.date.desc())
        )
        
        result = await db.execute(query)
        metrics = result.fetchall()
        
        if not metrics:
            raise HTTPException(status_code=404, content="No metrics found for client")
        
        # Calculate totals
        total_spend = sum(m.CampaignMetric.spend for m in metrics)
        total_revenue = sum(m.CampaignMetric.revenue for m in metrics)
        roas = total_revenue / total_spend if total_spend > 0 else 0.0
        
        # Group by date for daily metrics
        daily_data = {}
        for m in metrics:
            date_key = m.CampaignMetric.date.date()
            if date_key not in daily_data:
                daily_data[date_key] = {
                    "date": date_key.isoformat(),
                    "spend": 0.0,
                    "revenue": 0.0,
                    "roas": 0.0
                }
            
            daily_data[date_key]["spend"] += m.CampaignMetric.spend
            daily_data[date_key]["revenue"] += m.CampaignMetric.revenue
        
        # Calculate daily ROAS
        daily_metrics = []
        for date_key, data in sorted(daily_data.items()):
            data["roas"] = data["revenue"] / data["spend"] if data["spend"] > 0 else 0.0
            daily_metrics.append(data)
        
        # Check for threshold alerts
        threshold_alerts = []
        if roas < 2.5:  # ROAS threshold from requirements
            threshold_alerts.append({
                "type": "low_roas",
                "message": f"ROAS ({roas:.2f}) is below threshold (2.5)",
                "severity": "warning" if roas > 2.0 else "critical"
            })
        
        logger.info(
            "ROAS metrics retrieved",
            client_id=client_id,
            roas=roas,
            total_spend=total_spend,
            days=days
        )
        
        return ROASResponse(
            client_id=client_id,
            period_start=start_date,
            period_end=end_date,
            total_spend=total_spend,
            total_revenue=total_revenue,
            roas=roas,
            daily_metrics=daily_metrics,
            threshold_alerts=threshold_alerts
        )
        
    except Exception as e:
        logger.error("Failed to retrieve ROAS metrics", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to retrieve ROAS metrics")

@router.get("/cac", response_model=CACResponse)
async def get_cac_metrics(
    client_id: str = Query(..., description="Client ID"),
    days: int = Query(7, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get CAC metrics for AI decision making
    """
    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Query campaign metrics and target CAC
        query = (
            select(CampaignMetric, Campaign.target_cac)
            .join(Campaign)
            .join(Client)
            .where(
                Client.id == client_id,
                CampaignMetric.date >= start_date,
                CampaignMetric.date <= end_date
            )
        )
        
        result = await db.execute(query)
        metrics = result.fetchall()
        
        if not metrics:
            raise HTTPException(status_code=404, content="No metrics found for client")
        
        # Calculate totals
        total_spend = sum(m.CampaignMetric.spend for m in metrics)
        total_conversions = sum(m.CampaignMetric.conversions for m in metrics)
        cac = total_spend / total_conversions if total_conversions > 0 else 0.0
        
        # Get target CAC (use first campaign's target as default)
        target_cac = metrics[0].target_cac or 50.0
        
        # Determine performance status
        if cac <= target_cac * 0.9:
            performance_status = "below_target"  # Good performance
        elif cac <= target_cac * 1.1:
            performance_status = "on_target"
        else:
            performance_status = "above_target"  # Poor performance
        
        logger.info(
            "CAC metrics retrieved",
            client_id=client_id,
            cac=cac,
            target_cac=target_cac,
            performance_status=performance_status
        )
        
        return CACResponse(
            client_id=client_id,
            period_start=start_date,
            period_end=end_date,
            total_spend=total_spend,
            total_conversions=int(total_conversions),
            cac=cac,
            target_cac=target_cac,
            performance_status=performance_status
        )
        
    except Exception as e:
        logger.error("Failed to retrieve CAC metrics", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to retrieve CAC metrics")

@router.get("/performance", response_model=PerformanceMetrics)
async def get_performance_metrics(
    client_id: str = Query(..., description="Client ID"),
    days: int = Query(7, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get comprehensive performance metrics for dashboard
    """
    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Query aggregated metrics
        query = (
            select(
                func.sum(CampaignMetric.spend).label('total_spend'),
                func.sum(CampaignMetric.revenue).label('total_revenue'),
                func.sum(CampaignMetric.conversions).label('total_conversions'),
                func.sum(CampaignMetric.clicks).label('total_clicks'),
                func.sum(CampaignMetric.impressions).label('total_impressions')
            )
            .join(Campaign)
            .join(Client)
            .where(
                Client.id == client_id,
                CampaignMetric.date >= start_date,
                CampaignMetric.date <= end_date
            )
        )
        
        result = await db.execute(query)
        row = result.fetchone()
        
        if not row or not row.total_spend:
            raise HTTPException(status_code=404, content="No metrics found for client")
        
        # Calculate derived metrics
        total_spend = float(row.total_spend or 0)
        total_revenue = float(row.total_revenue or 0)
        total_conversions = int(row.total_conversions or 0)
        total_clicks = int(row.total_clicks or 0)
        total_impressions = int(row.total_impressions or 0)
        
        roas = total_revenue / total_spend if total_spend > 0 else 0.0
        cac = total_spend / total_conversions if total_conversions > 0 else 0.0
        ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0.0
        conversion_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0.0
        
        logger.info(
            "Performance metrics retrieved",
            client_id=client_id,
            roas=roas,
            cac=cac,
            total_spend=total_spend
        )
        
        return PerformanceMetrics(
            roas=roas,
            cac=cac,
            total_spend=total_spend,
            total_revenue=total_revenue,
            total_conversions=total_conversions,
            total_clicks=total_clicks,
            total_impressions=total_impressions,
            ctr=ctr,
            conversion_rate=conversion_rate
        )
        
    except Exception as e:
        logger.error("Failed to retrieve performance metrics", error=str(e), client_id=client_id)
        raise HTTPException(status_code=500, detail="Failed to retrieve performance metrics")
