# Production Environment Configuration for Omnify Marketing Cloud

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Security
SECRET_KEY=your-super-secure-production-jwt-key-256-bits-minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database (Production)
DATABASE_URL=postgresql+asyncpg://omnify_prod:<EMAIL>:5432/omnify_production

# Redis (Production)
REDIS_URL=redis://prod-redis.omnify.com:6379/0

# OpenAI Configuration
OPENAI_API_KEY=sk-your-production-openai-api-key
OPENAI_MODEL=gpt-4-1106-preview

# Manus RL Configuration
MANUS_RL_API_KEY=your-production-manus-rl-api-key
MANUS_RL_ENDPOINT=https://api.manus.rl/v1

# Google Ads API (Production)
GOOGLE_ADS_DEVELOPER_TOKEN=your-production-google-ads-developer-token
GOOGLE_ADS_CLIENT_ID=your-production-google-ads-client-id
GOOGLE_ADS_CLIENT_SECRET=your-production-google-ads-client-secret

# Meta/Facebook Ads API (Production)
META_APP_ID=your-production-meta-app-id
META_APP_SECRET=your-production-meta-app-secret

# n8n Configuration (Production)
N8N_WEBHOOK_URL=https://workflows.omnify.com/webhook
N8N_API_KEY=your-production-n8n-api-key

# Monitoring & Logging (Production)
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_ENDPOINT=https://prometheus.omnify.com

# Rate Limiting (Production)
RATE_LIMIT_PER_MINUTE=120
RATE_LIMIT_BURST=20

# Email Configuration (Production)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASSWORD=your-sendgrid-api-key

# Retool Dashboard (Production)
RETOOL_API_KEY=your-production-retool-api-key
RETOOL_WORKSPACE_URL=https://omnify.retool.com

# AI Agent Configuration (Production)
AI_CONFIDENCE_THRESHOLD=0.85
MAX_RETRY_ATTEMPTS=3
RETRY_BACKOFF_FACTOR=2.0

# Campaign Management (Production)
DEFAULT_BID_ADJUSTMENT_LIMIT=0.25  # 25% max adjustment in production
MIN_CAMPAIGN_BUDGET=500.0  # $500 minimum for production
MAX_CAMPAIGN_BUDGET=50000.0  # $50k maximum for production

# AWS Configuration (if using AWS)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=omnify-production-data

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/certs/omnify.crt
SSL_KEY_PATH=/etc/ssl/private/omnify.key

# Backup Configuration
BACKUP_S3_BUCKET=omnify-production-backups
BACKUP_RETENTION_DAYS=90

# Feature Flags
ENABLE_MANUS_RL=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_REAL_TIME_BIDDING=true
ENABLE_CHURN_PREDICTION=true
ENABLE_PERSONALIZATION=true

# Performance Tuning
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
REDIS_POOL_SIZE=10
HTTP_TIMEOUT=30
AI_REQUEST_TIMEOUT=60

# Compliance & Privacy
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=2555  # 7 years
ANONYMIZE_CUSTOMER_DATA=true

# Client Limits (Production)
MAX_CAMPAIGNS_PER_CLIENT=100
MAX_CUSTOMERS_PER_CLIENT=1000000
MAX_API_CALLS_PER_DAY=100000

# Alerting Thresholds
HIGH_ERROR_RATE_THRESHOLD=0.05  # 5%
HIGH_LATENCY_THRESHOLD=2.0  # 2 seconds
LOW_AI_CONFIDENCE_THRESHOLD=0.70
BUDGET_EXHAUSTION_THRESHOLD=0.90  # 90%

# Integration Endpoints
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/your/teams/webhook

# CDN Configuration
CDN_URL=https://cdn.omnify.com
STATIC_FILES_URL=https://static.omnify.com

# Load Balancer
LOAD_BALANCER_URL=https://api.omnify.com
HEALTH_CHECK_ENDPOINT=/health

# Scaling Configuration
AUTO_SCALE_MIN_INSTANCES=2
AUTO_SCALE_MAX_INSTANCES=20
AUTO_SCALE_TARGET_CPU=70
AUTO_SCALE_TARGET_MEMORY=80
