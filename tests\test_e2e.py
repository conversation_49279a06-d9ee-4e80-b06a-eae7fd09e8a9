"""
End-to-End Testing Suite for Omnify Marketing Cloud
Tests complete user journeys from registration to AI optimization
"""
import asyncio
import pytest
import httpx
from datetime import datetime, timedelta
from typing import Dict, Any
import structlog

logger = structlog.get_logger()

# Test configuration
BASE_URL = "http://localhost:8000/api/v1"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"
TEST_COMPANY = "Test Company Inc"

class OmnifyTestClient:
    """Test client for Omnify API"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(base_url=base_url)
        self.access_token = None
        self.user_data = None
        self.client_id = None
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def register_user(self, email: str, password: str, company: str) -> Dict[str, Any]:
        """Register a new user"""
        response = await self.client.post("/auth/register", json={
            "email": email,
            "password": password,
            "first_name": "Test",
            "last_name": "User",
            "company_name": company
        })
        
        if response.status_code == 200:
            self.user_data = response.json()
            self.client_id = self.user_data.get("client_id")
        
        return response.json()
    
    async def login(self, email: str, password: str) -> Dict[str, Any]:
        """Login and get access token"""
        response = await self.client.post("/auth/login", json={
            "email": email,
            "password": password
        })
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data["access_token"]
            self.client.headers["Authorization"] = f"Bearer {self.access_token}"
        
        return response.json()
    
    async def get_dashboard_overview(self) -> Dict[str, Any]:
        """Get dashboard overview"""
        response = await self.client.get(f"/dashboard/overview?client_id={self.client_id}")
        return response.json()
    
    async def get_campaigns(self) -> Dict[str, Any]:
        """Get campaign list"""
        response = await self.client.get(f"/dashboard/campaigns?client_id={self.client_id}")
        return response.json()
    
    async def start_onboarding(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """Start onboarding process"""
        response = await self.client.post("/onboarding/start", json={
            "client_id": self.client_id,
            **credentials
        })
        return response.json()
    
    async def get_onboarding_status(self, process_id: str) -> Dict[str, Any]:
        """Get onboarding status"""
        response = await self.client.get(f"/onboarding/status/{process_id}")
        return response.json()
    
    async def trigger_ai_analysis(self, agent: str, campaign_ids: list = None) -> Dict[str, Any]:
        """Trigger AI analysis"""
        response = await self.client.post("/agents/analyze", json={
            "client_id": self.client_id,
            "agent": agent,
            "campaign_ids": campaign_ids or [],
            "force_analysis": True
        })
        return response.json()
    
    async def get_ai_agents_status(self) -> Dict[str, Any]:
        """Get AI agents status"""
        response = await self.client.get(f"/dashboard/ai-agents?client_id={self.client_id}")
        return response.json()
    
    async def get_alerts(self) -> Dict[str, Any]:
        """Get active alerts"""
        response = await self.client.get(f"/dashboard/alerts?client_id={self.client_id}")
        return response.json()

@pytest.mark.asyncio
class TestUserJourney:
    """Test complete user journey"""
    
    async def test_complete_user_registration_and_login(self):
        """Test user registration and login flow"""
        async with OmnifyTestClient() as client:
            # Register user
            registration_data = await client.register_user(
                TEST_EMAIL, 
                TEST_PASSWORD, 
                TEST_COMPANY
            )
            
            assert "id" in registration_data
            assert registration_data["email"] == TEST_EMAIL
            assert registration_data["client_id"] is not None
            
            # Login
            login_data = await client.login(TEST_EMAIL, TEST_PASSWORD)
            
            assert "access_token" in login_data
            assert login_data["token_type"] == "bearer"
            assert client.access_token is not None
            
            logger.info("✅ User registration and login successful")
    
    async def test_dashboard_access_after_login(self):
        """Test dashboard access after login"""
        async with OmnifyTestClient() as client:
            # Register and login
            await client.register_user(TEST_EMAIL, TEST_PASSWORD, TEST_COMPANY)
            await client.login(TEST_EMAIL, TEST_PASSWORD)
            
            # Get dashboard overview
            overview = await client.get_dashboard_overview()
            
            assert "client_id" in overview
            assert "total_spend" in overview
            assert "current_roas" in overview
            assert "ai_decisions_today" in overview
            
            # Get campaigns
            campaigns = await client.get_campaigns()
            
            assert "campaigns" in campaigns
            assert "summary" in campaigns
            
            logger.info("✅ Dashboard access successful")
    
    async def test_onboarding_process(self):
        """Test complete onboarding process"""
        async with OmnifyTestClient() as client:
            # Register and login
            await client.register_user(TEST_EMAIL, TEST_PASSWORD, TEST_COMPANY)
            await client.login(TEST_EMAIL, TEST_PASSWORD)
            
            # Start onboarding with test credentials
            onboarding_data = await client.start_onboarding({
                "google_ads_credentials": {
                    "customer_id": "123-456-7890",
                    "developer_token": "test_dev_token"
                },
                "meta_ads_credentials": {
                    "account_id": "act_123456789",
                    "access_token": "test_access_token"
                }
            })
            
            assert "process_id" in onboarding_data
            assert onboarding_data["status"] == "started"
            
            process_id = onboarding_data["process_id"]
            
            # Check onboarding status
            status = await client.get_onboarding_status(process_id)
            
            assert "current_step" in status
            assert "progress_percentage" in status
            assert "steps" in status
            
            logger.info("✅ Onboarding process initiated successfully")
    
    async def test_ai_agents_functionality(self):
        """Test AI agents functionality"""
        async with OmnifyTestClient() as client:
            # Register and login
            await client.register_user(TEST_EMAIL, TEST_PASSWORD, TEST_COMPANY)
            await client.login(TEST_EMAIL, TEST_PASSWORD)
            
            # Get AI agents status
            agents_status = await client.get_ai_agents_status()
            
            assert "roi_engine" in agents_status
            assert "retention_reactor" in agents_status
            assert "engage_sense" in agents_status
            
            # Test each agent
            for agent_name in ["roi_engine", "retention_reactor", "engage_sense"]:
                agent_data = agents_status[agent_name]
                assert "status" in agent_data
                assert "last_run" in agent_data
            
            # Trigger AI analysis
            analysis_result = await client.trigger_ai_analysis("roi_engine")
            
            assert "status" in analysis_result
            
            logger.info("✅ AI agents functionality verified")
    
    async def test_alert_system(self):
        """Test alert system"""
        async with OmnifyTestClient() as client:
            # Register and login
            await client.register_user(TEST_EMAIL, TEST_PASSWORD, TEST_COMPANY)
            await client.login(TEST_EMAIL, TEST_PASSWORD)
            
            # Get alerts
            alerts = await client.get_alerts()
            
            assert "alerts" in alerts
            assert isinstance(alerts["alerts"], list)
            
            logger.info("✅ Alert system accessible")

@pytest.mark.asyncio
class TestAPIEndpoints:
    """Test individual API endpoints"""
    
    async def test_health_endpoints(self):
        """Test health check endpoints"""
        async with httpx.AsyncClient(base_url=BASE_URL) as client:
            # Main health check
            response = await client.get("/health")
            assert response.status_code == 200
            
            # Auth health check
            response = await client.get("/auth/health")
            assert response.status_code == 200
            
            logger.info("✅ Health endpoints working")
    
    async def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        async with httpx.AsyncClient(base_url=BASE_URL) as client:
            # Test registration
            response = await client.post("/auth/register", json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "API",
                "last_name": "Test",
                "company_name": "API Test Company"
            })
            
            assert response.status_code == 200
            user_data = response.json()
            assert "id" in user_data
            
            # Test login
            response = await client.post("/auth/login", json={
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            })
            
            assert response.status_code == 200
            login_data = response.json()
            assert "access_token" in login_data
            
            # Test protected endpoint
            headers = {"Authorization": f"Bearer {login_data['access_token']}"}
            response = await client.get("/auth/me", headers=headers)
            
            assert response.status_code == 200
            profile_data = response.json()
            assert profile_data["email"] == "<EMAIL>"
            
            logger.info("✅ Authentication endpoints working")

@pytest.mark.asyncio
class TestPerformance:
    """Test system performance"""
    
    async def test_concurrent_requests(self):
        """Test handling concurrent requests"""
        async def make_request():
            async with httpx.AsyncClient(base_url=BASE_URL) as client:
                response = await client.get("/health")
                return response.status_code
        
        # Make 10 concurrent requests
        tasks = [make_request() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        
        # All requests should succeed
        assert all(status == 200 for status in results)
        
        logger.info("✅ Concurrent requests handled successfully")
    
    async def test_response_times(self):
        """Test API response times"""
        async with httpx.AsyncClient(base_url=BASE_URL) as client:
            start_time = datetime.utcnow()
            
            response = await client.get("/health")
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            assert response.status_code == 200
            assert response_time < 1.0  # Should respond within 1 second
            
            logger.info(f"✅ Response time: {response_time:.3f}s")

@pytest.mark.asyncio
class TestDataIntegrity:
    """Test data integrity and consistency"""
    
    async def test_user_data_consistency(self):
        """Test user data consistency across endpoints"""
        async with OmnifyTestClient() as client:
            # Register user
            registration_data = await client.register_user(
                "<EMAIL>",
                TEST_PASSWORD,
                "Consistency Test Co"
            )
            
            # Login
            await client.login("<EMAIL>", TEST_PASSWORD)
            
            # Get user profile
            response = await client.client.get("/auth/me")
            profile_data = response.json()
            
            # Verify data consistency
            assert profile_data["email"] == registration_data["email"]
            assert profile_data["client_id"] == registration_data["client_id"]
            assert profile_data["first_name"] == registration_data["first_name"]
            
            logger.info("✅ User data consistency verified")

# Test runner
async def run_all_tests():
    """Run all tests"""
    test_classes = [
        TestUserJourney,
        TestAPIEndpoints,
        TestPerformance,
        TestDataIntegrity
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = []
    
    for test_class in test_classes:
        instance = test_class()
        methods = [method for method in dir(instance) if method.startswith('test_')]
        
        for method_name in methods:
            total_tests += 1
            try:
                method = getattr(instance, method_name)
                await method()
                passed_tests += 1
                print(f"✅ {test_class.__name__}.{method_name}")
            except Exception as e:
                failed_tests.append(f"{test_class.__name__}.{method_name}: {str(e)}")
                print(f"❌ {test_class.__name__}.{method_name}: {str(e)}")
    
    print(f"\n📊 Test Results:")
    print(f"Total: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {len(failed_tests)}")
    
    if failed_tests:
        print(f"\n❌ Failed Tests:")
        for failure in failed_tests:
            print(f"  - {failure}")
    
    return len(failed_tests) == 0

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
