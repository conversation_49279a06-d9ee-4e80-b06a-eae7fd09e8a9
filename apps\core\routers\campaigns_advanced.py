"""
Advanced Campaign Management Router
Enhanced campaign operations with AI insights and optimization
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from pydantic import BaseModel, Field
import structlog

from apps.core.database import get_db, Campaign, CampaignMetric, AIDecision
from apps.auth.dependencies import get_current_active_user
from lib.ai.decision_engine import ROIEngineX
from lib.monitoring.alert_manager import alert_manager
from apps.core.websocket_manager import notification_service

logger = structlog.get_logger()

router = APIRouter()

class CampaignOptimizationRequest(BaseModel):
    """Campaign optimization request"""
    campaign_ids: List[str]
    optimization_type: str = Field(..., description="Type of optimization: bid, budget, targeting, creative")
    force_optimization: bool = False
    target_metrics: Optional[Dict[str, float]] = None

class CampaignBulkAction(BaseModel):
    """Bulk campaign action"""
    campaign_ids: List[str]
    action: str = Field(..., description="Action: pause, resume, archive, duplicate")
    parameters: Optional[Dict[str, Any]] = None

class CampaignInsights(BaseModel):
    """Campaign insights and recommendations"""
    campaign_id: str
    performance_score: float
    ai_recommendations: List[Dict[str, Any]]
    optimization_opportunities: List[Dict[str, Any]]
    risk_factors: List[Dict[str, Any]]
    predicted_performance: Dict[str, float]

class CampaignComparison(BaseModel):
    """Campaign comparison analysis"""
    campaigns: List[str]
    metrics: Dict[str, Dict[str, float]]
    best_performer: str
    recommendations: List[str]

@router.get("/campaigns/{campaign_id}/insights", response_model=CampaignInsights)
async def get_campaign_insights(
    campaign_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get AI-powered campaign insights and recommendations"""
    
    # Get campaign
    campaign_result = await db.execute(
        select(Campaign).where(
            and_(
                Campaign.campaign_id == campaign_id,
                Campaign.client_id == current_user.client_id
            )
        )
    )
    campaign = campaign_result.scalar_one_or_none()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    # Get recent metrics (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    metrics_result = await db.execute(
        select(CampaignMetric).where(
            and_(
                CampaignMetric.campaign_id == campaign.id,
                CampaignMetric.date >= thirty_days_ago.date()
            )
        ).order_by(CampaignMetric.date.desc())
    )
    metrics = metrics_result.scalars().all()
    
    if not metrics:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No metrics found for campaign"
        )
    
    # Calculate performance score
    avg_roas = sum(m.roas for m in metrics) / len(metrics)
    avg_ctr = sum(m.ctr for m in metrics) / len(metrics)
    budget_efficiency = campaign.budget / sum(m.spend for m in metrics) if sum(m.spend for m in metrics) > 0 else 0
    
    performance_score = min(100, (avg_roas * 20) + (avg_ctr * 100) + (budget_efficiency * 30))
    
    # Generate AI recommendations
    ai_recommendations = []
    optimization_opportunities = []
    risk_factors = []
    
    # ROAS analysis
    if avg_roas < 2.0:
        risk_factors.append({
            "type": "low_roas",
            "severity": "high",
            "description": f"ROAS ({avg_roas:.2f}) is below target (2.0)",
            "impact": "Revenue generation is inefficient"
        })
        optimization_opportunities.append({
            "type": "bid_optimization",
            "priority": "high",
            "description": "Optimize bids to improve ROAS",
            "expected_improvement": "15-25% ROAS increase"
        })
    
    # CTR analysis
    if avg_ctr < 2.0:
        optimization_opportunities.append({
            "type": "creative_optimization",
            "priority": "medium",
            "description": "Improve ad creatives to increase CTR",
            "expected_improvement": "10-20% CTR increase"
        })
    
    # Budget utilization
    total_spend = sum(m.spend for m in metrics)
    budget_utilization = (total_spend / campaign.budget) * 100 if campaign.budget > 0 else 0
    
    if budget_utilization > 90:
        risk_factors.append({
            "type": "budget_exhaustion",
            "severity": "medium",
            "description": f"Budget {budget_utilization:.1f}% utilized",
            "impact": "Campaign may pause due to budget limits"
        })
    
    # AI recommendations based on performance
    if performance_score < 60:
        ai_recommendations.append({
            "type": "comprehensive_optimization",
            "confidence": 0.85,
            "description": "Campaign needs comprehensive optimization",
            "actions": ["Bid adjustment", "Audience refinement", "Creative refresh"]
        })
    elif performance_score < 80:
        ai_recommendations.append({
            "type": "targeted_optimization",
            "confidence": 0.75,
            "description": "Campaign has specific optimization opportunities",
            "actions": ["Fine-tune targeting", "A/B test creatives"]
        })
    else:
        ai_recommendations.append({
            "type": "scaling_opportunity",
            "confidence": 0.90,
            "description": "Campaign is performing well, consider scaling",
            "actions": ["Increase budget", "Expand targeting"]
        })
    
    # Predict future performance
    recent_trend = metrics[:7]  # Last 7 days
    if len(recent_trend) >= 7:
        trend_roas = sum(m.roas for m in recent_trend) / len(recent_trend)
        predicted_performance = {
            "roas_7_days": trend_roas * 1.05,  # Slight improvement expected
            "spend_7_days": sum(m.spend for m in recent_trend) * 1.1,
            "revenue_7_days": sum(m.revenue for m in recent_trend) * 1.15
        }
    else:
        predicted_performance = {
            "roas_7_days": avg_roas,
            "spend_7_days": total_spend / 30 * 7,
            "revenue_7_days": sum(m.revenue for m in metrics) / 30 * 7
        }
    
    return CampaignInsights(
        campaign_id=campaign_id,
        performance_score=performance_score,
        ai_recommendations=ai_recommendations,
        optimization_opportunities=optimization_opportunities,
        risk_factors=risk_factors,
        predicted_performance=predicted_performance
    )

@router.post("/campaigns/optimize")
async def optimize_campaigns(
    optimization_request: CampaignOptimizationRequest,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Trigger AI optimization for multiple campaigns"""
    
    # Validate campaigns belong to user
    campaigns_result = await db.execute(
        select(Campaign).where(
            and_(
                Campaign.campaign_id.in_(optimization_request.campaign_ids),
                Campaign.client_id == current_user.client_id
            )
        )
    )
    campaigns = campaigns_result.scalars().all()
    
    if len(campaigns) != len(optimization_request.campaign_ids):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="One or more campaigns not found"
        )
    
    # Start optimization process in background
    background_tasks.add_task(
        run_campaign_optimization,
        campaigns,
        optimization_request,
        current_user.client_id
    )
    
    logger.info(
        "Campaign optimization started",
        client_id=current_user.client_id,
        campaign_count=len(campaigns),
        optimization_type=optimization_request.optimization_type
    )
    
    return {
        "status": "optimization_started",
        "campaign_count": len(campaigns),
        "optimization_type": optimization_request.optimization_type,
        "estimated_completion": "5-10 minutes"
    }

@router.post("/campaigns/bulk-action")
async def bulk_campaign_action(
    bulk_action: CampaignBulkAction,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Perform bulk actions on multiple campaigns"""
    
    # Validate campaigns
    campaigns_result = await db.execute(
        select(Campaign).where(
            and_(
                Campaign.campaign_id.in_(bulk_action.campaign_ids),
                Campaign.client_id == current_user.client_id
            )
        )
    )
    campaigns = campaigns_result.scalars().all()
    
    if len(campaigns) != len(bulk_action.campaign_ids):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="One or more campaigns not found"
        )
    
    results = []
    
    for campaign in campaigns:
        try:
            if bulk_action.action == "pause":
                campaign.status = "paused"
                result = {"campaign_id": campaign.campaign_id, "status": "paused"}
                
            elif bulk_action.action == "resume":
                campaign.status = "active"
                result = {"campaign_id": campaign.campaign_id, "status": "resumed"}
                
            elif bulk_action.action == "archive":
                campaign.status = "archived"
                result = {"campaign_id": campaign.campaign_id, "status": "archived"}
                
            elif bulk_action.action == "duplicate":
                # Create duplicate campaign
                new_campaign = Campaign(
                    client_id=campaign.client_id,
                    name=f"{campaign.name} (Copy)",
                    platform=campaign.platform,
                    campaign_id=f"{campaign.campaign_id}_copy_{int(datetime.utcnow().timestamp())}",
                    budget=campaign.budget,
                    target_cac=campaign.target_cac,
                    status="paused"  # Start paused
                )
                db.add(new_campaign)
                result = {"campaign_id": campaign.campaign_id, "status": "duplicated", "new_campaign_id": new_campaign.campaign_id}
                
            else:
                result = {"campaign_id": campaign.campaign_id, "status": "error", "message": "Unknown action"}
            
            results.append(result)
            
        except Exception as e:
            logger.error("Bulk action failed for campaign", error=str(e), campaign_id=campaign.campaign_id)
            results.append({
                "campaign_id": campaign.campaign_id,
                "status": "error",
                "message": str(e)
            })
    
    await db.commit()
    
    logger.info(
        "Bulk campaign action completed",
        client_id=current_user.client_id,
        action=bulk_action.action,
        campaign_count=len(campaigns)
    )
    
    return {
        "action": bulk_action.action,
        "total_campaigns": len(campaigns),
        "results": results
    }

@router.post("/campaigns/compare", response_model=CampaignComparison)
async def compare_campaigns(
    campaign_ids: List[str],
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Compare performance of multiple campaigns"""
    
    if len(campaign_ids) < 2:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least 2 campaigns required for comparison"
        )
    
    # Get campaigns and their metrics
    campaigns_result = await db.execute(
        select(Campaign).where(
            and_(
                Campaign.campaign_id.in_(campaign_ids),
                Campaign.client_id == current_user.client_id
            )
        )
    )
    campaigns = campaigns_result.scalars().all()
    
    if len(campaigns) != len(campaign_ids):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="One or more campaigns not found"
        )
    
    # Get metrics for last 30 days
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    comparison_metrics = {}
    
    for campaign in campaigns:
        metrics_result = await db.execute(
            select(CampaignMetric).where(
                and_(
                    CampaignMetric.campaign_id == campaign.id,
                    CampaignMetric.date >= thirty_days_ago.date()
                )
            )
        )
        metrics = metrics_result.scalars().all()
        
        if metrics:
            comparison_metrics[campaign.campaign_id] = {
                "total_spend": sum(m.spend for m in metrics),
                "total_revenue": sum(m.revenue for m in metrics),
                "avg_roas": sum(m.roas for m in metrics) / len(metrics),
                "avg_ctr": sum(m.ctr for m in metrics) / len(metrics),
                "total_conversions": sum(m.conversions for m in metrics),
                "avg_cpc": sum(m.cpc for m in metrics) / len(metrics) if metrics else 0
            }
        else:
            comparison_metrics[campaign.campaign_id] = {
                "total_spend": 0,
                "total_revenue": 0,
                "avg_roas": 0,
                "avg_ctr": 0,
                "total_conversions": 0,
                "avg_cpc": 0
            }
    
    # Determine best performer (highest ROAS)
    best_performer = max(
        comparison_metrics.keys(),
        key=lambda k: comparison_metrics[k]["avg_roas"]
    )
    
    # Generate recommendations
    recommendations = []
    best_metrics = comparison_metrics[best_performer]
    
    for campaign_id, metrics in comparison_metrics.items():
        if campaign_id != best_performer:
            if metrics["avg_roas"] < best_metrics["avg_roas"] * 0.8:
                recommendations.append(f"Campaign {campaign_id}: Optimize for ROAS improvement")
            if metrics["avg_ctr"] < best_metrics["avg_ctr"] * 0.8:
                recommendations.append(f"Campaign {campaign_id}: Improve ad creatives for better CTR")
            if metrics["avg_cpc"] > best_metrics["avg_cpc"] * 1.2:
                recommendations.append(f"Campaign {campaign_id}: Optimize bids to reduce CPC")
    
    return CampaignComparison(
        campaigns=campaign_ids,
        metrics=comparison_metrics,
        best_performer=best_performer,
        recommendations=recommendations
    )

async def run_campaign_optimization(
    campaigns: List[Campaign],
    optimization_request: CampaignOptimizationRequest,
    client_id: str
):
    """Background task to run campaign optimization"""
    
    try:
        roi_engine = ROIEngineX()
        
        for campaign in campaigns:
            # Simulate optimization process
            await asyncio.sleep(2)  # Simulate processing time
            
            # Create AI decision record
            decision = AIDecision(
                client_id=client_id,
                campaign_id=campaign.id,
                agent_name="roi_engine",
                decision_type=optimization_request.optimization_type,
                confidence=0.85,
                reasoning=f"Automated {optimization_request.optimization_type} optimization",
                parameters={
                    "optimization_type": optimization_request.optimization_type,
                    "target_metrics": optimization_request.target_metrics or {},
                    "expected_improvement": "10-20%"
                },
                status="executed"
            )
            
            # Save decision (would need database session)
            logger.info(
                "Campaign optimization completed",
                campaign_id=campaign.campaign_id,
                optimization_type=optimization_request.optimization_type
            )
            
            # Send real-time notification
            await notification_service.notify_ai_optimization(
                client_id=client_id,
                agent_name="ROI Engine X™",
                campaign_id=campaign.campaign_id,
                optimization_type=optimization_request.optimization_type,
                expected_impact={
                    "confidence": 0.85,
                    "expected_improvement": "10-20%",
                    "metric": "ROAS"
                }
            )
    
    except Exception as e:
        logger.error("Campaign optimization failed", error=str(e), client_id=client_id)
