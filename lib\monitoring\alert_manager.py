"""
Advanced Monitoring and Alerting System for Omnify Marketing Cloud
"""
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
import structlog
from pydantic import BaseModel, Field
import json

from apps.core.config import settings
from lib.integrations.email_service import email_manager, EmailRecipient
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class AlertSeverity(str, Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class AlertCategory(str, Enum):
    """Alert categories"""
    PERFORMANCE = "performance"
    BUDGET = "budget"
    CHURN = "churn"
    SYSTEM = "system"
    AI_AGENT = "ai_agent"
    SECURITY = "security"

class AlertStatus(str, Enum):
    """Alert status"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"

class Alert(BaseModel):
    """Alert model"""
    alert_id: str
    title: str
    message: str
    category: AlertCategory
    severity: AlertSeverity
    status: AlertStatus = AlertStatus.ACTIVE
    
    # Context
    client_id: Optional[str] = None
    campaign_ids: List[str] = Field(default_factory=list)
    metric_values: Dict[str, float] = Field(default_factory=dict)
    
    # Timing
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    resolved_at: Optional[datetime] = None
    
    # Escalation
    escalation_level: int = 0
    max_escalations: int = 3
    escalation_interval: int = 300  # 5 minutes
    
    # Notification
    notification_channels: List[str] = Field(default_factory=list)
    last_notification: Optional[datetime] = None

class AlertRule(BaseModel):
    """Alert rule configuration"""
    rule_id: str
    name: str
    description: str
    category: AlertCategory
    severity: AlertSeverity
    
    # Conditions
    metric_name: str
    operator: str  # "gt", "lt", "eq", "gte", "lte"
    threshold: float
    duration: int = 300  # Seconds the condition must persist
    
    # Scope
    client_ids: List[str] = Field(default_factory=list)  # Empty = all clients
    campaign_filters: Dict[str, Any] = Field(default_factory=dict)
    
    # Notification
    notification_channels: List[str] = Field(default_factory=list)
    cooldown_period: int = 3600  # 1 hour between similar alerts
    
    # Status
    is_enabled: bool = True
    last_triggered: Optional[datetime] = None

class NotificationChannel(BaseModel):
    """Notification channel configuration"""
    channel_id: str
    name: str
    type: str  # "email", "slack", "webhook", "sms"
    config: Dict[str, Any]
    is_enabled: bool = True

class AlertManager:
    """Manages alerts, rules, and notifications"""
    
    def __init__(self):
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_rules: Dict[str, AlertRule] = {}
        self.notification_channels: Dict[str, NotificationChannel] = {}
        self.alert_history: List[Alert] = []
        self._initialize_default_rules()
        self._initialize_notification_channels()
    
    def _initialize_default_rules(self):
        """Initialize default alert rules"""
        default_rules = [
            AlertRule(
                rule_id="low_roas_critical",
                name="Critical Low ROAS",
                description="Campaign ROAS below 1.5 for more than 15 minutes",
                category=AlertCategory.PERFORMANCE,
                severity=AlertSeverity.CRITICAL,
                metric_name="roas",
                operator="lt",
                threshold=1.5,
                duration=900,  # 15 minutes
                notification_channels=["email_alerts", "slack_critical"]
            ),
            AlertRule(
                rule_id="high_cac_warning",
                name="High CAC Warning",
                description="Campaign CAC 50% above target",
                category=AlertCategory.PERFORMANCE,
                severity=AlertSeverity.WARNING,
                metric_name="cac_vs_target",
                operator="gt",
                threshold=50.0,  # 50% above target
                duration=600,  # 10 minutes
                notification_channels=["email_alerts"]
            ),
            AlertRule(
                rule_id="budget_exhaustion",
                name="Budget Exhaustion Alert",
                description="Campaign budget 90% exhausted",
                category=AlertCategory.BUDGET,
                severity=AlertSeverity.WARNING,
                metric_name="budget_utilization",
                operator="gte",
                threshold=90.0,
                duration=0,  # Immediate
                notification_channels=["email_alerts", "slack_budget"]
            ),
            AlertRule(
                rule_id="high_churn_risk",
                name="High Churn Risk Customers",
                description="Multiple customers with >80% churn probability",
                category=AlertCategory.CHURN,
                severity=AlertSeverity.WARNING,
                metric_name="high_churn_customer_count",
                operator="gte",
                threshold=10.0,
                duration=3600,  # 1 hour
                notification_channels=["email_alerts"]
            ),
            AlertRule(
                rule_id="ai_agent_failures",
                name="AI Agent Failures",
                description="High number of AI agent failures",
                category=AlertCategory.AI_AGENT,
                severity=AlertSeverity.WARNING,
                metric_name="ai_failure_rate",
                operator="gt",
                threshold=20.0,  # 20% failure rate
                duration=1800,  # 30 minutes
                notification_channels=["email_alerts", "slack_tech"]
            ),
            AlertRule(
                rule_id="system_error_rate",
                name="High System Error Rate",
                description="API error rate above 5%",
                category=AlertCategory.SYSTEM,
                severity=AlertSeverity.CRITICAL,
                metric_name="error_rate",
                operator="gt",
                threshold=5.0,
                duration=300,  # 5 minutes
                notification_channels=["email_alerts", "slack_critical"]
            )
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.rule_id] = rule
    
    def _initialize_notification_channels(self):
        """Initialize notification channels"""
        channels = [
            NotificationChannel(
                channel_id="email_alerts",
                name="Email Alerts",
                type="email",
                config={
                    "recipients": ["<EMAIL>", "<EMAIL>"],
                    "template_mapping": {
                        AlertCategory.PERFORMANCE: "alert_performance",
                        AlertCategory.BUDGET: "alert_budget",
                        AlertCategory.CHURN: "alert_churn",
                        AlertCategory.SYSTEM: "alert_system",
                        AlertCategory.AI_AGENT: "alert_ai_agent"
                    }
                }
            ),
            NotificationChannel(
                channel_id="slack_critical",
                name="Slack Critical Alerts",
                type="slack",
                config={
                    "webhook_url": getattr(settings, 'SLACK_WEBHOOK_URL', ''),
                    "channel": "#critical-alerts",
                    "username": "Omnify Alert Bot"
                }
            ),
            NotificationChannel(
                channel_id="slack_budget",
                name="Slack Budget Alerts",
                type="slack",
                config={
                    "webhook_url": getattr(settings, 'SLACK_WEBHOOK_URL', ''),
                    "channel": "#budget-alerts",
                    "username": "Omnify Budget Bot"
                }
            ),
            NotificationChannel(
                channel_id="slack_tech",
                name="Slack Technical Alerts",
                type="slack",
                config={
                    "webhook_url": getattr(settings, 'SLACK_WEBHOOK_URL', ''),
                    "channel": "#tech-alerts",
                    "username": "Omnify Tech Bot"
                }
            ),
            NotificationChannel(
                channel_id="webhook_external",
                name="External Webhook",
                type="webhook",
                config={
                    "url": getattr(settings, 'EXTERNAL_WEBHOOK_URL', ''),
                    "headers": {"Authorization": "Bearer token"},
                    "timeout": 10
                }
            )
        ]
        
        for channel in channels:
            self.notification_channels[channel.channel_id] = channel
    
    async def check_metric(
        self, 
        metric_name: str, 
        value: float, 
        client_id: str,
        campaign_id: Optional[str] = None,
        additional_context: Dict[str, Any] = None
    ):
        """Check metric against alert rules"""
        
        for rule in self.alert_rules.values():
            if not rule.is_enabled:
                continue
            
            if rule.metric_name != metric_name:
                continue
            
            # Check client scope
            if rule.client_ids and client_id not in rule.client_ids:
                continue
            
            # Check if condition is met
            if not self._evaluate_condition(rule.operator, value, rule.threshold):
                continue
            
            # Check cooldown period
            if rule.last_triggered:
                time_since_last = (datetime.utcnow() - rule.last_triggered).total_seconds()
                if time_since_last < rule.cooldown_period:
                    continue
            
            # Create or update alert
            alert_id = f"{rule.rule_id}_{client_id}_{campaign_id or 'all'}"
            
            if alert_id in self.active_alerts:
                # Update existing alert
                alert = self.active_alerts[alert_id]
                alert.updated_at = datetime.utcnow()
                alert.metric_values[metric_name] = value
            else:
                # Create new alert
                alert = Alert(
                    alert_id=alert_id,
                    title=rule.name,
                    message=self._format_alert_message(rule, value, additional_context),
                    category=rule.category,
                    severity=rule.severity,
                    client_id=client_id,
                    campaign_ids=[campaign_id] if campaign_id else [],
                    metric_values={metric_name: value},
                    notification_channels=rule.notification_channels
                )
                
                self.active_alerts[alert_id] = alert
                rule.last_triggered = datetime.utcnow()
                
                # Send notifications
                await self._send_notifications(alert)
                
                logger.info(
                    "Alert triggered",
                    alert_id=alert_id,
                    rule_id=rule.rule_id,
                    metric_name=metric_name,
                    value=value,
                    threshold=rule.threshold
                )
    
    def _evaluate_condition(self, operator: str, value: float, threshold: float) -> bool:
        """Evaluate alert condition"""
        if operator == "gt":
            return value > threshold
        elif operator == "gte":
            return value >= threshold
        elif operator == "lt":
            return value < threshold
        elif operator == "lte":
            return value <= threshold
        elif operator == "eq":
            return abs(value - threshold) < 0.001
        else:
            return False
    
    def _format_alert_message(
        self, 
        rule: AlertRule, 
        value: float, 
        context: Dict[str, Any] = None
    ) -> str:
        """Format alert message"""
        context = context or {}
        
        message = f"{rule.description}\n"
        message += f"Current value: {value:.2f}\n"
        message += f"Threshold: {rule.threshold:.2f}\n"
        
        if context:
            message += "\nAdditional context:\n"
            for key, val in context.items():
                message += f"- {key}: {val}\n"
        
        return message.strip()
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def _send_notifications(self, alert: Alert):
        """Send notifications for alert"""
        
        for channel_id in alert.notification_channels:
            channel = self.notification_channels.get(channel_id)
            if not channel or not channel.is_enabled:
                continue
            
            try:
                if channel.type == "email":
                    await self._send_email_notification(alert, channel)
                elif channel.type == "slack":
                    await self._send_slack_notification(alert, channel)
                elif channel.type == "webhook":
                    await self._send_webhook_notification(alert, channel)
                
                logger.info(
                    "Notification sent",
                    alert_id=alert.alert_id,
                    channel_id=channel_id,
                    channel_type=channel.type
                )
                
            except Exception as e:
                logger.error(
                    "Failed to send notification",
                    alert_id=alert.alert_id,
                    channel_id=channel_id,
                    error=str(e)
                )
        
        alert.last_notification = datetime.utcnow()
    
    async def _send_email_notification(self, alert: Alert, channel: NotificationChannel):
        """Send email notification"""
        recipients = channel.config.get("recipients", [])
        template_mapping = channel.config.get("template_mapping", {})
        
        template_id = template_mapping.get(alert.category, "alert_generic")
        
        for recipient_email in recipients:
            recipient = EmailRecipient(
                email=recipient_email,
                variables={
                    "alert_title": alert.title,
                    "alert_message": alert.message,
                    "alert_severity": alert.severity.value,
                    "alert_category": alert.category.value,
                    "client_id": alert.client_id or "N/A",
                    "created_at": alert.created_at.strftime("%Y-%m-%d %H:%M:%S UTC"),
                    "metric_values": json.dumps(alert.metric_values, indent=2)
                }
            )
            
            await email_manager.send_email(recipient, template_id)
    
    async def _send_slack_notification(self, alert: Alert, channel: NotificationChannel):
        """Send Slack notification"""
        webhook_url = channel.config.get("webhook_url")
        if not webhook_url:
            return
        
        # Format Slack message
        color = {
            AlertSeverity.INFO: "good",
            AlertSeverity.WARNING: "warning", 
            AlertSeverity.CRITICAL: "danger",
            AlertSeverity.EMERGENCY: "danger"
        }.get(alert.severity, "warning")
        
        payload = {
            "username": channel.config.get("username", "Omnify Alert Bot"),
            "channel": channel.config.get("channel", "#alerts"),
            "attachments": [{
                "color": color,
                "title": f"🚨 {alert.title}",
                "text": alert.message,
                "fields": [
                    {"title": "Severity", "value": alert.severity.value.upper(), "short": True},
                    {"title": "Category", "value": alert.category.value.title(), "short": True},
                    {"title": "Client ID", "value": alert.client_id or "N/A", "short": True},
                    {"title": "Time", "value": alert.created_at.strftime("%Y-%m-%d %H:%M:%S UTC"), "short": True}
                ],
                "footer": "Omnify Marketing Cloud",
                "ts": int(alert.created_at.timestamp())
            }]
        }
        
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
    
    async def _send_webhook_notification(self, alert: Alert, channel: NotificationChannel):
        """Send webhook notification"""
        url = channel.config.get("url")
        if not url:
            return
        
        headers = channel.config.get("headers", {})
        timeout = channel.config.get("timeout", 10)
        
        payload = {
            "alert_id": alert.alert_id,
            "title": alert.title,
            "message": alert.message,
            "category": alert.category.value,
            "severity": alert.severity.value,
            "client_id": alert.client_id,
            "campaign_ids": alert.campaign_ids,
            "metric_values": alert.metric_values,
            "created_at": alert.created_at.isoformat(),
            "status": alert.status.value
        }
        
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url, 
                json=payload, 
                headers=headers, 
                timeout=timeout
            )
            response.raise_for_status()
    
    async def resolve_alert(self, alert_id: str, resolved_by: str = "system"):
        """Resolve an active alert"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.utcnow()
            alert.updated_at = datetime.utcnow()
            
            # Move to history
            self.alert_history.append(alert)
            del self.active_alerts[alert_id]
            
            logger.info(
                "Alert resolved",
                alert_id=alert_id,
                resolved_by=resolved_by
            )
    
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str):
        """Acknowledge an alert"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.updated_at = datetime.utcnow()
            
            logger.info(
                "Alert acknowledged",
                alert_id=alert_id,
                acknowledged_by=acknowledged_by
            )
    
    def get_active_alerts(
        self, 
        client_id: Optional[str] = None,
        severity: Optional[AlertSeverity] = None,
        category: Optional[AlertCategory] = None
    ) -> List[Alert]:
        """Get active alerts with optional filters"""
        alerts = list(self.active_alerts.values())
        
        if client_id:
            alerts = [a for a in alerts if a.client_id == client_id]
        
        if severity:
            alerts = [a for a in alerts if a.severity == severity]
        
        if category:
            alerts = [a for a in alerts if a.category == category]
        
        # Sort by severity and creation time
        severity_order = {
            AlertSeverity.EMERGENCY: 0,
            AlertSeverity.CRITICAL: 1,
            AlertSeverity.WARNING: 2,
            AlertSeverity.INFO: 3
        }
        
        alerts.sort(key=lambda x: (severity_order.get(x.severity, 4), x.created_at), reverse=True)
        
        return alerts
    
    async def cleanup_old_alerts(self, max_age_hours: int = 24):
        """Clean up old resolved alerts"""
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        
        # Remove old alerts from history
        self.alert_history = [
            alert for alert in self.alert_history 
            if alert.resolved_at and alert.resolved_at > cutoff_time
        ]
        
        logger.info(
            "Alert cleanup completed",
            remaining_history_count=len(self.alert_history)
        )

# Global alert manager instance
alert_manager = AlertManager()
