"""
Retention Reactor Pro™ - Churn Prediction and Automated Retention Actions
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import structlog
from pydantic import BaseModel, Field
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib
import openai

from apps.core.config import settings
from lib.utils.retry import retry_with_backoff

logger = structlog.get_logger()

class CustomerData(BaseModel):
    """Customer data for churn prediction"""
    customer_id: str
    days_since_last_purchase: int
    total_purchases: int
    average_order_value: float
    total_spent: float
    email_engagement_rate: float
    support_tickets: int
    last_login_days: int
    subscription_tier: str
    geographic_region: str

class ChurnPrediction(BaseModel):
    """Churn prediction result"""
    customer_id: str
    churn_probability: float
    risk_level: str  # "low", "medium", "high", "critical"
    key_factors: List[str]
    recommended_actions: List[Dict[str, Any]]
    confidence: float
    model_version: str

class RetentionAction(BaseModel):
    """Retention action to execute"""
    action_type: str  # "email_campaign", "discount_offer", "personal_outreach", "product_recommendation"
    customer_id: str
    parameters: Dict[str, Any]
    priority: int  # 1-5, 5 being highest
    estimated_impact: float
    cost: float

class RetentionReactorPro:
    """
    Retention Reactor Pro™ - AI agent for churn prediction and retention automation
    """
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.feature_columns = [
            'days_since_last_purchase', 'total_purchases', 'average_order_value',
            'total_spent', 'email_engagement_rate', 'support_tickets', 
            'last_login_days'
        ]
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
    async def initialize_model(self):
        """Initialize or load the churn prediction model"""
        try:
            # Try to load existing model
            self.model = joblib.load('models/churn_model.pkl')
            self.scaler = joblib.load('models/churn_scaler.pkl')
            logger.info("Loaded existing churn prediction model")
        except FileNotFoundError:
            # Create and train a basic model with synthetic data
            await self._train_initial_model()
            logger.info("Created new churn prediction model")
    
    async def _train_initial_model(self):
        """Train initial model with synthetic data (replace with real data)"""
        # Generate synthetic training data
        np.random.seed(42)
        n_samples = 1000
        
        # Features
        X = np.random.rand(n_samples, len(self.feature_columns))
        X[:, 0] *= 365  # days_since_last_purchase
        X[:, 1] *= 50   # total_purchases
        X[:, 2] *= 200  # average_order_value
        X[:, 3] *= 5000 # total_spent
        X[:, 4] *= 100  # email_engagement_rate
        X[:, 5] *= 10   # support_tickets
        X[:, 6] *= 180  # last_login_days
        
        # Target (churn probability based on heuristics)
        y = (
            (X[:, 0] > 90) * 0.3 +  # Long time since purchase
            (X[:, 1] < 5) * 0.2 +   # Few purchases
            (X[:, 4] < 20) * 0.2 +  # Low email engagement
            (X[:, 5] > 5) * 0.1 +   # Many support tickets
            (X[:, 6] > 30) * 0.2    # Long time since login
        ) > 0.5
        
        # Train model
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_scaled, y)
        
        # Save model
        import os
        os.makedirs('models', exist_ok=True)
        joblib.dump(self.model, 'models/churn_model.pkl')
        joblib.dump(self.scaler, 'models/churn_scaler.pkl')
    
    @retry_with_backoff(max_retries=3, backoff_factor=2.0)
    async def predict_churn(self, customer_data: CustomerData) -> ChurnPrediction:
        """
        Predict churn probability for a customer
        """
        if not self.model:
            await self.initialize_model()
        
        # Prepare features
        features = np.array([[
            customer_data.days_since_last_purchase,
            customer_data.total_purchases,
            customer_data.average_order_value,
            customer_data.total_spent,
            customer_data.email_engagement_rate,
            customer_data.support_tickets,
            customer_data.last_login_days
        ]])
        
        # Scale features
        features_scaled = self.scaler.transform(features)
        
        # Predict
        churn_probability = self.model.predict_proba(features_scaled)[0][1]
        
        # Determine risk level
        if churn_probability >= 0.8:
            risk_level = "critical"
        elif churn_probability >= 0.6:
            risk_level = "high"
        elif churn_probability >= 0.4:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        # Get feature importance for key factors
        feature_importance = self.model.feature_importances_
        key_factors = [
            self.feature_columns[i] for i in np.argsort(feature_importance)[-3:][::-1]
        ]
        
        # Generate recommended actions using AI
        recommended_actions = await self._generate_retention_actions(
            customer_data, churn_probability, key_factors
        )
        
        logger.info(
            "Churn prediction completed",
            customer_id=customer_data.customer_id,
            churn_probability=churn_probability,
            risk_level=risk_level
        )
        
        return ChurnPrediction(
            customer_id=customer_data.customer_id,
            churn_probability=churn_probability,
            risk_level=risk_level,
            key_factors=key_factors,
            recommended_actions=recommended_actions,
            confidence=0.85,  # Model confidence
            model_version="1.0"
        )
    
    async def _generate_retention_actions(
        self, 
        customer_data: CustomerData, 
        churn_probability: float,
        key_factors: List[str]
    ) -> List[Dict[str, Any]]:
        """
        Generate AI-powered retention action recommendations
        """
        prompt = f"""
        Analyze this customer's churn risk and recommend specific retention actions:
        
        Customer Profile:
        - ID: {customer_data.customer_id}
        - Churn Probability: {churn_probability:.2%}
        - Days Since Last Purchase: {customer_data.days_since_last_purchase}
        - Total Purchases: {customer_data.total_purchases}
        - Average Order Value: ${customer_data.average_order_value:.2f}
        - Total Spent: ${customer_data.total_spent:.2f}
        - Email Engagement Rate: {customer_data.email_engagement_rate:.1f}%
        - Support Tickets: {customer_data.support_tickets}
        - Subscription Tier: {customer_data.subscription_tier}
        
        Key Risk Factors: {', '.join(key_factors)}
        
        Recommend 3-5 specific retention actions with:
        1. Action type (email_campaign, discount_offer, personal_outreach, product_recommendation)
        2. Specific parameters (discount %, email template, product suggestions)
        3. Priority (1-5)
        4. Estimated impact (0-1)
        5. Estimated cost ($)
        
        Respond in JSON format:
        {{
            "actions": [
                {{
                    "action_type": "discount_offer",
                    "parameters": {{"discount_percent": 15, "valid_days": 7}},
                    "priority": 4,
                    "estimated_impact": 0.3,
                    "cost": 25.0,
                    "reasoning": "High-value customer needs immediate incentive"
                }}
            ]
        }}
        """
        
        try:
            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert customer retention specialist. Provide specific, actionable retention strategies."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=800
            )
            
            import json
            result = json.loads(response.choices[0].message.content)
            return result.get("actions", [])
            
        except Exception as e:
            logger.error("Failed to generate retention actions", error=str(e))
            # Fallback to rule-based actions
            return self._fallback_retention_actions(customer_data, churn_probability)
    
    def _fallback_retention_actions(
        self, 
        customer_data: CustomerData, 
        churn_probability: float
    ) -> List[Dict[str, Any]]:
        """
        Fallback rule-based retention actions
        """
        actions = []
        
        if churn_probability >= 0.8:
            # Critical risk - aggressive intervention
            actions.extend([
                {
                    "action_type": "personal_outreach",
                    "parameters": {"channel": "phone", "urgency": "high"},
                    "priority": 5,
                    "estimated_impact": 0.4,
                    "cost": 50.0,
                    "reasoning": "Critical churn risk requires personal touch"
                },
                {
                    "action_type": "discount_offer",
                    "parameters": {"discount_percent": 25, "valid_days": 3},
                    "priority": 5,
                    "estimated_impact": 0.35,
                    "cost": 40.0,
                    "reasoning": "Urgent discount to prevent immediate churn"
                }
            ])
        
        elif churn_probability >= 0.6:
            # High risk - targeted intervention
            actions.extend([
                {
                    "action_type": "email_campaign",
                    "parameters": {"template": "win_back", "personalized": True},
                    "priority": 4,
                    "estimated_impact": 0.25,
                    "cost": 5.0,
                    "reasoning": "Personalized win-back campaign"
                },
                {
                    "action_type": "product_recommendation",
                    "parameters": {"based_on": "purchase_history", "discount": 15},
                    "priority": 3,
                    "estimated_impact": 0.2,
                    "cost": 15.0,
                    "reasoning": "Relevant product suggestions with incentive"
                }
            ])
        
        elif churn_probability >= 0.4:
            # Medium risk - preventive actions
            actions.append({
                "action_type": "email_campaign",
                "parameters": {"template": "engagement", "frequency": "weekly"},
                "priority": 2,
                "estimated_impact": 0.15,
                "cost": 2.0,
                "reasoning": "Increase engagement to prevent churn"
            })
        
        return actions
    
    async def execute_retention_action(
        self, 
        action: RetentionAction,
        customer_data: CustomerData
    ) -> Dict[str, Any]:
        """
        Execute a specific retention action
        """
        try:
            if action.action_type == "email_campaign":
                return await self._execute_email_campaign(action, customer_data)
            elif action.action_type == "discount_offer":
                return await self._execute_discount_offer(action, customer_data)
            elif action.action_type == "personal_outreach":
                return await self._execute_personal_outreach(action, customer_data)
            elif action.action_type == "product_recommendation":
                return await self._execute_product_recommendation(action, customer_data)
            else:
                raise ValueError(f"Unknown action type: {action.action_type}")
                
        except Exception as e:
            logger.error(
                "Failed to execute retention action",
                error=str(e),
                action_type=action.action_type,
                customer_id=customer_data.customer_id
            )
            return {"status": "failed", "error": str(e)}
    
    async def _execute_email_campaign(
        self, 
        action: RetentionAction, 
        customer_data: CustomerData
    ) -> Dict[str, Any]:
        """Execute email campaign action"""
        # TODO: Integrate with email service (SendGrid, Mailchimp, etc.)
        logger.info(
            "Email campaign executed",
            customer_id=customer_data.customer_id,
            template=action.parameters.get("template")
        )
        return {"status": "success", "channel": "email", "sent_at": datetime.utcnow()}
    
    async def _execute_discount_offer(
        self, 
        action: RetentionAction, 
        customer_data: CustomerData
    ) -> Dict[str, Any]:
        """Execute discount offer action"""
        # TODO: Integrate with e-commerce platform
        discount_code = f"SAVE{action.parameters.get('discount_percent', 10)}"
        logger.info(
            "Discount offer created",
            customer_id=customer_data.customer_id,
            discount_code=discount_code
        )
        return {
            "status": "success", 
            "discount_code": discount_code,
            "expires_at": datetime.utcnow() + timedelta(days=action.parameters.get("valid_days", 7))
        }
    
    async def _execute_personal_outreach(
        self, 
        action: RetentionAction, 
        customer_data: CustomerData
    ) -> Dict[str, Any]:
        """Execute personal outreach action"""
        # TODO: Create task in CRM system
        logger.info(
            "Personal outreach task created",
            customer_id=customer_data.customer_id,
            channel=action.parameters.get("channel")
        )
        return {"status": "success", "task_created": True, "assigned_to": "retention_team"}
    
    async def _execute_product_recommendation(
        self, 
        action: RetentionAction, 
        customer_data: CustomerData
    ) -> Dict[str, Any]:
        """Execute product recommendation action"""
        # TODO: Integrate with recommendation engine
        logger.info(
            "Product recommendations generated",
            customer_id=customer_data.customer_id
        )
        return {"status": "success", "recommendations_sent": True}
