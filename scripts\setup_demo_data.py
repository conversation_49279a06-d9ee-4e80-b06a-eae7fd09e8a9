#!/usr/bin/env python3
"""
Demo Data Setup for Omnify Marketing Cloud
Creates realistic test data for demonstrations and testing
"""
import asyncio
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any
import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from apps.core.database import (
    get_db, Client, User, Campaign, CampaignMetric, 
    CustomerProfile, RetentionAction, AIDecision
)
from apps.auth.user_management import user_manager, UserRegistration
from lib.ai.decision_engine import ROIEngineX, AIDecisionResult
from lib.ai.retention_reactor import RetentionReactorPro, CustomerData, ChurnPrediction
from lib.ai.engage_sense import EngageSenseUltra, CustomerBehavior, EngagementScore

logger = structlog.get_logger()

class DemoDataGenerator:
    """Generates realistic demo data for Omnify Marketing Cloud"""
    
    def __init__(self):
        self.demo_companies = [
            {
                "name": "TechStart Solutions",
                "email": "<EMAIL>",
                "industry": "SaaS",
                "monthly_budget": 25000
            },
            {
                "name": "EcoFriendly Goods",
                "email": "<EMAIL>", 
                "industry": "E-commerce",
                "monthly_budget": 15000
            },
            {
                "name": "HealthPlus Clinic",
                "email": "<EMAIL>",
                "industry": "Healthcare",
                "monthly_budget": 35000
            }
        ]
        
        self.campaign_templates = [
            {
                "name": "Brand Awareness Campaign",
                "platform": "google_ads",
                "budget_ratio": 0.4,
                "target_cac": 45.0
            },
            {
                "name": "Conversion Campaign",
                "platform": "google_ads", 
                "budget_ratio": 0.35,
                "target_cac": 38.0
            },
            {
                "name": "Social Media Ads",
                "platform": "meta_ads",
                "budget_ratio": 0.25,
                "target_cac": 42.0
            }
        ]
    
    async def create_demo_clients(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """Create demo client accounts with users"""
        demo_clients = []
        
        for i, company in enumerate(self.demo_companies):
            try:
                # Create user registration
                registration = UserRegistration(
                    email=company["email"],
                    password="Demo123!",
                    first_name="Demo",
                    last_name="User",
                    company_name=company["name"]
                )
                
                # Register user (creates client automatically)
                user_response = await user_manager.register_user(registration, db)
                
                demo_clients.append({
                    "client_id": user_response.client_id,
                    "user_id": user_response.id,
                    "company": company,
                    "credentials": {
                        "email": company["email"],
                        "password": "Demo123!"
                    }
                })
                
                logger.info(
                    "Demo client created",
                    company=company["name"],
                    client_id=user_response.client_id
                )
                
            except Exception as e:
                logger.error(
                    "Failed to create demo client",
                    company=company["name"],
                    error=str(e)
                )
        
        return demo_clients
    
    async def create_demo_campaigns(
        self, 
        client_id: str, 
        company: Dict[str, Any],
        db: AsyncSession
    ) -> List[Campaign]:
        """Create demo campaigns for a client"""
        campaigns = []
        
        for template in self.campaign_templates:
            campaign_budget = company["monthly_budget"] * template["budget_ratio"]
            
            campaign = Campaign(
                client_id=client_id,
                name=f"{template['name']} - {company['name']}",
                platform=template["platform"],
                campaign_id=f"camp_{client_id}_{template['platform']}_{len(campaigns)+1}",
                budget=campaign_budget,
                target_cac=template["target_cac"],
                status="active"
            )
            
            db.add(campaign)
            campaigns.append(campaign)
        
        await db.flush()  # Get campaign IDs
        
        logger.info(
            "Demo campaigns created",
            client_id=client_id,
            campaign_count=len(campaigns)
        )
        
        return campaigns
    
    async def create_demo_metrics(
        self,
        campaigns: List[Campaign],
        db: AsyncSession,
        days_back: int = 90
    ):
        """Create realistic campaign metrics for the past N days"""
        
        for campaign in campaigns:
            base_daily_spend = campaign.budget / 30  # Monthly budget / 30 days
            
            for day_offset in range(days_back):
                date = datetime.utcnow().date() - timedelta(days=day_offset)
                
                # Add some randomness to make it realistic
                daily_spend = base_daily_spend * random.uniform(0.7, 1.3)
                
                # Calculate realistic metrics
                impressions = int(daily_spend * random.uniform(800, 1200))
                clicks = int(impressions * random.uniform(0.02, 0.08))  # 2-8% CTR
                conversions = clicks * random.uniform(0.05, 0.15)  # 5-15% conversion rate
                
                # Revenue based on conversion value
                avg_order_value = random.uniform(80, 200)
                revenue = conversions * avg_order_value
                
                # Calculate derived metrics
                ctr = (clicks / impressions * 100) if impressions > 0 else 0
                cpc = daily_spend / clicks if clicks > 0 else 0
                roas = revenue / daily_spend if daily_spend > 0 else 0
                
                metric = CampaignMetric(
                    campaign_id=campaign.id,
                    date=date,
                    impressions=impressions,
                    clicks=clicks,
                    conversions=conversions,
                    spend=daily_spend,
                    revenue=revenue,
                    ctr=ctr,
                    cpc=cpc,
                    roas=roas
                )
                
                db.add(metric)
        
        logger.info("Demo metrics created", days_back=days_back)
    
    async def create_demo_customers(
        self,
        client_id: str,
        db: AsyncSession,
        customer_count: int = 500
    ) -> List[CustomerProfile]:
        """Create demo customer profiles"""
        customers = []
        
        # Customer segments with different characteristics
        segments = [
            {
                "name": "champions",
                "ratio": 0.15,
                "purchase_range": (8, 20),
                "spend_range": (2000, 8000),
                "engagement_range": (80, 95)
            },
            {
                "name": "loyal_customers", 
                "ratio": 0.25,
                "purchase_range": (4, 12),
                "spend_range": (800, 3000),
                "engagement_range": (60, 85)
            },
            {
                "name": "potential_loyalists",
                "ratio": 0.20,
                "purchase_range": (2, 6),
                "spend_range": (300, 1200),
                "engagement_range": (40, 70)
            },
            {
                "name": "at_risk",
                "ratio": 0.15,
                "purchase_range": (1, 4),
                "spend_range": (100, 600),
                "engagement_range": (20, 50)
            },
            {
                "name": "hibernating",
                "ratio": 0.25,
                "purchase_range": (1, 3),
                "spend_range": (50, 300),
                "engagement_range": (10, 35)
            }
        ]
        
        customer_id_counter = 1
        
        for segment in segments:
            segment_count = int(customer_count * segment["ratio"])
            
            for i in range(segment_count):
                # Generate realistic customer data
                total_purchases = random.randint(*segment["purchase_range"])
                total_spent = random.uniform(*segment["spend_range"])
                avg_order_value = total_spent / total_purchases if total_purchases > 0 else 0
                
                # Last purchase date based on segment
                if segment["name"] == "hibernating":
                    last_purchase_days = random.randint(90, 365)
                elif segment["name"] == "at_risk":
                    last_purchase_days = random.randint(30, 90)
                else:
                    last_purchase_days = random.randint(1, 30)
                
                last_purchase_date = datetime.utcnow() - timedelta(days=last_purchase_days)
                
                customer = CustomerProfile(
                    client_id=client_id,
                    customer_id=f"cust_{client_id}_{customer_id_counter:04d}",
                    email=f"customer{customer_id_counter}@demo.com",
                    first_name=f"Customer",
                    last_name=f"{customer_id_counter}",
                    total_purchases=total_purchases,
                    total_spent=total_spent,
                    average_order_value=avg_order_value,
                    last_purchase_date=last_purchase_date,
                    email_engagement_rate=random.uniform(*segment["engagement_range"]),
                    segment=segment["name"],
                    risk_level="high" if segment["name"] in ["at_risk", "hibernating"] else "low"
                )
                
                db.add(customer)
                customers.append(customer)
                customer_id_counter += 1
        
        logger.info(
            "Demo customers created",
            client_id=client_id,
            customer_count=len(customers)
        )
        
        return customers
    
    async def create_demo_ai_decisions(
        self,
        campaigns: List[Campaign],
        db: AsyncSession,
        decision_count: int = 50
    ):
        """Create demo AI decisions"""
        
        decision_types = [
            "increase_bid",
            "decrease_bid", 
            "pause_campaign",
            "increase_budget",
            "optimize_targeting"
        ]
        
        for _ in range(decision_count):
            campaign = random.choice(campaigns)
            
            decision = AIDecision(
                client_id=campaign.client_id,
                campaign_id=campaign.id,
                agent_name="roi_engine",
                decision_type=random.choice(decision_types),
                confidence=random.uniform(0.75, 0.95),
                reasoning=f"AI analysis suggests {random.choice(decision_types)} based on performance metrics",
                parameters={
                    "old_value": random.uniform(1.0, 5.0),
                    "new_value": random.uniform(1.0, 5.0),
                    "expected_improvement": random.uniform(5.0, 25.0)
                },
                status="executed",
                created_at=datetime.utcnow() - timedelta(
                    hours=random.randint(1, 72)
                )
            )
            
            db.add(decision)
        
        logger.info("Demo AI decisions created", decision_count=decision_count)
    
    async def create_demo_retention_actions(
        self,
        customers: List[CustomerProfile],
        db: AsyncSession,
        action_count: int = 100
    ):
        """Create demo retention actions"""
        
        action_types = [
            "discount_email",
            "personalized_offer",
            "win_back_campaign",
            "loyalty_program_invite",
            "product_recommendation"
        ]
        
        # Focus on at-risk customers
        at_risk_customers = [c for c in customers if c.risk_level == "high"]
        
        for _ in range(action_count):
            customer = random.choice(at_risk_customers if at_risk_customers else customers)
            
            action = RetentionAction(
                client_id=customer.client_id,
                customer_id=customer.customer_id,
                action_type=random.choice(action_types),
                trigger_reason="churn_risk_detected",
                parameters={
                    "discount_percentage": random.randint(10, 30),
                    "offer_value": random.uniform(20, 100),
                    "channel": random.choice(["email", "sms", "push"])
                },
                status=random.choice(["sent", "delivered", "opened", "clicked"]),
                created_at=datetime.utcnow() - timedelta(
                    hours=random.randint(1, 168)  # Last week
                )
            )
            
            db.add(action)
        
        logger.info("Demo retention actions created", action_count=action_count)
    
    async def generate_all_demo_data(self) -> Dict[str, Any]:
        """Generate complete demo dataset"""
        async with get_db() as db:
            logger.info("Starting demo data generation...")
            
            # Create demo clients
            demo_clients = await self.create_demo_clients(db)
            
            all_campaigns = []
            all_customers = []
            
            for client_data in demo_clients:
                client_id = client_data["client_id"]
                company = client_data["company"]
                
                # Create campaigns
                campaigns = await self.create_demo_campaigns(client_id, company, db)
                all_campaigns.extend(campaigns)
                
                # Create customers
                customers = await self.create_demo_customers(client_id, db)
                all_customers.extend(customers)
            
            await db.commit()
            
            # Create metrics (after campaigns are committed)
            await self.create_demo_metrics(all_campaigns, db)
            
            # Create AI decisions
            await self.create_demo_ai_decisions(all_campaigns, db)
            
            # Create retention actions
            await self.create_demo_retention_actions(all_customers, db)
            
            await db.commit()
            
            logger.info("Demo data generation completed successfully!")
            
            return {
                "clients": demo_clients,
                "campaigns": len(all_campaigns),
                "customers": len(all_customers),
                "summary": {
                    "total_clients": len(demo_clients),
                    "total_campaigns": len(all_campaigns),
                    "total_customers": len(all_customers),
                    "data_period_days": 90
                }
            }

async def main():
    """Main function to generate demo data"""
    generator = DemoDataGenerator()
    
    try:
        result = await generator.generate_all_demo_data()
        
        print("\n🎉 Demo Data Generation Complete!")
        print(f"📊 Summary:")
        print(f"  - Clients: {result['summary']['total_clients']}")
        print(f"  - Campaigns: {result['summary']['total_campaigns']}")
        print(f"  - Customers: {result['summary']['total_customers']}")
        print(f"  - Data Period: {result['summary']['data_period_days']} days")
        print("\n🔑 Demo Login Credentials:")
        
        for client in result["clients"]:
            print(f"  Company: {client['company']['name']}")
            print(f"  Email: {client['credentials']['email']}")
            print(f"  Password: {client['credentials']['password']}")
            print(f"  Client ID: {client['client_id']}")
            print()
        
        print("✅ You can now login to test the platform with realistic data!")
        
    except Exception as e:
        logger.error("Demo data generation failed", error=str(e))
        print(f"❌ Demo data generation failed: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
