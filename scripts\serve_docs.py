#!/usr/bin/env python3
"""
Documentation Server for Omnify Marketing Cloud
Serves documentation with markdown rendering and search
"""
import os
import re
from pathlib import Path
from typing import Dict, List
import markdown
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="Omnify Marketing Cloud Documentation",
    description="Complete documentation for the AI-native marketing automation platform",
    version="1.0.0"
)

# Setup paths
DOCS_DIR = Path(__file__).parent.parent / "docs"
STATIC_DIR = DOCS_DIR / "static"
TEMPLATES_DIR = DOCS_DIR / "templates"

# Create directories if they don't exist
STATIC_DIR.mkdir(exist_ok=True)
TEMPLATES_DIR.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")

# Setup Jinja2 templates
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))

# Markdown extensions
md = markdown.Markdown(
    extensions=[
        'codehilite',
        'fenced_code',
        'tables',
        'toc',
        'attr_list',
        'def_list'
    ],
    extension_configs={
        'codehilite': {
            'css_class': 'highlight',
            'use_pygments': True
        },
        'toc': {
            'permalink': True
        }
    }
)

class DocumentationManager:
    """Manages documentation files and rendering"""
    
    def __init__(self, docs_dir: Path):
        self.docs_dir = docs_dir
        self.docs_cache: Dict[str, Dict] = {}
        self.load_all_docs()
    
    def load_all_docs(self):
        """Load all markdown files into cache"""
        for md_file in self.docs_dir.glob("*.md"):
            self.load_doc(md_file.stem)
    
    def load_doc(self, doc_name: str) -> Dict:
        """Load and parse a markdown document"""
        file_path = self.docs_dir / f"{doc_name}.md"
        
        if not file_path.exists():
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract title from first heading
        title_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
        title = title_match.group(1) if title_match else doc_name.replace('-', ' ').title()
        
        # Convert markdown to HTML
        html_content = md.convert(content)
        
        # Extract table of contents
        toc = getattr(md, 'toc', '')
        
        # Reset markdown instance for next conversion
        md.reset()
        
        doc_data = {
            'name': doc_name,
            'title': title,
            'content': html_content,
            'toc': toc,
            'raw_content': content,
            'file_path': str(file_path)
        }
        
        self.docs_cache[doc_name] = doc_data
        return doc_data
    
    def get_doc(self, doc_name: str) -> Dict:
        """Get a document from cache or load it"""
        if doc_name not in self.docs_cache:
            return self.load_doc(doc_name)
        return self.docs_cache[doc_name]
    
    def list_docs(self) -> List[Dict]:
        """List all available documents"""
        return [
            {
                'name': doc['name'],
                'title': doc['title'],
                'url': f"/docs/{doc['name']}"
            }
            for doc in self.docs_cache.values()
        ]
    
    def search_docs(self, query: str) -> List[Dict]:
        """Search documents by content"""
        results = []
        query_lower = query.lower()
        
        for doc in self.docs_cache.values():
            # Search in title and content
            if (query_lower in doc['title'].lower() or 
                query_lower in doc['raw_content'].lower()):
                
                # Find context around the match
                content_lower = doc['raw_content'].lower()
                match_index = content_lower.find(query_lower)
                
                if match_index != -1:
                    start = max(0, match_index - 100)
                    end = min(len(doc['raw_content']), match_index + 100)
                    context = doc['raw_content'][start:end]
                    
                    results.append({
                        'name': doc['name'],
                        'title': doc['title'],
                        'url': f"/docs/{doc['name']}",
                        'context': context,
                        'relevance': query_lower.count(query_lower)
                    })
        
        # Sort by relevance
        results.sort(key=lambda x: x['relevance'], reverse=True)
        return results

# Initialize documentation manager
doc_manager = DocumentationManager(DOCS_DIR)

@app.get("/", response_class=HTMLResponse)
async def home():
    """Serve the main documentation index"""
    index_file = DOCS_DIR / "index.html"
    if index_file.exists():
        return FileResponse(str(index_file))
    else:
        # Fallback to generated index
        docs_list = doc_manager.list_docs()
        return HTMLResponse(generate_index_html(docs_list))

@app.get("/docs/{doc_name}", response_class=HTMLResponse)
async def get_documentation(doc_name: str):
    """Serve a specific documentation page"""
    doc = doc_manager.get_doc(doc_name)
    
    if not doc:
        raise HTTPException(status_code=404, detail="Documentation not found")
    
    # Generate HTML page
    html = generate_doc_html(doc, doc_manager.list_docs())
    return HTMLResponse(html)

@app.get("/api/docs")
async def list_documentation():
    """API endpoint to list all documentation"""
    return {"docs": doc_manager.list_docs()}

@app.get("/api/search")
async def search_documentation(q: str):
    """API endpoint to search documentation"""
    if not q or len(q) < 2:
        return {"results": []}
    
    results = doc_manager.search_docs(q)
    return {"query": q, "results": results}

@app.get("/raw/{doc_name}")
async def get_raw_markdown(doc_name: str):
    """Get raw markdown content"""
    doc = doc_manager.get_doc(doc_name)
    
    if not doc:
        raise HTTPException(status_code=404, detail="Documentation not found")
    
    return {"content": doc['raw_content']}

def generate_index_html(docs_list: List[Dict]) -> str:
    """Generate index HTML if index.html doesn't exist"""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Omnify Marketing Cloud Documentation</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            h1 {{ color: #333; }}
            .doc-list {{ list-style: none; padding: 0; }}
            .doc-list li {{ margin: 10px 0; }}
            .doc-list a {{ text-decoration: none; color: #007bff; }}
            .doc-list a:hover {{ text-decoration: underline; }}
        </style>
    </head>
    <body>
        <h1>🚀 Omnify Marketing Cloud Documentation</h1>
        <p>Welcome to the complete documentation for Omnify Marketing Cloud.</p>
        
        <h2>📚 Available Documentation</h2>
        <ul class="doc-list">
            {''.join([f'<li><a href="/docs/{doc["name"]}">{doc["title"]}</a></li>' for doc in docs_list])}
        </ul>
        
        <h2>🔍 Search</h2>
        <input type="text" id="search" placeholder="Search documentation..." style="width: 300px; padding: 8px;">
        <div id="search-results"></div>
        
        <script>
            document.getElementById('search').addEventListener('input', async (e) => {{
                const query = e.target.value;
                if (query.length < 2) {{
                    document.getElementById('search-results').innerHTML = '';
                    return;
                }}
                
                const response = await fetch(`/api/search?q=${{encodeURIComponent(query)}}`);
                const data = await response.json();
                
                const resultsHtml = data.results.map(result => 
                    `<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">
                        <a href="${{result.url}}">${{result.title}}</a>
                        <p style="color: #666; font-size: 0.9em;">${{result.context}}</p>
                    </div>`
                ).join('');
                
                document.getElementById('search-results').innerHTML = resultsHtml;
            }});
        </script>
    </body>
    </html>
    """

def generate_doc_html(doc: Dict, docs_list: List[Dict]) -> str:
    """Generate HTML for a documentation page"""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>{doc['title']} - Omnify Marketing Cloud</title>
        <style>
            body {{ 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }}
            .sidebar {{
                position: fixed;
                left: 0;
                top: 0;
                width: 250px;
                height: 100vh;
                background: #f8f9fa;
                padding: 20px;
                overflow-y: auto;
                border-right: 1px solid #dee2e6;
            }}
            .content {{
                margin-left: 270px;
                padding: 20px;
            }}
            .nav-list {{ list-style: none; padding: 0; }}
            .nav-list li {{ margin: 5px 0; }}
            .nav-list a {{ 
                text-decoration: none; 
                color: #007bff; 
                font-size: 0.9em;
            }}
            .nav-list a:hover {{ text-decoration: underline; }}
            .nav-list a.current {{ font-weight: bold; color: #333; }}
            h1, h2, h3, h4, h5, h6 {{ color: #333; }}
            code {{ 
                background: #f8f9fa; 
                padding: 2px 4px; 
                border-radius: 3px; 
                font-family: 'Monaco', 'Consolas', monospace;
            }}
            pre {{ 
                background: #f8f9fa; 
                padding: 15px; 
                border-radius: 5px; 
                overflow-x: auto;
            }}
            table {{ 
                border-collapse: collapse; 
                width: 100%; 
                margin: 20px 0;
            }}
            th, td {{ 
                border: 1px solid #dee2e6; 
                padding: 8px 12px; 
                text-align: left;
            }}
            th {{ background: #f8f9fa; }}
            .toc {{ 
                background: #f8f9fa; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 20px 0;
            }}
            .back-link {{ 
                display: inline-block; 
                margin-bottom: 20px; 
                color: #007bff; 
                text-decoration: none;
            }}
            .back-link:hover {{ text-decoration: underline; }}
        </style>
    </head>
    <body>
        <div class="sidebar">
            <h3><a href="/" style="text-decoration: none; color: #333;">📚 Documentation</a></h3>
            <ul class="nav-list">
                {''.join([f'<li><a href="/docs/{d["name"]}" class="{"current" if d["name"] == doc["name"] else ""}">{d["title"]}</a></li>' for d in docs_list])}
            </ul>
        </div>
        
        <div class="content">
            <a href="/" class="back-link">← Back to Index</a>
            
            {f'<div class="toc"><h3>Table of Contents</h3>{doc["toc"]}</div>' if doc["toc"] else ''}
            
            <div class="markdown-content">
                {doc['content']}
            </div>
            
            <hr style="margin: 40px 0;">
            <p style="color: #666; font-size: 0.9em;">
                📞 Support: <a href="mailto:<EMAIL>"><EMAIL></a> | 
                📈 Business: <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
        </div>
    </body>
    </html>
    """

if __name__ == "__main__":
    print("🚀 Starting Omnify Marketing Cloud Documentation Server...")
    print(f"📚 Serving documentation from: {DOCS_DIR}")
    print(f"🌐 Available at: http://localhost:8080")
    print(f"📖 Found {len(doc_manager.docs_cache)} documentation files")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        reload=True,
        reload_dirs=[str(DOCS_DIR)]
    )
