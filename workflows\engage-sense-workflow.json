{"name": "EngageSense Ultra™ - Personalization Workflow", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "value": 4}]}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://localhost:8000/api/v1/agents/optimize", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"agent_type\": \"engage_sense\",\n  \"force_execution\": false\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Trigger Engagement Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [460, 300], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"amount": 45, "unit": "seconds"}, "name": "Wait for Processing", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "http://localhost:8000/api/v1/agents/decisions", "options": {"queryParameters": {"parameters": [{"name": "client_id", "value": "={{$json[\"client_id\"]}}"}, {"name": "agent_type", "value": "engage_sense"}, {"name": "days", "value": "1"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Get Engagement Scores", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 300], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"decision\"][\"segment\"]}}", "operation": "equal", "value2": "champion"}]}}, "name": "Check Champion Segment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"decision\"][\"segment\"]}}", "operation": "equal", "value2": "loyal"}]}}, "name": "Check Loyal Segment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 400]}, {"parameters": {"url": "https://api.mailchimp.com/3.0/campaigns", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"type\": \"regular\",\n  \"recipients\": {\n    \"list_id\": \"VIP_LIST_ID\",\n    \"segment_opts\": {\n      \"match\": \"all\",\n      \"conditions\": [{\n        \"condition_type\": \"TextMerge\",\n        \"field\": \"CUSTOMER_ID\",\n        \"op\": \"is\",\n        \"value\": \"{{$json[\"decision\"][\"customer_id\"]}}\"\n      }]\n    }\n  },\n  \"settings\": {\n    \"subject_line\": \"Exclusive VIP Offer Just for You!\",\n    \"from_name\": \"Omnify Team\",\n    \"reply_to\": \"<EMAIL>\"\n  }\n}", "headers": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_MAILCHIMP_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}}}, "name": "Send VIP Campaign", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 100]}, {"parameters": {"url": "https://api.mailchimp.com/3.0/campaigns", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"type\": \"regular\",\n  \"recipients\": {\n    \"list_id\": \"LOYAL_LIST_ID\",\n    \"segment_opts\": {\n      \"match\": \"all\",\n      \"conditions\": [{\n        \"condition_type\": \"TextMerge\",\n        \"field\": \"CUSTOMER_ID\",\n        \"op\": \"is\",\n        \"value\": \"{{$json[\"decision\"][\"customer_id\"]}}\"\n      }]\n    }\n  },\n  \"settings\": {\n    \"subject_line\": \"Thank You for Your Loyalty - Special Rewards Inside!\",\n    \"from_name\": \"Omnify Team\",\n    \"reply_to\": \"<EMAIL>\"\n  }\n}", "headers": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_MAILCHIMP_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}}}, "name": "Send Loyalty Campaign", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"decision\"][\"segment\"]}}", "operation": "equal", "value2": "at_risk"}]}}, "name": "Check At-Risk Segment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 600]}, {"parameters": {"url": "http://localhost:8000/api/v1/webhooks/external/performance-alert", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"event_type\": \"at_risk_customer\",\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"data\": {\n    \"customer_id\": \"{{$json[\"decision\"][\"customer_id\"]}}\",\n    \"engagement_score\": {{$json[\"decision\"][\"overall_score\"]}},\n    \"segment\": \"{{$json[\"decision\"][\"segment\"]}}\",\n    \"next_best_action\": {{$json[\"decision\"][\"next_best_action\"]}}\n  }\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Send At-Risk Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 500], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}, {"parameters": {"url": "http://localhost:8000/api/v1/webhooks/external/performance-alert", "options": {"bodyContentType": "json", "jsonBody": "={\n  \"event_type\": \"personalization_completed\",\n  \"client_id\": \"{{$json[\"client_id\"]}}\",\n  \"data\": {\n    \"customers_processed\": {{$json[\"customers_processed\"]}},\n    \"segments_updated\": true,\n    \"campaigns_triggered\": {{$json[\"campaigns_triggered\"]}},\n    \"timestamp\": \"{{$now}}\"\n  }\n}", "headers": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Log Personalization Results", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1560, 300], "credentials": {"httpHeaderAuth": {"id": "1", "name": "Omnify API Auth"}}}], "connections": {"Schedule Trigger": {"main": [[{"node": "Trigger Engagement Analysis", "type": "main", "index": 0}]]}, "Trigger Engagement Analysis": {"main": [[{"node": "Wait for Processing", "type": "main", "index": 0}]]}, "Wait for Processing": {"main": [[{"node": "Get Engagement Scores", "type": "main", "index": 0}]]}, "Get Engagement Scores": {"main": [[{"node": "Check Champion Segment", "type": "main", "index": 0}, {"node": "Check Loyal Segment", "type": "main", "index": 0}, {"node": "Check At-Risk Segment", "type": "main", "index": 0}]]}, "Check Champion Segment": {"main": [[{"node": "Send VIP Campaign", "type": "main", "index": 0}]]}, "Check Loyal Segment": {"main": [[{"node": "Send Loyalty Campaign", "type": "main", "index": 0}]]}, "Check At-Risk Segment": {"main": [[{"node": "Send At-Risk Alert", "type": "main", "index": 0}]]}, "Send VIP Campaign": {"main": [[{"node": "Log Personalization Results", "type": "main", "index": 0}]]}, "Send Loyalty Campaign": {"main": [[{"node": "Log Personalization Results", "type": "main", "index": 0}]]}, "Send At-Risk Alert": {"main": [[{"node": "Log Personalization Results", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "id": "3"}