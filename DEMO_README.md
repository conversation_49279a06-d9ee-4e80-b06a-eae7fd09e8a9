# 🚀 Omnify Marketing Cloud - Demo Guide

> **One-command demo setup for the world's first multi-cloud AI-native marketing automation platform**

## 🎯 Quick Demo Launch

### **Option 1: One-Command Demo (Recommended)**
```bash
python demo_launcher.py
```

This will automatically:
- ✅ Check requirements (<PERSON>, Docker, Docker Compose)
- ✅ Setup environment configuration
- ✅ Start PostgreSQL and Redis
- ✅ Install Python dependencies
- ✅ Initialize database with demo data
- ✅ Launch the API server
- ✅ Display access points and demo commands

### **Option 2: Manual Setup**
```bash
# 1. Start infrastructure
docker-compose -f docker-compose.demo.yml up -d postgres redis

# 2. Install dependencies
pip install -r requirements.txt

# 3. Setup environment
cp .env.example .env

# 4. Initialize database
python -c "
import asyncio
from apps.core.database import init_db
asyncio.run(init_db())
"

# 5. Start API server
uvicorn apps.core.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🌐 Demo Access Points

Once running, access these endpoints:

- **📚 API Documentation**: http://localhost:8000/docs
- **❤️ Health Check**: http://localhost:8000/health
- **🔧 API Root**: http://localhost:8000/

## 🎮 Demo Features

### **✅ Available for Immediate Demo**

#### **1. Multi-Cloud Architecture Simulation**
```bash
# Test cloud variant recommendations
curl http://localhost:8000/api/v1/integrations/cloud-variants

# Simulate deployment to different clouds
python scripts/universal_deploy.py deploy --variant aws --environment demo
python scripts/universal_deploy.py deploy --variant azure --environment demo
python scripts/universal_deploy.py deploy --variant gcp --environment demo
```

#### **2. AI-Powered Setup Wizard**
```bash
# Run the enhanced setup wizard
python scripts/enhanced_setup_wizard.py
```

#### **3. Campaign Management APIs**
```bash
# Get all campaigns
curl http://localhost:8000/api/v1/campaigns/

# Get campaign analytics
curl http://localhost:8000/api/v1/analytics/campaigns/1

# AI optimization recommendations
curl http://localhost:8000/api/v1/ai-agents/roi-engine/optimize/1
```

#### **4. Real-time Health Monitoring**
```bash
# Comprehensive health check
python scripts/health_check.py --variant local --environment demo

# API health endpoint
curl http://localhost:8000/health
```

#### **5. Integration Wizard**
```bash
# Get integration recommendations
curl http://localhost:8000/api/v1/integrations/recommendations

# Test integration setup
curl -X POST http://localhost:8000/api/v1/integrations/setup \
  -H "Content-Type: application/json" \
  -d '{"platform": "google_ads", "credentials": {"developer_token": "demo"}}'
```

## 🧪 Demo Data

The demo includes realistic data:

### **Demo Companies**
- **TechStart Solutions** (Technology, $50K/month)
- **GrowthCorp Marketing** (Marketing, $75K/month)  
- **ScaleUp Ventures** (E-commerce, $100K/month)

### **Demo Campaigns**
- Q4 Product Launch (Google Ads, $25K budget)
- Holiday Sale Campaign (Meta Ads, $15K budget)
- Brand Awareness Drive (Google Ads, $30K budget)
- Retargeting Campaign (Meta Ads, $10K budget)
- Lead Generation (Google Ads, $20K budget)

### **Demo AI Decisions**
- Bid adjustments with 92% confidence
- Budget optimizations with real impact tracking
- Audience expansion recommendations

## 🔧 Demo Commands

### **Test API Endpoints**
```bash
# Health check
curl http://localhost:8000/health

# Get campaigns
curl http://localhost:8000/api/v1/campaigns/

# Get analytics
curl http://localhost:8000/api/v1/analytics/overview

# AI recommendations
curl http://localhost:8000/api/v1/ai-agents/recommendations
```

### **Test Multi-Cloud Features**
```bash
# Cloud variant analysis
python scripts/enhanced_setup_wizard.py

# Deployment simulation
python scripts/universal_deploy.py deploy --variant aws --environment demo

# Health checks
python scripts/health_check.py --variant local --environment demo
```

### **Test Integration Wizard**
```bash
# Integration recommendations
curl http://localhost:8000/api/v1/integrations/recommendations

# Setup wizard
curl http://localhost:8000/api/v1/integrations/wizard/start
```

## 📊 Demo Monitoring

### **Optional: Full Monitoring Stack**
```bash
# Start with monitoring (Grafana + Prometheus)
docker-compose -f docker-compose.demo.yml --profile full-demo up -d

# Access points:
# - Grafana: http://localhost:3000 (admin/demo123)
# - Prometheus: http://localhost:9090
# - n8n: http://localhost:5678 (demo/demo123)
```

## 🎯 Demo Scenarios

### **Scenario 1: New Customer Onboarding**
1. Run setup wizard: `python scripts/enhanced_setup_wizard.py`
2. Choose cloud variant based on business profile
3. Deploy to selected cloud (simulated)
4. Validate deployment with health checks

### **Scenario 2: Campaign Optimization**
1. View current campaigns: `curl http://localhost:8000/api/v1/campaigns/`
2. Get AI recommendations: `curl http://localhost:8000/api/v1/ai-agents/roi-engine/optimize/1`
3. Apply optimizations and track results

### **Scenario 3: Multi-Cloud Migration**
1. Check current deployment: `python scripts/health_check.py --variant local --environment demo`
2. Simulate migration: `python scripts/universal_deploy.py deploy --variant azure --environment demo`
3. Validate new deployment

## 🚨 Troubleshooting

### **Common Issues**

#### **Port Already in Use**
```bash
# Check what's using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>
```

#### **Database Connection Issues**
```bash
# Check PostgreSQL status
docker-compose -f docker-compose.demo.yml ps postgres

# Restart PostgreSQL
docker-compose -f docker-compose.demo.yml restart postgres
```

#### **Dependencies Issues**
```bash
# Reinstall dependencies
pip install --upgrade -r requirements.txt

# Or use virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

## 🎉 Demo Success Criteria

After running the demo, you should be able to:

- ✅ Access API documentation at http://localhost:8000/docs
- ✅ See healthy status at http://localhost:8000/health
- ✅ Run the enhanced setup wizard successfully
- ✅ Get campaign data from the API
- ✅ Receive AI optimization recommendations
- ✅ Simulate multi-cloud deployments
- ✅ Run comprehensive health checks

## 📞 Demo Support

If you encounter issues:

1. **Check logs**: The demo launcher shows detailed logs
2. **Verify requirements**: Ensure Python 3.11+, Docker, and Docker Compose are installed
3. **Check ports**: Ensure ports 8000, 5432, and 6379 are available
4. **Review documentation**: See `docs/README.md` for complete documentation

## 🚀 Next Steps

After the demo:

1. **Production Deployment**: Use `python scripts/universal_deploy.py` for real cloud deployment
2. **Integration Setup**: Configure real API keys for Google Ads, Meta Ads, etc.
3. **Monitoring**: Set up production monitoring with cloud-native tools
4. **Scaling**: Deploy across multiple cloud variants for redundancy

---

**🎯 This demo showcases the complete Omnify Marketing Cloud platform with realistic data and full functionality. Perfect for client presentations, investor demos, and technical evaluations.**
