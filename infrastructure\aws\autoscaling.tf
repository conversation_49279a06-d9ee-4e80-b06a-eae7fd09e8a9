# Auto Scaling Configuration for AWS ECS
# Implements comprehensive auto-scaling policies and performance optimization

# ECS Service Auto Scaling Target
resource "aws_appautoscaling_target" "ecs_target" {
  max_capacity       = var.max_capacity
  min_capacity       = var.min_capacity
  resource_id        = "service/${aws_ecs_cluster.omnify_cluster.name}/${aws_ecs_service.omnify_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"

  tags = {
    Name        = "omnify-autoscaling-target-${var.environment}"
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# CPU-based Auto Scaling Policy
resource "aws_appautoscaling_policy" "ecs_cpu_policy" {
  name               = "omnify-cpu-scaling-policy-${var.environment}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.ecs_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = var.cpu_target_value
    scale_out_cooldown = 300
    scale_in_cooldown  = 300
  }
}

# Memory-based Auto Scaling Policy
resource "aws_appautoscaling_policy" "ecs_memory_policy" {
  name               = "omnify-memory-scaling-policy-${var.environment}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.ecs_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = var.memory_target_value
    scale_out_cooldown = 300
    scale_in_cooldown  = 300
  }
}

# Request Count-based Auto Scaling Policy
resource "aws_appautoscaling_policy" "ecs_request_count_policy" {
  name               = "omnify-request-count-scaling-policy-${var.environment}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.ecs_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ALBRequestCountPerTarget"
      resource_label         = "${aws_lb.omnify_alb.arn_suffix}/${aws_lb_target_group.omnify_api_tg.arn_suffix}"
    }
    target_value       = var.request_count_target_value
    scale_out_cooldown = 180
    scale_in_cooldown  = 300
  }
}

# Custom Metric Auto Scaling Policy (AI Processing Queue)
resource "aws_cloudwatch_metric_alarm" "ai_queue_depth_high" {
  alarm_name          = "omnify-ai-queue-depth-high-${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "QueueDepth"
  namespace           = "Omnify/AI"
  period              = "60"
  statistic           = "Average"
  threshold           = "10"
  alarm_description   = "This metric monitors AI processing queue depth"
  alarm_actions       = [aws_appautoscaling_policy.ecs_scale_up_policy.arn]

  dimensions = {
    Environment = var.environment
    Service     = "omnify-api"
  }

  tags = {
    Name        = "omnify-ai-queue-alarm-${var.environment}"
    Environment = var.environment
  }
}

resource "aws_cloudwatch_metric_alarm" "ai_queue_depth_low" {
  alarm_name          = "omnify-ai-queue-depth-low-${var.environment}"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "QueueDepth"
  namespace           = "Omnify/AI"
  period              = "300"
  statistic           = "Average"
  threshold           = "2"
  alarm_description   = "This metric monitors AI processing queue depth for scale down"
  alarm_actions       = [aws_appautoscaling_policy.ecs_scale_down_policy.arn]

  dimensions = {
    Environment = var.environment
    Service     = "omnify-api"
  }

  tags = {
    Name        = "omnify-ai-queue-low-alarm-${var.environment}"
    Environment = var.environment
  }
}

# Step Scaling Policies for Custom Metrics
resource "aws_appautoscaling_policy" "ecs_scale_up_policy" {
  name               = "omnify-scale-up-policy-${var.environment}"
  policy_type        = "StepScaling"
  resource_id        = aws_appautoscaling_target.ecs_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_target.service_namespace

  step_scaling_policy_configuration {
    adjustment_type         = "ChangeInCapacity"
    cooldown               = 300
    metric_aggregation_type = "Average"

    step_adjustment {
      metric_interval_lower_bound = 0
      metric_interval_upper_bound = 10
      scaling_adjustment          = 1
    }

    step_adjustment {
      metric_interval_lower_bound = 10
      scaling_adjustment          = 2
    }
  }
}

resource "aws_appautoscaling_policy" "ecs_scale_down_policy" {
  name               = "omnify-scale-down-policy-${var.environment}"
  policy_type        = "StepScaling"
  resource_id        = aws_appautoscaling_target.ecs_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_target.service_namespace

  step_scaling_policy_configuration {
    adjustment_type         = "ChangeInCapacity"
    cooldown               = 300
    metric_aggregation_type = "Average"

    step_adjustment {
      metric_interval_upper_bound = 0
      scaling_adjustment          = -1
    }
  }
}

# RDS Auto Scaling for Aurora Serverless v2
resource "aws_appautoscaling_target" "aurora_target" {
  count              = var.enable_aurora_autoscaling ? 1 : 0
  max_capacity       = var.aurora_max_capacity
  min_capacity       = var.aurora_min_capacity
  resource_id        = "cluster:${aws_rds_cluster.omnify_aurora.cluster_identifier}"
  scalable_dimension = "rds:cluster:ReadReplicaCount"
  service_namespace  = "rds"

  tags = {
    Name        = "omnify-aurora-autoscaling-target-${var.environment}"
    Environment = var.environment
  }
}

resource "aws_appautoscaling_policy" "aurora_cpu_policy" {
  count              = var.enable_aurora_autoscaling ? 1 : 0
  name               = "omnify-aurora-cpu-policy-${var.environment}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.aurora_target[0].resource_id
  scalable_dimension = aws_appautoscaling_target.aurora_target[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.aurora_target[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "RDSReaderAverageCPUUtilization"
    }
    target_value       = 70.0
    scale_out_cooldown = 300
    scale_in_cooldown  = 300
  }
}

# ElastiCache Auto Scaling
resource "aws_appautoscaling_target" "elasticache_target" {
  count              = var.enable_elasticache_autoscaling ? 1 : 0
  max_capacity       = var.elasticache_max_capacity
  min_capacity       = var.elasticache_min_capacity
  resource_id        = "replication-group/${aws_elasticache_replication_group.omnify_redis.id}"
  scalable_dimension = "elasticache:replication-group:Replicas"
  service_namespace  = "elasticache"

  tags = {
    Name        = "omnify-elasticache-autoscaling-target-${var.environment}"
    Environment = var.environment
  }
}

resource "aws_appautoscaling_policy" "elasticache_cpu_policy" {
  count              = var.enable_elasticache_autoscaling ? 1 : 0
  name               = "omnify-elasticache-cpu-policy-${var.environment}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.elasticache_target[0].resource_id
  scalable_dimension = aws_appautoscaling_target.elasticache_target[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.elasticache_target[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ElastiCachePrimaryEngineCPUUtilization"
    }
    target_value       = 75.0
    scale_out_cooldown = 300
    scale_in_cooldown  = 300
  }
}

# Scheduled Scaling for Predictable Load Patterns
resource "aws_appautoscaling_scheduled_action" "scale_up_business_hours" {
  name               = "omnify-scale-up-business-hours-${var.environment}"
  service_namespace  = aws_appautoscaling_target.ecs_target.service_namespace
  resource_id        = aws_appautoscaling_target.ecs_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target.scalable_dimension
  schedule           = "cron(0 8 * * MON-FRI)"  # 8 AM Monday-Friday
  timezone           = "America/New_York"

  scalable_target_action {
    min_capacity = var.min_capacity + 1
    max_capacity = var.max_capacity
  }
}

resource "aws_appautoscaling_scheduled_action" "scale_down_after_hours" {
  name               = "omnify-scale-down-after-hours-${var.environment}"
  service_namespace  = aws_appautoscaling_target.ecs_target.service_namespace
  resource_id        = aws_appautoscaling_target.ecs_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target.scalable_dimension
  schedule           = "cron(0 20 * * MON-FRI)"  # 8 PM Monday-Friday
  timezone           = "America/New_York"

  scalable_target_action {
    min_capacity = var.min_capacity
    max_capacity = var.max_capacity
  }
}

# Performance Monitoring Dashboard
resource "aws_cloudwatch_dashboard" "omnify_performance" {
  dashboard_name = "omnify-performance-${var.environment}"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/ECS", "CPUUtilization", "ServiceName", aws_ecs_service.omnify_service.name, "ClusterName", aws_ecs_cluster.omnify_cluster.name],
            ["AWS/ECS", "MemoryUtilization", "ServiceName", aws_ecs_service.omnify_service.name, "ClusterName", aws_ecs_cluster.omnify_cluster.name],
            ["AWS/ApplicationELB", "RequestCount", "LoadBalancer", aws_lb.omnify_alb.arn_suffix],
            ["AWS/ApplicationELB", "TargetResponseTime", "LoadBalancer", aws_lb.omnify_alb.arn_suffix]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "ECS Service Performance"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/RDS", "CPUUtilization", "DBClusterIdentifier", aws_rds_cluster.omnify_aurora.cluster_identifier],
            ["AWS/RDS", "DatabaseConnections", "DBClusterIdentifier", aws_rds_cluster.omnify_aurora.cluster_identifier],
            ["AWS/ElastiCache", "CPUUtilization", "CacheClusterId", aws_elasticache_replication_group.omnify_redis.id],
            ["AWS/ElastiCache", "CacheHitRate", "CacheClusterId", aws_elasticache_replication_group.omnify_redis.id]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "Database and Cache Performance"
          period  = 300
        }
      }
    ]
  })
}

# Variables
variable "min_capacity" {
  description = "Minimum number of ECS tasks"
  type        = number
  default     = 2
}

variable "max_capacity" {
  description = "Maximum number of ECS tasks"
  type        = number
  default     = 20
}

variable "cpu_target_value" {
  description = "Target CPU utilization percentage"
  type        = number
  default     = 70
}

variable "memory_target_value" {
  description = "Target memory utilization percentage"
  type        = number
  default     = 80
}

variable "request_count_target_value" {
  description = "Target request count per target"
  type        = number
  default     = 1000
}

variable "enable_aurora_autoscaling" {
  description = "Enable Aurora auto scaling"
  type        = bool
  default     = true
}

variable "aurora_min_capacity" {
  description = "Minimum Aurora read replicas"
  type        = number
  default     = 1
}

variable "aurora_max_capacity" {
  description = "Maximum Aurora read replicas"
  type        = number
  default     = 5
}

variable "enable_elasticache_autoscaling" {
  description = "Enable ElastiCache auto scaling"
  type        = bool
  default     = true
}

variable "elasticache_min_capacity" {
  description = "Minimum ElastiCache replicas"
  type        = number
  default     = 1
}

variable "elasticache_max_capacity" {
  description = "Maximum ElastiCache replicas"
  type        = number
  default     = 5
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name"
  type        = string
}

# Outputs
output "autoscaling_target_arn" {
  description = "ARN of the auto scaling target"
  value       = aws_appautoscaling_target.ecs_target.arn
}

output "cpu_policy_arn" {
  description = "ARN of the CPU scaling policy"
  value       = aws_appautoscaling_policy.ecs_cpu_policy.arn
}

output "memory_policy_arn" {
  description = "ARN of the memory scaling policy"
  value       = aws_appautoscaling_policy.ecs_memory_policy.arn
}

output "performance_dashboard_url" {
  description = "URL of the performance dashboard"
  value       = "https://console.aws.amazon.com/cloudwatch/home?region=${var.aws_region}#dashboards:name=${aws_cloudwatch_dashboard.omnify_performance.dashboard_name}"
}
