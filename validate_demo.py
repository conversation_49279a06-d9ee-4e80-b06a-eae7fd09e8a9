#!/usr/bin/env python3
"""
Demo Validation Script for Omnify Marketing Cloud
Validates that all demo components are working correctly
"""
import asyncio
import sys
import time
from typing import Dict, Any
import httpx
import structlog

logger = structlog.get_logger()

class DemoValidator:
    """Validates demo functionality"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results = {}
    
    async def validate_all(self) -> Dict[str, Any]:
        """Run all validation tests"""
        print("🔍 VALIDATING OMNIFY DEMO SETUP")
        print("=" * 50)
        
        tests = [
            ("API Health", self.test_api_health),
            ("Database Connection", self.test_database),
            ("Campaign APIs", self.test_campaigns),
            ("Analytics APIs", self.test_analytics),
            ("AI Agents", self.test_ai_agents),
            ("Integration Wizard", self.test_integration_wizard),
            ("WebSocket Connection", self.test_websocket),
            ("Documentation", self.test_documentation)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing {test_name}...")
            try:
                result = await test_func()
                if result.get("status") == "success":
                    print(f"  ✅ {test_name}: PASSED")
                    passed += 1
                else:
                    print(f"  ❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                
                self.results[test_name] = result
                
            except Exception as e:
                print(f"  ❌ {test_name}: ERROR - {str(e)}")
                self.results[test_name] = {"status": "error", "error": str(e)}
        
        # Summary
        print(f"\n📊 VALIDATION SUMMARY")
        print("=" * 30)
        print(f"Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED - DEMO IS READY!")
            return {"status": "success", "passed": passed, "total": total}
        else:
            print("⚠️ SOME TESTS FAILED - CHECK SETUP")
            return {"status": "partial", "passed": passed, "total": total}
    
    async def test_api_health(self) -> Dict[str, Any]:
        """Test basic API health"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "status": "success",
                        "response_time": response.elapsed.total_seconds(),
                        "data": data
                    }
                else:
                    return {
                        "status": "failed",
                        "error": f"HTTP {response.status_code}: {response.text}"
                    }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_database(self) -> Dict[str, Any]:
        """Test database connectivity"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/api/v1/campaigns/")
                
                if response.status_code in [200, 401]:  # 401 is OK (auth required)
                    return {"status": "success", "note": "Database accessible"}
                else:
                    return {
                        "status": "failed",
                        "error": f"Database connection issue: HTTP {response.status_code}"
                    }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_campaigns(self) -> Dict[str, Any]:
        """Test campaign APIs"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Test campaigns endpoint
                response = await client.get(f"{self.base_url}/api/v1/campaigns/")
                
                if response.status_code in [200, 401]:
                    return {"status": "success", "note": "Campaign APIs accessible"}
                else:
                    return {
                        "status": "failed",
                        "error": f"Campaign API issue: HTTP {response.status_code}"
                    }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_analytics(self) -> Dict[str, Any]:
        """Test analytics APIs"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/api/v1/analytics/overview")
                
                if response.status_code in [200, 401]:
                    return {"status": "success", "note": "Analytics APIs accessible"}
                else:
                    return {
                        "status": "failed",
                        "error": f"Analytics API issue: HTTP {response.status_code}"
                    }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_ai_agents(self) -> Dict[str, Any]:
        """Test AI agent APIs"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/api/v1/agents/roi-engine/status")
                
                if response.status_code in [200, 401]:
                    return {"status": "success", "note": "AI Agent APIs accessible"}
                else:
                    return {
                        "status": "failed",
                        "error": f"AI Agent API issue: HTTP {response.status_code}"
                    }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_integration_wizard(self) -> Dict[str, Any]:
        """Test integration wizard APIs"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/api/v1/integrations/recommendations")
                
                if response.status_code in [200, 401]:
                    return {"status": "success", "note": "Integration Wizard accessible"}
                else:
                    return {
                        "status": "failed",
                        "error": f"Integration Wizard issue: HTTP {response.status_code}"
                    }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_websocket(self) -> Dict[str, Any]:
        """Test WebSocket connectivity"""
        try:
            # Simple WebSocket test
            import websockets
            
            uri = f"ws://localhost:8000/ws/demo_client"
            
            async with websockets.connect(uri, timeout=5) as websocket:
                # Send ping
                await websocket.send("ping")
                
                # Wait for pong
                response = await asyncio.wait_for(websocket.recv(), timeout=3)
                
                if response == "pong":
                    return {"status": "success", "note": "WebSocket working"}
                else:
                    return {"status": "failed", "error": f"Unexpected response: {response}"}
                    
        except ImportError:
            return {"status": "skipped", "note": "websockets library not installed"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_documentation(self) -> Dict[str, Any]:
        """Test API documentation"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/docs")
                
                if response.status_code == 200:
                    return {"status": "success", "note": "API documentation accessible"}
                else:
                    return {
                        "status": "failed",
                        "error": f"Documentation issue: HTTP {response.status_code}"
                    }
        except Exception as e:
            return {"status": "error", "error": str(e)}

async def main():
    """Main validation function"""
    print("🚀 OMNIFY MARKETING CLOUD - DEMO VALIDATOR")
    print("=" * 60)
    
    # Check if API server is running
    print("🔍 Checking if API server is running...")
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get("http://localhost:8000/")
            if response.status_code == 200:
                print("✅ API server is running")
            else:
                print(f"⚠️ API server responded with status {response.status_code}")
    except Exception as e:
        print(f"❌ API server is not running: {str(e)}")
        print("\n💡 To start the demo:")
        print("   python demo_launcher.py")
        print("   OR")
        print("   uvicorn apps.core.main:app --host 0.0.0.0 --port 8000 --reload")
        sys.exit(1)
    
    # Run validation
    validator = DemoValidator()
    results = await validator.validate_all()
    
    # Display next steps
    if results["status"] == "success":
        print(f"\n🎯 DEMO READY FOR PRESENTATION!")
        print("=" * 40)
        print("📍 Access Points:")
        print("  • API Docs: http://localhost:8000/docs")
        print("  • Health: http://localhost:8000/health")
        print("  • Root: http://localhost:8000/")
        
        print("\n🎮 Demo Commands:")
        print("  # Enhanced setup wizard")
        print("  python scripts/enhanced_setup_wizard.py")
        print("")
        print("  # Multi-cloud deployment simulation")
        print("  python scripts/universal_deploy.py deploy --variant aws --environment demo")
        print("")
        print("  # Health checks")
        print("  python scripts/health_check.py --variant local --environment demo")
        
        sys.exit(0)
    else:
        print(f"\n⚠️ DEMO NEEDS ATTENTION")
        print("Some components are not working correctly.")
        print("Please check the failed tests and fix the issues.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
