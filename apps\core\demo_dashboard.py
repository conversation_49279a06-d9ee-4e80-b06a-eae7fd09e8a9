"""
Simple Demo Dashboard for Omnify Marketing Cloud
Provides a web interface to demonstrate AI agents without complex setup
"""
from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import json
import random
from datetime import datetime, timedelta
from typing import Dict, Any

router = APIRouter()
templates = Jinja2Templates(directory="templates")

def generate_demo_data() -> Dict[str, Any]:
    """Generate realistic demo data for the dashboard"""
    
    # ROI Engine X Demo Data
    roi_data = {
        "active_campaigns": 12,
        "optimizations_today": random.randint(45, 78),
        "roi_improvement": round(random.uniform(156, 312), 1),
        "revenue_impact": random.randint(8400, 15600),
        "confidence_score": random.randint(85, 95),
        "recent_optimizations": [
            {
                "campaign": "Google Ads - SaaS Keywords",
                "action": "Increased bids by 15%",
                "impact": "+$2,400 projected revenue",
                "confidence": 92,
                "time": "2 minutes ago"
            },
            {
                "campaign": "Facebook - Lookalike Audience",
                "action": "Reallocated budget from low performers",
                "impact": "+$1,800 projected revenue", 
                "confidence": 88,
                "time": "8 minutes ago"
            },
            {
                "campaign": "LinkedIn - Enterprise Targeting",
                "action": "Paused underperforming ad sets",
                "impact": "-$450 wasted spend",
                "confidence": 94,
                "time": "15 minutes ago"
            }
        ]
    }
    
    # Retention Reactor Pro Demo Data
    retention_data = {
        "customers_monitored": random.randint(12800, 15200),
        "at_risk_customers": random.randint(67, 89),
        "high_value_at_risk": random.randint(23, 34),
        "retention_rate": round(random.uniform(76, 82), 1),
        "revenue_at_risk": random.randint(89000, 125000),
        "campaigns_triggered": random.randint(15, 28),
        "recent_alerts": [
            {
                "customer": "TechStart Inc",
                "value": "$15,000 annual",
                "risk_score": 89,
                "reason": "Login frequency dropped 23%, feature usage declined",
                "action": "Retention sequence triggered",
                "time": "5 minutes ago"
            },
            {
                "customer": "DataCorp Solutions", 
                "value": "$8,500 annual",
                "risk_score": 76,
                "reason": "Support ticket sentiment negative, no feature access",
                "action": "Customer success outreach scheduled",
                "time": "12 minutes ago"
            },
            {
                "customer": "CloudTech Enterprises",
                "value": "$22,000 annual", 
                "risk_score": 82,
                "reason": "Usage patterns changed, expansion stalled",
                "action": "Executive touch campaign activated",
                "time": "18 minutes ago"
            }
        ]
    }
    
    # EngageSense Ultra Demo Data
    engagement_data = {
        "total_customers": random.randint(14500, 16800),
        "segments_active": 8,
        "personalizations_today": random.randint(1200, 1800),
        "engagement_improvement": round(random.uniform(320, 380), 1),
        "conversion_lift": round(random.uniform(145, 175), 1),
        "segments": [
            {"name": "Champions", "count": random.randint(1800, 2200), "percentage": 12, "trend": "up"},
            {"name": "Loyal Customers", "count": random.randint(2700, 3200), "percentage": 18, "trend": "stable"},
            {"name": "Potential Loyalists", "count": random.randint(2400, 2800), "percentage": 16, "trend": "up"},
            {"name": "New Customers", "count": random.randint(2200, 2600), "percentage": 15, "trend": "up"},
            {"name": "Promising", "count": random.randint(1900, 2300), "percentage": 13, "trend": "stable"},
            {"name": "Need Attention", "count": random.randint(1600, 2000), "percentage": 11, "trend": "down"},
            {"name": "At Risk", "count": random.randint(1300, 1700), "percentage": 9, "trend": "down"},
            {"name": "Hibernating", "count": random.randint(900, 1300), "percentage": 6, "trend": "stable"}
        ],
        "recent_personalizations": [
            {
                "customer": "Jennifer Martinez",
                "segment": "Potential Champion",
                "action": "Premium content recommendations",
                "engagement": "+45% session time",
                "time": "1 minute ago"
            },
            {
                "customer": "Michael Chen",
                "segment": "At Risk", 
                "action": "Re-engagement campaign triggered",
                "engagement": "Email opened, clicked CTA",
                "time": "4 minutes ago"
            },
            {
                "customer": "Sarah Johnson",
                "segment": "Champion",
                "action": "VIP webinar invitation sent",
                "engagement": "Registered immediately",
                "time": "7 minutes ago"
            }
        ]
    }
    
    # Overall platform metrics
    platform_data = {
        "total_revenue_impact": random.randint(47000, 68000),
        "campaigns_optimized": random.randint(247, 312),
        "customers_retained": random.randint(89, 127),
        "personalizations_delivered": random.randint(15600, 23400),
        "ai_decisions_today": random.randint(1247, 1856),
        "uptime": "99.97%",
        "response_time": f"{random.randint(45, 89)}ms",
        "cloud_variant": "AWS-Manus",
        "last_updated": datetime.now().strftime("%H:%M:%S")
    }
    
    return {
        "roi_engine": roi_data,
        "retention_reactor": retention_data,
        "engagesense": engagement_data,
        "platform": platform_data
    }

@router.get("/demo", response_class=HTMLResponse)
async def demo_dashboard(request: Request):
    """Render the demo dashboard"""
    demo_data = generate_demo_data()
    
    # Simple HTML template embedded (no external template files needed)
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Omnify Marketing Cloud - Live Demo</title>
        <style>
            * {{ margin: 0; padding: 0; box-sizing: border-box; }}
            body {{ 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }}
            .header {{
                background: rgba(255,255,255,0.95);
                padding: 1rem 2rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                backdrop-filter: blur(10px);
            }}
            .header h1 {{
                color: #2d3748;
                font-size: 2rem;
                font-weight: 700;
            }}
            .header p {{
                color: #718096;
                margin-top: 0.5rem;
            }}
            .container {{
                max-width: 1400px;
                margin: 2rem auto;
                padding: 0 2rem;
            }}
            .metrics-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
                margin-bottom: 3rem;
            }}
            .metric-card {{
                background: rgba(255,255,255,0.95);
                border-radius: 12px;
                padding: 2rem;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.2);
            }}
            .metric-card h3 {{
                color: #2d3748;
                font-size: 1.25rem;
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }}
            .metric-value {{
                font-size: 2.5rem;
                font-weight: 700;
                color: #4299e1;
                margin-bottom: 0.5rem;
            }}
            .metric-label {{
                color: #718096;
                font-size: 0.9rem;
            }}
            .ai-agents {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                gap: 2rem;
                margin-bottom: 3rem;
            }}
            .agent-card {{
                background: rgba(255,255,255,0.95);
                border-radius: 12px;
                padding: 2rem;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                backdrop-filter: blur(10px);
            }}
            .agent-header {{
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }}
            .agent-icon {{
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.5rem;
                color: white;
            }}
            .roi-icon {{ background: linear-gradient(135deg, #4299e1, #3182ce); }}
            .retention-icon {{ background: linear-gradient(135deg, #48bb78, #38a169); }}
            .engagement-icon {{ background: linear-gradient(135deg, #ed8936, #dd6b20); }}
            .agent-title {{
                font-size: 1.5rem;
                font-weight: 700;
                color: #2d3748;
            }}
            .agent-status {{
                background: #48bb78;
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
            }}
            .recent-activity {{
                margin-top: 1.5rem;
            }}
            .activity-item {{
                background: #f7fafc;
                border-radius: 8px;
                padding: 1rem;
                margin-bottom: 0.75rem;
                border-left: 4px solid #4299e1;
            }}
            .activity-title {{
                font-weight: 600;
                color: #2d3748;
                margin-bottom: 0.25rem;
            }}
            .activity-details {{
                color: #718096;
                font-size: 0.9rem;
            }}
            .activity-time {{
                color: #a0aec0;
                font-size: 0.8rem;
                margin-top: 0.25rem;
            }}
            .live-indicator {{
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                background: #48bb78;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.9rem;
                font-weight: 600;
                margin-bottom: 2rem;
            }}
            .pulse {{
                width: 8px;
                height: 8px;
                background: white;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }}
            @keyframes pulse {{
                0% {{ opacity: 1; }}
                50% {{ opacity: 0.5; }}
                100% {{ opacity: 1; }}
            }}
            .refresh-btn {{
                background: #4299e1;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: background 0.2s;
            }}
            .refresh-btn:hover {{
                background: #3182ce;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚀 Omnify Marketing Cloud</h1>
            <p>AI-Native Marketing Automation Platform - Live Demo</p>
        </div>
        
        <div class="container">
            <div class="live-indicator">
                <div class="pulse"></div>
                Live Demo - AI Agents Active
            </div>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>💰 Revenue Impact Today</h3>
                    <div class="metric-value">${demo_data['platform']['total_revenue_impact']:,}</div>
                    <div class="metric-label">Generated by AI optimization</div>
                </div>
                <div class="metric-card">
                    <h3>🤖 AI Decisions Made</h3>
                    <div class="metric-value">{demo_data['platform']['ai_decisions_today']:,}</div>
                    <div class="metric-label">Automated optimizations today</div>
                </div>
                <div class="metric-card">
                    <h3>📈 Campaigns Optimized</h3>
                    <div class="metric-value">{demo_data['platform']['campaigns_optimized']}</div>
                    <div class="metric-label">Active campaign improvements</div>
                </div>
                <div class="metric-card">
                    <h3>🎯 Customers Retained</h3>
                    <div class="metric-value">{demo_data['platform']['customers_retained']}</div>
                    <div class="metric-label">Churn prevented this month</div>
                </div>
            </div>
            
            <div class="ai-agents">
                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon roi-icon">💰</div>
                        <div>
                            <div class="agent-title">ROI Engine X™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="metrics-grid" style="grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <div class="metric-value" style="font-size: 1.5rem;">{demo_data['roi_engine']['roi_improvement']}%</div>
                            <div class="metric-label">ROI Improvement</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.5rem;">{demo_data['roi_engine']['optimizations_today']}</div>
                            <div class="metric-label">Optimizations Today</div>
                        </div>
                    </div>
                    <div class="recent-activity">
                        <h4 style="margin-bottom: 1rem; color: #2d3748;">Recent Optimizations</h4>
                        {chr(10).join([f'''
                        <div class="activity-item">
                            <div class="activity-title">{opt['campaign']}</div>
                            <div class="activity-details">{opt['action']} • {opt['impact']}</div>
                            <div class="activity-time">Confidence: {opt['confidence']}% • {opt['time']}</div>
                        </div>
                        ''' for opt in demo_data['roi_engine']['recent_optimizations']])}
                    </div>
                </div>
                
                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon retention-icon">🔄</div>
                        <div>
                            <div class="agent-title">Retention Reactor Pro™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="metrics-grid" style="grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <div class="metric-value" style="font-size: 1.5rem;">{demo_data['retention_reactor']['at_risk_customers']}</div>
                            <div class="metric-label">At-Risk Customers</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.5rem;">{demo_data['retention_reactor']['retention_rate']}%</div>
                            <div class="metric-label">Retention Rate</div>
                        </div>
                    </div>
                    <div class="recent-activity">
                        <h4 style="margin-bottom: 1rem; color: #2d3748;">Recent Alerts</h4>
                        {chr(10).join([f'''
                        <div class="activity-item">
                            <div class="activity-title">{alert['customer']} ({alert['value']})</div>
                            <div class="activity-details">Risk Score: {alert['risk_score']}% • {alert['reason']}</div>
                            <div class="activity-time">{alert['action']} • {alert['time']}</div>
                        </div>
                        ''' for alert in demo_data['retention_reactor']['recent_alerts']])}
                    </div>
                </div>
                
                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-icon engagement-icon">👥</div>
                        <div>
                            <div class="agent-title">EngageSense Ultra™</div>
                            <div class="agent-status">ACTIVE</div>
                        </div>
                    </div>
                    <div class="metrics-grid" style="grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <div class="metric-value" style="font-size: 1.5rem;">{demo_data['engagesense']['engagement_improvement']}%</div>
                            <div class="metric-label">Engagement Boost</div>
                        </div>
                        <div>
                            <div class="metric-value" style="font-size: 1.5rem;">{demo_data['engagesense']['personalizations_today']}</div>
                            <div class="metric-label">Personalizations Today</div>
                        </div>
                    </div>
                    <div class="recent-activity">
                        <h4 style="margin-bottom: 1rem; color: #2d3748;">Recent Personalizations</h4>
                        {chr(10).join([f'''
                        <div class="activity-item">
                            <div class="activity-title">{pers['customer']} ({pers['segment']})</div>
                            <div class="activity-details">{pers['action']} • {pers['engagement']}</div>
                            <div class="activity-time">{pers['time']}</div>
                        </div>
                        ''' for pers in demo_data['engagesense']['recent_personalizations']])}
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 3rem;">
                <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Live Data</button>
                <p style="margin-top: 1rem; color: rgba(255,255,255,0.8);">
                    Last updated: {demo_data['platform']['last_updated']} | 
                    Cloud: {demo_data['platform']['cloud_variant']} | 
                    Uptime: {demo_data['platform']['uptime']} | 
                    Response: {demo_data['platform']['response_time']}
                </p>
            </div>
        </div>
        
        <script>
            // Auto-refresh every 30 seconds to simulate live data
            setTimeout(() => location.reload(), 30000);
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)

@router.get("/demo/data")
async def demo_data_api():
    """API endpoint for demo data (for AJAX updates)"""
    return generate_demo_data()
