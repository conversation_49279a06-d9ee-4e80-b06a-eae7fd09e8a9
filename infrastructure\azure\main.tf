# Terraform configuration for Azure OpenAI Accelerator variant
# Complete Infrastructure as Code for production deployment

terraform {
  required_version = ">= 1.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

provider "azurerm" {
  features {}
}

# Variables
variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "location" {
  description = "Azure region"
  type        = string
  default     = "East US"
}

variable "resource_group_name" {
  description = "Resource group name"
  type        = string
  default     = "omnify"
}

# Resource Group
resource "azurerm_resource_group" "omnify_rg" {
  name     = "${var.resource_group_name}-${var.environment}"
  location = var.location

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
    Variant     = "azure-openai"
  }
}

# Virtual Network
resource "azurerm_virtual_network" "omnify_vnet" {
  name                = "omnify-vnet-${var.environment}"
  address_space       = ["10.0.0.0/16"]
  location            = azurerm_resource_group.omnify_rg.location
  resource_group_name = azurerm_resource_group.omnify_rg.name

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Subnets
resource "azurerm_subnet" "omnify_subnet" {
  name                 = "omnify-subnet-${var.environment}"
  resource_group_name  = azurerm_resource_group.omnify_rg.name
  virtual_network_name = azurerm_virtual_network.omnify_vnet.name
  address_prefixes     = ["********/24"]

  delegation {
    name = "container-instances"
    service_delegation {
      name    = "Microsoft.ContainerInstance/containerGroups"
      actions = ["Microsoft.Network/virtualNetworks/subnets/action"]
    }
  }
}

# Network Security Group
resource "azurerm_network_security_group" "omnify_nsg" {
  name                = "omnify-nsg-${var.environment}"
  location            = azurerm_resource_group.omnify_rg.location
  resource_group_name = azurerm_resource_group.omnify_rg.name

  security_rule {
    name                       = "HTTP"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "HTTPS"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Associate NSG with Subnet
resource "azurerm_subnet_network_security_group_association" "omnify_nsg_association" {
  subnet_id                 = azurerm_subnet.omnify_subnet.id
  network_security_group_id = azurerm_network_security_group.omnify_nsg.id
}

# Azure SQL Database
resource "azurerm_mssql_server" "omnify_sql_server" {
  name                         = "omnify-sql-${var.environment}-${random_string.suffix.result}"
  resource_group_name          = azurerm_resource_group.omnify_rg.name
  location                     = azurerm_resource_group.omnify_rg.location
  version                      = "12.0"
  administrator_login          = "omnify_admin"
  administrator_login_password = random_password.sql_password.result

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

resource "azurerm_mssql_database" "omnify_database" {
  name           = "omnify"
  server_id      = azurerm_mssql_server.omnify_sql_server.id
  collation      = "SQL_Latin1_General_CP1_CI_AS"
  license_type   = "LicenseIncluded"
  max_size_gb    = 100
  sku_name       = "S2"
  zone_redundant = false

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Azure Cache for Redis
resource "azurerm_redis_cache" "omnify_redis" {
  name                = "omnify-redis-${var.environment}-${random_string.suffix.result}"
  location            = azurerm_resource_group.omnify_rg.location
  resource_group_name = azurerm_resource_group.omnify_rg.name
  capacity            = 2
  family              = "C"
  sku_name            = "Standard"
  enable_non_ssl_port = false
  minimum_tls_version = "1.2"

  redis_configuration {
    enable_authentication = true
  }

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Azure OpenAI Service
resource "azurerm_cognitive_account" "omnify_openai" {
  name                = "omnify-openai-${var.environment}-${random_string.suffix.result}"
  location            = azurerm_resource_group.omnify_rg.location
  resource_group_name = azurerm_resource_group.omnify_rg.name
  kind                = "OpenAI"
  sku_name            = "S0"

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Container Registry
resource "azurerm_container_registry" "omnify_acr" {
  name                = "omnifyacr${var.environment}${random_string.suffix.result}"
  resource_group_name = azurerm_resource_group.omnify_rg.name
  location            = azurerm_resource_group.omnify_rg.location
  sku                 = "Standard"
  admin_enabled       = true

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Container Instance
resource "azurerm_container_group" "omnify_container" {
  name                = "omnify-container-${var.environment}"
  location            = azurerm_resource_group.omnify_rg.location
  resource_group_name = azurerm_resource_group.omnify_rg.name
  ip_address_type     = "Public"
  dns_name_label      = "omnify-${var.environment}-${random_string.suffix.result}"
  os_type             = "Linux"
  subnet_ids          = [azurerm_subnet.omnify_subnet.id]

  container {
    name   = "omnify-api"
    image  = "${azurerm_container_registry.omnify_acr.login_server}/omnify:latest"
    cpu    = "2"
    memory = "4"

    ports {
      port     = 8000
      protocol = "TCP"
    }

    environment_variables = {
      CLOUD_VARIANT    = "azure"
      AI_ENGINE        = "azure_openai"
      DATABASE_TYPE    = "azure_sql"
      CACHE_TYPE       = "azure_redis"
      ENVIRONMENT      = var.environment
    }

    secure_environment_variables = {
      DATABASE_URL = "mssql://${azurerm_mssql_server.omnify_sql_server.administrator_login}:${random_password.sql_password.result}@${azurerm_mssql_server.omnify_sql_server.fully_qualified_domain_name}:1433/${azurerm_mssql_database.omnify_database.name}"
      REDIS_URL    = "redis://:${azurerm_redis_cache.omnify_redis.primary_access_key}@${azurerm_redis_cache.omnify_redis.hostname}:6380"
      AZURE_OPENAI_ENDPOINT = azurerm_cognitive_account.omnify_openai.endpoint
      AZURE_OPENAI_KEY      = azurerm_cognitive_account.omnify_openai.primary_access_key
    }
  }

  image_registry_credential {
    server   = azurerm_container_registry.omnify_acr.login_server
    username = azurerm_container_registry.omnify_acr.admin_username
    password = azurerm_container_registry.omnify_acr.admin_password
  }

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Application Insights
resource "azurerm_application_insights" "omnify_insights" {
  name                = "omnify-insights-${var.environment}"
  location            = azurerm_resource_group.omnify_rg.location
  resource_group_name = azurerm_resource_group.omnify_rg.name
  application_type    = "web"

  tags = {
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Random resources for unique naming
resource "random_string" "suffix" {
  length  = 8
  special = false
  upper   = false
}

resource "random_password" "sql_password" {
  length  = 16
  special = true
}

# Outputs
output "resource_group_name" {
  description = "Resource group name"
  value       = azurerm_resource_group.omnify_rg.name
}

output "container_fqdn" {
  description = "Container instance FQDN"
  value       = azurerm_container_group.omnify_container.fqdn
}

output "sql_server_fqdn" {
  description = "SQL Server FQDN"
  value       = azurerm_mssql_server.omnify_sql_server.fully_qualified_domain_name
}

output "redis_hostname" {
  description = "Redis hostname"
  value       = azurerm_redis_cache.omnify_redis.hostname
}

output "openai_endpoint" {
  description = "Azure OpenAI endpoint"
  value       = azurerm_cognitive_account.omnify_openai.endpoint
}

output "container_registry_url" {
  description = "Container registry URL"
  value       = azurerm_container_registry.omnify_acr.login_server
}
