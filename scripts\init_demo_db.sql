-- Demo database initialization for Omnify Marketing Cloud
-- This script sets up demo data for immediate demonstration

-- Ensure database exists
CREATE DATABASE IF NOT EXISTS omnify_db;

-- Connect to the database
\c omnify_db;

-- Create demo data after tables are created by SQLAlchemy
-- This will be populated by the Python application

-- Demo companies
INSERT INTO clients (company_name, industry, monthly_spend, created_at, updated_at) VALUES
('TechStart Solutions', 'Technology', 50000.00, NOW(), NOW()),
('GrowthCorp Marketing', 'Marketing', 75000.00, NOW(), NOW()),
('ScaleUp Ventures', 'E-commerce', 100000.00, NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Demo users (passwords will be hashed by the application)
-- These are created by the application startup process

-- Demo campaigns
INSERT INTO campaigns (name, platform, status, budget, spent, impressions, clicks, conversions, ctr, cpc, roas, client_id, created_at, updated_at) VALUES
('Q4 Product Launch', 'google_ads', 'active', 25000.00, 18500.00, 125000, 3750, 187, 3.0, 4.93, 4.2, 1, NOW(), NOW()),
('Holiday Sale Campaign', 'meta_ads', 'active', 15000.00, 12300.00, 89000, 2670, 134, 3.0, 4.61, 3.8, 1, NOW(), NOW()),
('Brand Awareness Drive', 'google_ads', 'active', 30000.00, 22100.00, 156000, 4680, 234, 3.0, 4.72, 3.9, 2, NOW(), NOW()),
('Retargeting Campaign', 'meta_ads', 'paused', 10000.00, 8900.00, 67000, 2010, 120, 3.0, 4.43, 4.1, 2, NOW(), NOW()),
('Lead Generation', 'google_ads', 'active', 20000.00, 16700.00, 98000, 2940, 176, 3.0, 5.68, 3.7, 3, NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Demo customer profiles
INSERT INTO customer_profiles (email, first_name, last_name, total_spent, order_count, last_purchase_date, churn_probability, engagement_score, client_id, created_at, updated_at) VALUES
('<EMAIL>', 'John', 'Doe', 1250.00, 5, NOW() - INTERVAL '7 days', 0.15, 0.85, 1, NOW(), NOW()),
('<EMAIL>', 'Jane', 'Smith', 2100.00, 8, NOW() - INTERVAL '3 days', 0.08, 0.92, 1, NOW(), NOW()),
('<EMAIL>', 'Bob', 'Wilson', 850.00, 3, NOW() - INTERVAL '45 days', 0.65, 0.45, 1, NOW(), NOW()),
('<EMAIL>', 'Alice', 'Brown', 3200.00, 12, NOW() - INTERVAL '2 days', 0.05, 0.95, 2, NOW(), NOW()),
('<EMAIL>', 'Charlie', 'Davis', 750.00, 2, NOW() - INTERVAL '60 days', 0.78, 0.35, 2, NOW(), NOW()),
('<EMAIL>', 'Diana', 'Miller', 1800.00, 7, NOW() - INTERVAL '5 days', 0.12, 0.88, 3, NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Demo AI decisions
INSERT INTO ai_decisions (campaign_id, decision_type, ai_engine, confidence, recommendation, action_taken, expected_impact, actual_impact, created_at) VALUES
(1, 'bid_adjustment', 'manus_rl', 0.92, '{"action": "increase_bid", "amount": 0.15, "reason": "High ROAS performance"}', 'increase_bid', '{"roas_improvement": 0.08, "volume_increase": 0.12}', '{"roas_improvement": 0.09, "volume_increase": 0.11}', NOW() - INTERVAL '2 hours'),
(2, 'budget_optimization', 'demo', 0.87, '{"action": "reallocate_budget", "from": "display", "to": "search", "amount": 2000}', 'reallocate_budget', '{"efficiency_gain": 0.15}', '{"efficiency_gain": 0.14}', NOW() - INTERVAL '4 hours'),
(3, 'audience_expansion', 'manus_rl', 0.89, '{"action": "expand_audience", "similarity": 0.85, "budget_increase": 0.20}', 'expand_audience', '{"reach_increase": 0.25, "cpa_change": 0.05}', '{"reach_increase": 0.23, "cpa_change": 0.04}', NOW() - INTERVAL '6 hours')
ON CONFLICT DO NOTHING;

-- Demo retention actions
INSERT INTO retention_actions (customer_id, action_type, action_data, predicted_impact, actual_impact, status, created_at, completed_at) VALUES
(3, 'email_campaign', '{"template": "win_back", "discount": 15, "subject": "We miss you! 15% off your next order"}', 0.35, 0.42, 'completed', NOW() - INTERVAL '3 days', NOW() - INTERVAL '2 days'),
(5, 'personalized_offer', '{"product_category": "electronics", "discount": 20, "urgency": "limited_time"}', 0.28, NULL, 'sent', NOW() - INTERVAL '1 day', NULL),
(1, 'loyalty_program', '{"tier_upgrade": "gold", "benefits": ["free_shipping", "early_access"]}', 0.15, 0.18, 'completed', NOW() - INTERVAL '5 days', NOW() - INTERVAL '4 days')
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_campaigns_client_id ON campaigns(client_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns(status);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_client_id ON customer_profiles(client_id);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_churn ON customer_profiles(churn_probability);
CREATE INDEX IF NOT EXISTS idx_ai_decisions_campaign_id ON ai_decisions(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ai_decisions_created_at ON ai_decisions(created_at);

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO omnify_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO omnify_user;
