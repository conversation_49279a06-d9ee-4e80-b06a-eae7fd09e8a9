# AWS-specific requirements for Omnify Marketing Cloud
# Optimized for AWS-Manus Hybrid variant

# Core application dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# Cache and messaging
redis[hiredis]==5.0.1
celery[redis]==5.3.4

# AWS SDK and services
boto3==1.34.0
botocore==1.34.0
aioboto3==12.0.0
aws-lambda-powertools==2.28.0

# AWS-specific AI services
anthropic==0.7.8  # For AWS Bedrock Claude models
langchain-aws==0.1.0
langchain-community==0.0.10

# Manus RL engine dependencies
numpy==1.24.4
scipy==1.11.4
scikit-learn==1.3.2
pandas==2.1.4
torch==2.1.2
gymnasium==0.29.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# Data validation and serialization
marshmallow==3.20.1
marshmallow-dataclass==8.6.0

# Date and time
python-dateutil==2.8.2
pytz==2023.3

# Environment and configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Testing (for development)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# AWS-specific monitoring
aws-xray-sdk==2.12.1
watchtower==3.0.1

# Performance optimization
orjson==3.9.10
ujson==5.8.0

# Image processing (for marketing assets)
Pillow==10.1.0

# Email
emails==0.6.0

# Stripe for billing
stripe==7.8.0

# WebSocket support
websockets==12.0

# AWS Graviton3 optimized packages
# These are compiled for ARM64 architecture
cffi==1.16.0
cryptography==41.0.8
lxml==4.9.3

# Additional AWS services
aws-cdk-lib==2.108.0
constructs==10.3.0
