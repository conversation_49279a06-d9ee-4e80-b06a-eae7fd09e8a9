# Terraform configuration for AWS-Manus Hybrid variant
# Complete Infrastructure as Code for production deployment

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Variables
variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "cluster_name" {
  description = "ECS cluster name"
  type        = string
  default     = "omnify"
}

# VPC Configuration
resource "aws_vpc" "omnify_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "omnify-vpc-${var.environment}"
    Environment = var.environment
    Project     = "omnify-marketing-cloud"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "omnify_igw" {
  vpc_id = aws_vpc.omnify_vpc.id

  tags = {
    Name        = "omnify-igw-${var.environment}"
    Environment = var.environment
  }
}

# Public Subnets
resource "aws_subnet" "public_subnets" {
  count             = 2
  vpc_id            = aws_vpc.omnify_vpc.id
  cidr_block        = "10.0.${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  map_public_ip_on_launch = true

  tags = {
    Name        = "omnify-public-subnet-${count.index + 1}-${var.environment}"
    Environment = var.environment
  }
}

# Private Subnets
resource "aws_subnet" "private_subnets" {
  count             = 2
  vpc_id            = aws_vpc.omnify_vpc.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "omnify-private-subnet-${count.index + 1}-${var.environment}"
    Environment = var.environment
  }
}

# Route Table for Public Subnets
resource "aws_route_table" "public_rt" {
  vpc_id = aws_vpc.omnify_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.omnify_igw.id
  }

  tags = {
    Name        = "omnify-public-rt-${var.environment}"
    Environment = var.environment
  }
}

# Route Table Associations
resource "aws_route_table_association" "public_rta" {
  count          = length(aws_subnet.public_subnets)
  subnet_id      = aws_subnet.public_subnets[count.index].id
  route_table_id = aws_route_table.public_rt.id
}

# Security Groups
resource "aws_security_group" "ecs_sg" {
  name_prefix = "omnify-ecs-${var.environment}"
  vpc_id      = aws_vpc.omnify_vpc.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "omnify-ecs-sg-${var.environment}"
    Environment = var.environment
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "omnify_cluster" {
  name = "${var.cluster_name}-${var.environment}"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = {
    Name        = "omnify-cluster-${var.environment}"
    Environment = var.environment
  }
}

# ECS Cluster Capacity Providers
resource "aws_ecs_cluster_capacity_providers" "omnify_capacity_providers" {
  cluster_name = aws_ecs_cluster.omnify_cluster.name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE"
  }
}

# Aurora Serverless v2 Subnet Group
resource "aws_db_subnet_group" "omnify_db_subnet_group" {
  name       = "omnify-db-subnet-group-${var.environment}"
  subnet_ids = aws_subnet.private_subnets[*].id

  tags = {
    Name        = "omnify-db-subnet-group-${var.environment}"
    Environment = var.environment
  }
}

# Aurora Serverless v2 Cluster
resource "aws_rds_cluster" "omnify_aurora" {
  cluster_identifier      = "omnify-aurora-${var.environment}"
  engine                  = "aurora-postgresql"
  engine_mode             = "provisioned"
  engine_version          = "15.4"
  database_name           = "omnify"
  master_username         = "omnify_admin"
  manage_master_user_password = true
  
  db_subnet_group_name   = aws_db_subnet_group.omnify_db_subnet_group.name
  vpc_security_group_ids = [aws_security_group.aurora_sg.id]

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 0.5
  }

  backup_retention_period = 7
  preferred_backup_window = "03:00-04:00"
  
  skip_final_snapshot = var.environment != "prod"

  tags = {
    Name        = "omnify-aurora-${var.environment}"
    Environment = var.environment
  }
}

# Aurora Instance
resource "aws_rds_cluster_instance" "omnify_aurora_instance" {
  identifier         = "omnify-aurora-instance-${var.environment}"
  cluster_identifier = aws_rds_cluster.omnify_aurora.id
  instance_class     = "db.serverless"
  engine             = aws_rds_cluster.omnify_aurora.engine
  engine_version     = aws_rds_cluster.omnify_aurora.engine_version

  tags = {
    Name        = "omnify-aurora-instance-${var.environment}"
    Environment = var.environment
  }
}

# Security Group for Aurora
resource "aws_security_group" "aurora_sg" {
  name_prefix = "omnify-aurora-${var.environment}"
  vpc_id      = aws_vpc.omnify_vpc.id

  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_sg.id]
  }

  tags = {
    Name        = "omnify-aurora-sg-${var.environment}"
    Environment = var.environment
  }
}

# ElastiCache Subnet Group
resource "aws_elasticache_subnet_group" "omnify_redis_subnet_group" {
  name       = "omnify-redis-subnet-group-${var.environment}"
  subnet_ids = aws_subnet.private_subnets[*].id

  tags = {
    Name        = "omnify-redis-subnet-group-${var.environment}"
    Environment = var.environment
  }
}

# ElastiCache Redis Cluster
resource "aws_elasticache_replication_group" "omnify_redis" {
  replication_group_id       = "omnify-redis-${var.environment}"
  description                = "Redis cluster for Omnify Marketing Cloud"
  
  node_type                  = "cache.r6g.large"
  port                       = 6379
  parameter_group_name       = "default.redis7"
  
  num_cache_clusters         = 2
  automatic_failover_enabled = true
  multi_az_enabled          = true
  
  subnet_group_name = aws_elasticache_subnet_group.omnify_redis_subnet_group.name
  security_group_ids = [aws_security_group.redis_sg.id]

  tags = {
    Name        = "omnify-redis-${var.environment}"
    Environment = var.environment
  }
}

# Security Group for Redis
resource "aws_security_group" "redis_sg" {
  name_prefix = "omnify-redis-${var.environment}"
  vpc_id      = aws_vpc.omnify_vpc.id

  ingress {
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_sg.id]
  }

  tags = {
    Name        = "omnify-redis-sg-${var.environment}"
    Environment = var.environment
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

# Outputs
output "vpc_id" {
  description = "VPC ID"
  value       = aws_vpc.omnify_vpc.id
}

output "ecs_cluster_name" {
  description = "ECS Cluster name"
  value       = aws_ecs_cluster.omnify_cluster.name
}

output "aurora_endpoint" {
  description = "Aurora cluster endpoint"
  value       = aws_rds_cluster.omnify_aurora.endpoint
}

output "redis_endpoint" {
  description = "Redis cluster endpoint"
  value       = aws_elasticache_replication_group.omnify_redis.primary_endpoint_address
}
