"""
AI Agents API endpoints for ROI Engine X™, Retention Reactor Pro™, and EngageSense Ultra™
"""
from typing import List, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field
import structlog

from apps.core.database import get_db, Campaign, CampaignMetric, AIDecision, Client
from apps.auth.dependencies import get_current_user
from lib.ai.decision_engine import ROIEngineX, AIDecisionRequest
from lib.connectors.google_ads import GoogleAdsConnector
from lib.connectors.meta_ads import MetaAdsConnector

logger = structlog.get_logger()

router = APIRouter()

class OptimizationRequest(BaseModel):
    client_id: str
    campaign_ids: List[str] = Field(default_factory=list)
    agent_type: str = Field(..., regex="^(roi_engine|retention_reactor|engage_sense)$")
    force_execution: bool = Field(default=False)

class OptimizationResponse(BaseModel):
    request_id: str
    client_id: str
    agent_type: str
    decisions: List[Dict[str, Any]]
    status: str
    timestamp: datetime

class AgentStatusResponse(BaseModel):
    agent_type: str
    status: str
    last_run: datetime
    decisions_today: int
    success_rate: float

@router.post("/optimize", response_model=OptimizationResponse)
async def trigger_optimization(
    request: OptimizationRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Trigger AI agent optimization for campaigns
    """
    try:
        # Verify client exists
        client_query = select(Client).where(Client.id == request.client_id)
        client_result = await db.execute(client_query)
        client = client_result.scalar_one_or_none()

        if not client:
            raise HTTPException(status_code=404, detail="Client not found")

        # Get campaigns to optimize
        campaigns_query = select(Campaign).where(
            Campaign.client_id == request.client_id,
            Campaign.status == "active"
        )

        if request.campaign_ids:
            campaigns_query = campaigns_query.where(
                Campaign.campaign_id.in_(request.campaign_ids)
            )

        campaigns_result = await db.execute(campaigns_query)
        campaigns = campaigns_result.scalars().all()

        if not campaigns:
            raise HTTPException(status_code=404, detail="No active campaigns found")

        # Generate request ID
        request_id = f"opt_{int(datetime.utcnow().timestamp())}"

        # Process based on agent type
        if request.agent_type == "roi_engine":
            background_tasks.add_task(
                _run_roi_engine_optimization,
                request_id,
                client,
                campaigns,
                db
            )
        elif request.agent_type == "retention_reactor":
            background_tasks.add_task(
                _run_retention_reactor_optimization,
                request_id,
                client,
                campaigns,
                db
            )
        elif request.agent_type == "engage_sense":
            background_tasks.add_task(
                _run_engage_sense_optimization,
                request_id,
                client,
                campaigns,
                db
            )

        logger.info(
            "Optimization triggered",
            request_id=request_id,
            client_id=request.client_id,
            agent_type=request.agent_type,
            campaign_count=len(campaigns)
        )

        return OptimizationResponse(
            request_id=request_id,
            client_id=request.client_id,
            agent_type=request.agent_type,
            decisions=[],
            status="processing",
            timestamp=datetime.utcnow()
        )

    except Exception as e:
        logger.error("Failed to trigger optimization", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to trigger optimization")

@router.get("/status/{agent_type}", response_model=AgentStatusResponse)
async def get_agent_status(
    agent_type: str,
    client_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get status of specific AI agent
    """
    try:
        # Get recent decisions for this agent
        today = datetime.utcnow().date()
        decisions_query = select(AIDecision).where(
            AIDecision.client_id == client_id,
            AIDecision.agent_type == agent_type,
            AIDecision.created_at >= today
        )

        decisions_result = await db.execute(decisions_query)
        decisions = decisions_result.scalars().all()

        # Calculate metrics
        decisions_today = len(decisions)
        successful_decisions = len([d for d in decisions if d.result == "success"])
        success_rate = (successful_decisions / decisions_today * 100) if decisions_today > 0 else 0.0

        # Get last run time
        last_run = max([d.created_at for d in decisions]) if decisions else datetime.min

        # Determine status
        if not decisions:
            status = "idle"
        elif datetime.utcnow() - last_run < timedelta(hours=1):
            status = "active"
        else:
            status = "idle"

        return AgentStatusResponse(
            agent_type=agent_type,
            status=status,
            last_run=last_run,
            decisions_today=decisions_today,
            success_rate=success_rate
        )

    except Exception as e:
        logger.error("Failed to get agent status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get agent status")

@router.get("/decisions", response_model=List[Dict[str, Any]])
async def get_recent_decisions(
    client_id: str,
    agent_type: str = None,
    days: int = 7,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get recent AI decisions for analysis
    """
    try:
        # Calculate date range
        start_date = datetime.utcnow() - timedelta(days=days)

        # Build query
        query = select(AIDecision).where(
            AIDecision.client_id == client_id,
            AIDecision.created_at >= start_date
        )

        if agent_type:
            query = query.where(AIDecision.agent_type == agent_type)

        query = query.order_by(AIDecision.created_at.desc())

        result = await db.execute(query)
        decisions = result.scalars().all()

        # Format response
        decisions_data = []
        for decision in decisions:
            decisions_data.append({
                "id": decision.id,
                "agent_type": decision.agent_type,
                "decision_id": decision.decision_id,
                "decision": decision.decision,
                "confidence": decision.confidence,
                "action_taken": decision.action_taken,
                "result": decision.result,
                "created_at": decision.created_at.isoformat(),
                "error_message": decision.error_message
            })

        return decisions_data

    except Exception as e:
        logger.error("Failed to get recent decisions", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get recent decisions")

# Background task functions

async def _run_roi_engine_optimization(
    request_id: str,
    client: Client,
    campaigns: List[Campaign],
    db: AsyncSession
):
    """
    Run ROI Engine X™ optimization in background
    """
    try:
        roi_engine = ROIEngineX()

        for campaign in campaigns:
            # Get historical metrics
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)

            metrics_query = select(CampaignMetric).where(
                CampaignMetric.campaign_id == campaign.id,
                CampaignMetric.date >= start_date
            ).order_by(CampaignMetric.date.desc())

            metrics_result = await db.execute(metrics_query)
            metrics = metrics_result.scalars().all()

            # Convert to dict format
            historical_metrics = [
                {
                    "date": m.date.isoformat(),
                    "spend": m.spend,
                    "revenue": m.revenue,
                    "conversions": m.conversions,
                    "clicks": m.clicks,
                    "impressions": m.impressions
                }
                for m in metrics
            ]

            # Prepare campaign data
            campaign_data = {
                "campaign_id": campaign.campaign_id,
                "platform": campaign.platform,
                "budget": campaign.budget,
                "target_cac": campaign.target_cac,
                "budget_remaining": campaign.budget * 0.7,  # Assume 70% remaining
                "days_remaining": 30
            }

            # Get AI decision
            decision = await roi_engine.optimize_campaign(
                client_id=str(client.id),
                campaign_data=campaign_data,
                historical_metrics=historical_metrics
            )

            # Save decision to database
            ai_decision = AIDecision(
                client_id=client.id,
                agent_type="roi_engine",
                decision_id=f"{request_id}_{campaign.id}",
                input_data=campaign_data,
                decision=decision.dict(),
                confidence=decision.confidence,
                action_taken=decision.action,
                result="pending"
            )

            db.add(ai_decision)

            # Execute action if confidence is high enough
            if decision.confidence >= 0.8 and decision.action != "no_action":
                try:
                    await _execute_campaign_action(campaign, decision, client)
                    ai_decision.result = "success"
                except Exception as e:
                    ai_decision.result = "failed"
                    ai_decision.error_message = str(e)
                    logger.error(
                        "Failed to execute campaign action",
                        error=str(e),
                        campaign_id=campaign.id
                    )

        await db.commit()

        logger.info(
            "ROI Engine optimization completed",
            request_id=request_id,
            client_id=client.id,
            campaign_count=len(campaigns)
        )

    except Exception as e:
        logger.error(
            "ROI Engine optimization failed",
            error=str(e),
            request_id=request_id
        )

async def _run_retention_reactor_optimization(
    request_id: str,
    client: Client,
    campaigns: List[Campaign],
    db: AsyncSession
):
    """
    Run Retention Reactor Pro™ optimization
    """
    try:
        from lib.ai.retention_reactor import RetentionReactorPro, CustomerData
        import numpy as np

        retention_reactor = RetentionReactorPro()
        await retention_reactor.initialize_model()

        # Get customer data for churn prediction
        # In production, this would come from your customer database
        sample_customers = [
            CustomerData(
                customer_id=f"cust_{i}",
                days_since_last_purchase=np.random.randint(1, 365),
                total_purchases=np.random.randint(1, 50),
                average_order_value=np.random.uniform(20, 500),
                total_spent=np.random.uniform(100, 10000),
                email_engagement_rate=np.random.uniform(0, 100),
                support_tickets=np.random.randint(0, 10),
                last_login_days=np.random.randint(1, 180),
                subscription_tier="premium",
                geographic_region="US"
            )
            for i in range(10)  # Process 10 customers for demo
        ]

        for customer in sample_customers:
            # Predict churn
            prediction = await retention_reactor.predict_churn(customer)

            # Save prediction to database
            ai_decision = AIDecision(
                client_id=client.id,
                agent_type="retention_reactor",
                decision_id=f"{request_id}_{customer.customer_id}",
                input_data=customer.dict(),
                decision=prediction.dict(),
                confidence=prediction.confidence,
                action_taken=f"churn_risk_{prediction.risk_level}",
                result="success"
            )
            db.add(ai_decision)

            # Execute high-priority retention actions
            if prediction.risk_level in ["high", "critical"]:
                for action_data in prediction.recommended_actions[:2]:  # Top 2 actions
                    try:
                        from lib.ai.retention_reactor import RetentionAction
                        action = RetentionAction(
                            action_type=action_data["action_type"],
                            customer_id=customer.customer_id,
                            parameters=action_data["parameters"],
                            priority=action_data["priority"],
                            estimated_impact=action_data["estimated_impact"],
                            cost=action_data["cost"]
                        )

                        result = await retention_reactor.execute_retention_action(action, customer)
                        logger.info(
                            "Retention action executed",
                            customer_id=customer.customer_id,
                            action_type=action.action_type,
                            result=result["status"]
                        )
                    except Exception as e:
                        logger.error(
                            "Failed to execute retention action",
                            error=str(e),
                            customer_id=customer.customer_id
                        )

        await db.commit()

        logger.info(
            "Retention Reactor optimization completed",
            request_id=request_id,
            client_id=client.id,
            customers_processed=len(sample_customers)
        )

    except Exception as e:
        logger.error(
            "Retention Reactor optimization failed",
            error=str(e),
            request_id=request_id
        )

async def _run_engage_sense_optimization(
    request_id: str,
    client: Client,
    campaigns: List[Campaign],
    db: AsyncSession
):
    """
    Run EngageSense Ultra™ optimization
    """
    try:
        from lib.ai.engage_sense import EngageSenseUltra, CustomerBehavior
        import numpy as np

        engage_sense = EngageSenseUltra()
        await engage_sense.initialize_models()

        # Get customer behavior data
        # In production, this would come from your analytics/tracking system
        sample_behaviors = [
            CustomerBehavior(
                customer_id=f"cust_{i}",
                page_views=np.random.randint(5, 100),
                session_duration_avg=np.random.uniform(2, 30),
                bounce_rate=np.random.uniform(20, 80),
                email_opens=np.random.randint(0, 20),
                email_clicks=np.random.randint(0, 10),
                social_shares=np.random.randint(0, 5),
                product_views=np.random.randint(1, 50),
                cart_additions=np.random.randint(0, 10),
                purchase_frequency=np.random.uniform(0.1, 5.0),
                support_interactions=np.random.randint(0, 5),
                feature_usage_score=np.random.uniform(10, 100),
                mobile_vs_desktop_ratio=np.random.uniform(0.2, 0.8)
            )
            for i in range(15)  # Process 15 customers for demo
        ]

        for behavior in sample_behaviors:
            # Score customer engagement
            engagement_score = await engage_sense.score_customer_engagement(behavior)

            # Save engagement analysis to database
            ai_decision = AIDecision(
                client_id=client.id,
                agent_type="engage_sense",
                decision_id=f"{request_id}_{behavior.customer_id}",
                input_data=behavior.dict(),
                decision=engagement_score.dict(),
                confidence=0.85,
                action_taken=f"segment_{engagement_score.segment}",
                result="success"
            )
            db.add(ai_decision)

            # Execute personalized outreach for high-value segments
            if engagement_score.segment in ["champion", "loyal", "potential_loyalist"]:
                try:
                    # Execute next best action
                    action_type = engagement_score.next_best_action["action"]

                    if action_type in ["vip_program_invite", "loyalty_reward", "educational_content"]:
                        result = await engage_sense.execute_personalized_outreach(
                            behavior.customer_id,
                            engagement_score,
                            "email_campaign"
                        )

                        logger.info(
                            "Personalized outreach executed",
                            customer_id=behavior.customer_id,
                            segment=engagement_score.segment,
                            action=action_type,
                            result=result["status"]
                        )
                except Exception as e:
                    logger.error(
                        "Failed to execute personalized outreach",
                        error=str(e),
                        customer_id=behavior.customer_id
                    )

        await db.commit()

        logger.info(
            "EngageSense optimization completed",
            request_id=request_id,
            client_id=client.id,
            customers_processed=len(sample_behaviors)
        )

    except Exception as e:
        logger.error(
            "EngageSense optimization failed",
            error=str(e),
            request_id=request_id
        )

async def _execute_campaign_action(campaign: Campaign, decision, client: Client):
    """
    Execute the recommended action on the campaign
    """
    if campaign.platform == "google_ads" and client.google_ads_customer_id:
        connector = GoogleAdsConnector(client.google_ads_customer_id)

        if decision.action == "increase_bid":
            adjustment = decision.params.get("bid_adjustment", 0.1)
            await connector.update_campaign_bid(campaign.campaign_id, adjustment)
        elif decision.action == "decrease_bid":
            adjustment = -abs(decision.params.get("bid_adjustment", 0.1))
            await connector.update_campaign_bid(campaign.campaign_id, adjustment)

    elif campaign.platform == "meta_ads" and client.meta_ads_account_id:
        connector = MetaAdsConnector(client.meta_ads_account_id)

        if decision.action in ["increase_bid", "decrease_bid"]:
            adjustment = decision.params.get("budget_adjustment", 0.1)
            if decision.action == "decrease_bid":
                adjustment = -abs(adjustment)
            await connector.update_campaign_budget(campaign.campaign_id, adjustment)
